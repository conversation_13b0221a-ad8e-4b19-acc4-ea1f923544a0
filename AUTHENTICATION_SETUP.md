# Authentication Setup Guide

This guide will help you set up authentication for the Alpha Gas Solution application using Supabase.

## Step 1: Update Database Schema

Run the following SQL in your Supabase SQL Editor to add authentication support:

```sql
-- Migration to add authentication support
-- This will update the gas_analytes table to use user_id instead of user_session

-- First, add the user_id column
ALTER TABLE gas_analytes ADD COLUMN user_id UUID REFERENCES auth.users(id);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_gas_analytes_user_id ON gas_analytes(user_id);

-- Clear existing session-based data (recommended since it was shared)
DELETE FROM gas_analytes WHERE user_session IS NOT NULL;

-- Drop the old user_session column
ALTER TABLE gas_analytes DROP COLUMN user_session;

-- Enable Row Level Security (RLS) for user data isolation
ALTER TABLE gas_analytes ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only access their own data
CREATE POLICY "Users can access own analytes" ON gas_analytes
FOR ALL USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT ALL ON gas_analytes TO authenticated;
GRANT USAGE ON SEQUENCE gas_analytes_id_seq TO authenticated;
```

## Step 2: Enable Google OAuth (Optional)

1. Go to your Supabase Dashboard
2. Navigate to **Authentication > Providers**
3. Enable **Google** provider
4. Add your Google OAuth credentials:
   - **Client ID**: Get from Google Cloud Console
   - **Client Secret**: Get from Google Cloud Console
   - **Redirect URL**: Will be automatically set by Supabase

### Getting Google OAuth Credentials:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Go to **Credentials > Create Credentials > OAuth 2.0 Client ID**
5. Set **Application type** to "Web application"
6. Add your Supabase redirect URL to **Authorized redirect URIs**:
   - Format: `https://[your-project].supabase.co/auth/v1/callback`
7. Copy the **Client ID** and **Client Secret** to Supabase

## Step 3: Configure Email Settings (Recommended)

1. In Supabase Dashboard, go to **Authentication > Settings**
2. Configure **SMTP Settings** for email verification
3. Or use Supabase's default email service for testing

## Step 4: Test the Authentication

1. Deploy your application
2. Visit the login page: `https://your-app.vercel.app/login.html`
3. Try signing up with email/password
4. Try signing in with Google (if configured)
5. Verify that data is now user-specific

## Step 5: Security Features

The new authentication system provides:

✅ **User Isolation**: Each user sees only their own data
✅ **Row Level Security**: Database-level data protection
✅ **Email Verification**: Secure account creation
✅ **Password Reset**: Self-service password recovery
✅ **OAuth Support**: Easy sign-in with Google
✅ **Session Management**: Secure authentication state

## Troubleshooting

### Common Issues:

1. **"User not authenticated" errors**:
   - Check if user is properly signed in
   - Verify Supabase auth configuration

2. **Google OAuth not working**:
   - Verify Google Cloud Console settings
   - Check redirect URLs match exactly
   - Ensure Google+ API is enabled

3. **Email verification issues**:
   - Configure SMTP settings in Supabase
   - Check spam folder for verification emails

4. **Database permission errors**:
   - Ensure RLS policies are correctly applied
   - Verify user_id column exists and is properly referenced

## Files Modified for Authentication:

- `auth.js` - Authentication management
- `login.html` - Login/signup interface
- `index.html` - Protected main application with auth check
- `supabase-client.js` - Updated to use user_id instead of session_id
- `storage.js` - Authentication-aware data persistence
- `supabase-auth-migration.sql` - Database schema update

## Next Steps:

1. Apply the database migration
2. Configure OAuth providers (optional)
3. Test authentication flow
4. Deploy and verify data isolation works correctly

Each user will now have their own isolated data set, solving the issue where analytes weren't persisting due to shared sessions.
