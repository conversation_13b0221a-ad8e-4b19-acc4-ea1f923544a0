<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Customizable Analysis Range & AI Calibration Check</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
            padding-bottom: 200px; 
        }
        .chart-container, .customization-form-container, .calibration-analyzer-container {
            width: 95%;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .gas-row {
            display: flex;
            align-items: flex-start; 
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .gas-row:last-child {
            border-bottom: none;
        }
        .gas-name {
            width: 180px;
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
            padding-right: 10px;
            flex-shrink: 0;
            margin-top: 10px; 
        }
        .bar-area {
            flex-grow: 1;
            height: 40px;
            position: relative;
            background-color: #e9ecef;
            border-radius: 6px;
            overflow: visible; 
        }
        .bar {
            position: absolute;
            height: 60%;
            top: 20%;
            border-radius: 4px;
            opacity: 0.8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: white;
            white-space: nowrap;
            overflow: visible; 
        }
        .current-bar {
            background-color: #4299e1; 
            z-index: 3; 
        }
        .target-bar {
            background-color: #a0aec0; 
            height: 100%;
            top: 0;
            opacity: 0.5;
            z-index: 2; 
        }
        .gap-notes-section {
            width: 250px;
            padding-left: 15px;
            flex-shrink: 0;
        }
        .gap-notes-text {
            font-size: 0.75rem;
            color: #4a5568;
            line-height: 1.3;
            white-space: pre-wrap;
        }
        .scale-label {
            font-size: 0.7rem;
            color: #718096;
            position: absolute;
            bottom: -18px;
            transform: translateX(-50%);
        }
        .legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f7fafc;
            border-radius: 8px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 0.8rem;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border-radius: 3px;
        }
        .header-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 5px;
            text-align: center;
        }
        .header-subtitle {
            font-size: 1rem;
            color: #4a5568;
            margin-bottom: 25px;
            text-align: center;
        }

        .tooltip {
            position: absolute;
            background-color: #2d3748; 
            color: white;
            padding: 8px 12px; 
            border-radius: 6px; 
            font-size: 0.8rem; 
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
            z-index: 100; 
            pointer-events: none;
            min-width: 150px; 
            max-width: 300px; 
            white-space: normal; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            bottom: 110%; 
            left: 50%;
            transform: translateX(-50%);
        }

        .bar:hover .tooltip {
            visibility: visible;
            opacity: 1;
        }
        
        .form-section, .analyzer-section {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        .form-section:last-child, .analyzer-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .form-title, .analyzer-title { 
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .form-input, .form-textarea, .analyzer-input, .analyzer-select { /* Consolidated input styles */
            width: 100%;
            padding: 10px;
            border: 1px solid #cbd5e0;
            border-radius: 6px;
            font-size: 0.9rem;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        .analyzer-input.concentration {
             width: calc(100% - 80px); /* Adjust width for unit selector */
             margin-right: 10px;
        }
        .analyzer-select {
            width: 70px; /* Width for unit selector */
        }
         .compound-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .compound-row .form-input { /* Use .form-input for consistency */
            margin-bottom: 0; /* Remove bottom margin for inputs in a row */
        }
        .compound-row .remove-compound-btn {
            background-color: #ef4444; /* red-500 */
            color: white;
            padding: 6px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .compound-row .remove-compound-btn:hover {
            background-color: #dc2626; /* red-600 */
        }

        .form-label {
            display: block;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 5px;
            color: #4a5568;
        }
        .range-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        .range-input-group input[type="number"] {
            width: 100px;
        }
        .range-input-group input[type="text"] {
            flex-grow: 1;
        }
        .add-range-btn, .analyzer-btn, .add-compound-btn { 
            background-color: #d1d5db; 
            color: #1f2937; 
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 0.9rem; 
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
            display: inline-block; 
            margin-bottom:10px; /* Add some margin for standalone buttons */
        }
        .add-range-btn:hover, .analyzer-btn:hover, .add-compound-btn:hover {
            background-color: #9ca3af; 
        }
        .analyzer-btn { /* This is the "Analyze Calibration Standards" button */
             background-color: #2563eb; 
             color: white;
             margin-top: 10px; /* Add margin top to separate from last compound row or Add Compound button */
        }
        .analyzer-btn:hover {
            background-color: #1d4ed8; 
        }
        .submit-btn {
            background-color: #3b82f6; 
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
            display: block;
            width: 100%;
            margin-top: 10px;
        }
        .submit-btn:hover {
            background-color: #2563eb; 
        }
        .dynamic-ranges-container .range-input-group:not(:last-child) {
            border-bottom: 1px dashed #e2e8f0;
            padding-bottom: 10px;
        }
        .error-message {
            color: red;
            font-size: 0.8rem;
            margin-top: 5px;
            margin-bottom: 10px;
        }
        #calibrationResults div {
            border: 1px solid #e2e8f0;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 6px;
            background-color: #f9fafb;
        }
        #calibrationResults h4 {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        #calibrationResults p {
            font-size: 0.85rem;
            margin-bottom: 3px;
        }
        #calibrationResults .can-analyze {
            color: #16a34a; /* Green */
            font-weight: bold;
        }
        #calibrationResults .cannot-analyze {
            color: #dc2626; /* Red */
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <h1 class="header-title">Alpha Gas Solution</h1>
        <p class="header-subtitle">Analysis Range Visualization & AI Calibration Check</p>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4299e1;"></div>
                <span>Current Capability</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #a0aec0; opacity: 0.5;"></div>
                <span>Target Range (All-Rounder)</span>
            </div>
        </div>

        <div style="position: relative; height: 20px; margin-bottom: 25px; border-bottom: 1px solid #cbd5e0;">
            <!-- Scale labels will be dynamically added here by JavaScript -->
        </div>

        <div id="chart">
            <!-- Gas rows will be dynamically added here -->
        </div>
    </div>

    <div class="customization-form-container">
        <h2 class="form-title">Add New Analyte to Chart</h2>
        <form id="addAnalyteForm">
            <div class="form-section">
                <label for="analyteName" class="form-label">Analyte Name:</label>
                <input type="text" id="analyteName" class="form-input" required>
                <div id="analyteNameError" class="error-message" style="display: none;"></div>
            </div>

            <div class="form-section">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Current Capabilities</h3>
                <div id="currentRangesContainer" class="dynamic-ranges-container">
                    <!-- Current range inputs will be added here -->
                </div>
                <button type="button" class="add-range-btn" onclick="addRangeInput('current')">Add Current Range</button>
            </div>

            <div class="form-section">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Target Ranges</h3>
                <div id="targetRangesContainer" class="dynamic-ranges-container">
                    <!-- Target range inputs will be added here -->
                </div>
                <button type="button" class="add-range-btn" onclick="addRangeInput('target')">Add Target Range</button>
            </div>
            
            <div class="form-section">
                <label for="gapNotes" class="form-label">Gap Notes / Remarks:</label>
                <textarea id="gapNotes" class="form-textarea"></textarea>
            </div>

            <button type="submit" class="submit-btn">Add Analyte to Chart</button>
        </form>
    </div>

    <!-- New AI Calibration Gas Analyzer Section -->
    <div class="calibration-analyzer-container">
        <h2 class="analyzer-title">AI Calibration Gas Capability Analysis</h2>
        <div class="analyzer-section">
            <div>
                <label for="calibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                <input type="text" id="calibrationGasStandardName" class="form-input" placeholder="e.g., Daily Check Mix">
            </div>
            
            <h3 class="text-lg font-medium text-gray-700 mt-4 mb-2">Components:</h3>
            <div id="calibrationCompoundsContainer">
                <!-- Compound input rows will be added here -->
            </div>
            <button type="button" id="addCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
            <div id="calibrationInputError" class="error-message" style="display: none;"></div>
            <button type="button" id="analyzeCalibrationBtn" class="analyzer-btn">🔬 Analyze Calibration Standard</button>
        </div>
        <div id="calibrationLoading" class="text-sm text-gray-600 my-2" style="display: none;">Analyzing, please wait...</div>
        <div id="calibrationResults">
            <!-- AI analysis results will be displayed here -->
        </div>
    </div>

    <script>
        // --- Configuration ---
        const minScalePPM = 0.001; 
        const maxScalePPM = 1000000; 

        // --- Initial Gas Data (Your Current Capabilities) ---
        let gasData = [
             {
                name: "Hydrocarbons (Total/Generic)",
                current: [{ min: 1, max: 100, label: "1-100 ppm (GC1-FID, unvalidated)" }],
                target: [{ min: 0.01, max: 1000000, label: "0.01 ppm - 100%" }],
                gapNotes: "Speciation needed (VOCs). Current FID unvalidated. Broader range for pure components & trace analysis."
            },
            {
                name: "SO2",
                current: [
                    { min: 1, max: 50, label: "<50 ppm (GC2-FPD, saturated at 50ppm)" },
                    { min: 1000, max: 50000, label: "0.1% - 5% (GC1-TCD)" }
                ],
                target: [{ min: 1, max: 500000, label: "1 ppm - 50%" }],
                gapNotes: "Gap between FPD saturation (~50ppm) and TCD lower limit (1000ppm). Lower detection for CEMS."
            },
            {
                name: "H2S",
                current: [
                    { min: 1, max: 30, label: "<30 ppm (GC2-FPD, saturated at 30ppm)" },
                    { min: 500, max: 60000, label: "0.05% - >6% (GC1-TCD)" }
                ],
                target: [{ min: 1, max: 200000, label: "1 ppm - 20%" }],
                gapNotes: "Gap between FPD saturation (~30ppm) and TCD lower limit (500ppm)."
            },
            { name: "O2", current: [ { min: 1000, max: 980000, label: "0.1% - 98% (GC1-TCD)"} ], target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], gapNotes: "Need ppb/low ppm for UHP." },
            { name: "CH4", current: [ { min: 8, max: 30000, label: "8ppm - 3% (GC1-FID)" }, { min: 30000, max: 834000, label: "3% - 83.4% (GC1-TCD)" } ], target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], gapNotes: "Lower detection for trace." },
            { name: "CO2", current: [ { min: 5000, max: 150000, label: "≥0.5% (Teledyne, max std 15%)" } ], target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], gapNotes: "GC1-TCD not operational." },
            { name: "CO", current: [ { min: 5000, max: 100000, label: "≥0.5% (Teledyne, max std 10%)" } ], target: [{ min: 1, max: 100000, label: "1 ppm - 10%" }], gapNotes: "GC1-TCD not operational." },
            { name: "N2O", current: [ { min: 5000, max: 500000, label: "≥0.5% (Teledyne, Entonox)" } ], target: [{ min: 0.1, max: 500000, label: "0.1 ppm - 50%" }], gapNotes: "Need lower for CEMS." },
            { name: "N2", current: [], target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], gapNotes: "Teledyne N2 overlap issues." },
            { name: "NO", current: [{ min: 1, max: 5000, label: "0-5000 ppm (Teledyne)" }], target: [{ min: 1, max: 10000, label: "1 ppm - 1%" }], gapNotes: "Good for CEMS." },
            { name: "NO2", current: [{ min: 1, max: 5000, label: "0-5000 ppm (Teledyne)" }], target: [{ min: 1, max: 500, label: "1 ppm - 500 ppm" }], gapNotes: "Good for CEMS." },
            { name: "H2", current: [ ], target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], gapNotes: "Major gap." },
            { name: "Argon (Ar)", current: [], target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], gapNotes: "Critical, N2 overlap." },
            { name: "VOCs (Speciated)", current: [], target: [{ min: 0.001, max: 100000, label: "1 ppb - Various %" }], gapNotes: "Requires GC-MS." },
        ];

        // --- Helper Functions ---
        function logPosition(valuePPM) {
            if (valuePPM === null || valuePPM <= 0) valuePPM = minScalePPM;
            const minLog = Math.log10(minScalePPM);
            const maxLog = Math.log10(maxScalePPM);
            const valLog = Math.log10(valuePPM);
            return ((valLog - minLog) / (maxLog - minLog)) * 100;
        }

        function formatLabel(ppm) {
            if (ppm === null) return "";
            if (ppm < 0.000001) return (ppm * 1000000000).toFixed(3) + " ppt";
            if (ppm < 1) return (ppm * 1000).toFixed(3) + " ppb";
            if (ppm >= 1000000) return (ppm / 10000).toFixed(1) + "%"; 
            if (ppm >= 1000) return (ppm / 10000).toFixed(2) + "%"; 
            return ppm.toFixed(1) + " ppm";
        }

        // --- Chart Rendering ---
        const chartElement = document.getElementById('chart');
        const scaleAxisElement = document.querySelector('.chart-container div[style*="border-bottom: 1px solid #cbd5e0;"]');

        function drawScaleLabels() {
            if (!scaleAxisElement) return;
            scaleAxisElement.innerHTML = ''; 
            const scalePoints = [
                { value: 0.001, label: "1 ppb" }, { value: 0.01, label: "10 ppb" }, { value: 0.1, label: "0.1 ppm" },
                { value: 1, label: "1 ppm" }, { value: 10, label: "10 ppm" }, { value: 100, label: "100 ppm" },
                { value: 1000, label: "0.1%" }, { value: 10000, label: "1%" }, { value: 100000, label: "10%" },
                { value: 1000000, label: "100%" }
            ];
            scalePoints.forEach(point => {
                const posPercent = logPosition(point.value);
                if (posPercent >=0 && posPercent <=100) {
                    const labelDiv = document.createElement('div');
                    labelDiv.classList.add('scale-label');
                    labelDiv.style.left = `${posPercent}%`;
                    labelDiv.textContent = point.label;
                    scaleAxisElement.appendChild(labelDiv);

                    const tickDiv = document.createElement('div');
                    tickDiv.style.position = 'absolute';
                    tickDiv.style.left = `${posPercent}%`;
                    tickDiv.style.bottom = '0px';
                    tickDiv.style.width = '1px';
                    tickDiv.style.height = '5px';
                    tickDiv.style.backgroundColor = '#a0aec0';
                    scaleAxisElement.appendChild(tickDiv);
                }
            });
        }
        
        function renderChart() {
            chartElement.innerHTML = ''; 
            gasData.forEach(gas => {
                const row = document.createElement('div');
                row.classList.add('gas-row');

                const nameDiv = document.createElement('div');
                nameDiv.classList.add('gas-name');
                nameDiv.textContent = gas.name;
                row.appendChild(nameDiv);

                const barArea = document.createElement('div');
                barArea.classList.add('bar-area');

                if (gas.target && gas.target.length > 0) {
                    gas.target.forEach(range => {
                        const targetMinPos = logPosition(range.min === null ? minScalePPM : parseFloat(range.min));
                        const targetMaxPos = logPosition(range.max === null ? maxScalePPM : parseFloat(range.max));
                        if (targetMinPos < targetMaxPos && targetMaxPos <= 100 && targetMinPos >= 0) {
                            const targetBar = document.createElement('div');
                            targetBar.classList.add('bar', 'target-bar');
                            targetBar.style.left = `${targetMinPos}%`;
                            targetBar.style.width = `${targetMaxPos - targetMinPos}%`;
                            const tooltip = document.createElement('span');
                            tooltip.classList.add('tooltip');
                            tooltip.textContent = `Target: ${range.label || (formatLabel(parseFloat(range.min)) + " - " + formatLabel(parseFloat(range.max)))}`;
                            targetBar.appendChild(tooltip);
                            barArea.appendChild(targetBar);
                        }
                    });
                }

                if (gas.current && gas.current.length > 0) {
                    gas.current.forEach(range => {
                        const currentMinPos = logPosition(range.min === null ? minScalePPM : parseFloat(range.min));
                        const currentMaxPos = logPosition(range.max === null ? maxScalePPM : parseFloat(range.max));
                        if (currentMinPos < currentMaxPos && currentMaxPos <= 100 && currentMinPos >= 0) {
                            const currentBar = document.createElement('div');
                            currentBar.classList.add('bar', 'current-bar');
                            currentBar.style.left = `${currentMinPos}%`;
                            currentBar.style.width = `${currentMaxPos - currentMinPos}%`;
                            const tooltip = document.createElement('span');
                            tooltip.classList.add('tooltip');
                            tooltip.textContent = `Current: ${range.label || (formatLabel(parseFloat(range.min)) + " - " + formatLabel(parseFloat(range.max)))}`;
                            currentBar.appendChild(tooltip);
                            barArea.appendChild(currentBar);
                        }
                    });
                }
                row.appendChild(barArea);

                const notesSection = document.createElement('div'); 
                notesSection.classList.add('gap-notes-section'); 

                const notesText = document.createElement('div'); 
                notesText.classList.add('gap-notes-text');
                notesText.textContent = gas.gapNotes || "No notes provided.";
                notesSection.appendChild(notesText); 

                row.appendChild(notesSection); 

                chartElement.appendChild(row);
            });
        }

        // --- Form Handling for Customization ---
        function addRangeInput(type) {
            const containerId = type === 'current' ? 'currentRangesContainer' : 'targetRangesContainer';
            const container = document.getElementById(containerId);
            const group = document.createElement('div');
            group.classList.add('range-input-group');
            group.innerHTML = `
                <input type="number" step="any" placeholder="Min (ppm)" class="form-input range-min">
                <input type="number" step="any" placeholder="Max (ppm)" class="form-input range-max">
                <input type="text" placeholder="Label (optional)" class="form-input range-label">
                <button type="button" class="text-red-500 hover:text-red-700 text-sm" onclick="this.parentElement.remove()">Remove</button>
            `;
            container.appendChild(group);
        }
        
        function showFormError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                setTimeout(() => {
                    errorElement.style.display = 'none';
                    errorElement.textContent = '';
                }, 3000);
            }
        }

        document.getElementById('addAnalyteForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const analyteNameInput = document.getElementById('analyteName');
            const analyteName = analyteNameInput.value.trim();
            const gapNotes = document.getElementById('gapNotes').value;
            
            if (!analyteName) {
                showFormError('analyteNameError', "Please enter an analyte name.");
                analyteNameInput.focus();
                return;
            }
            const currentRanges = [];
            document.querySelectorAll('#currentRangesContainer .range-input-group').forEach(group => {
                const min = group.querySelector('.range-min').value;
                const max = group.querySelector('.range-max').value;
                const label = group.querySelector('.range-label').value;
                if (min && max) { 
                    currentRanges.push({ min: parseFloat(min) || null, max: parseFloat(max) || null, label: label || "" });
                }
            });
            const targetRanges = [];
            document.querySelectorAll('#targetRangesContainer .range-input-group').forEach(group => {
                const min = group.querySelector('.range-min').value;
                const max = group.querySelector('.range-max').value;
                const label = group.querySelector('.range-label').value;
                 if (min && max) { 
                    targetRanges.push({ min: parseFloat(min) || null, max: parseFloat(max) || null, label: label || "" });
                }
            });
            gasData.push({ name: analyteName, current: currentRanges, target: targetRanges, gapNotes: gapNotes });
            renderChart(); 
            this.reset(); 
            document.getElementById('currentRangesContainer').innerHTML = ''; 
            document.getElementById('targetRangesContainer').innerHTML = ''; 
            addRangeInput('current'); 
            addRangeInput('target'); 
        });

        // --- New AI Calibration Gas Analyzer ---
        const calibrationGasStandardNameInput = document.getElementById('calibrationGasStandardName');
        const addCalibrationCompoundBtn = document.getElementById('addCalibrationCompoundBtn');
        const calibrationCompoundsContainer = document.getElementById('calibrationCompoundsContainer');
        const analyzeCalibrationBtn = document.getElementById('analyzeCalibrationBtn');
        const calibrationResultsDiv = document.getElementById('calibrationResults');
        const calibrationLoadingDiv = document.getElementById('calibrationLoading');
        const calibrationInputErrorDiv = document.getElementById('calibrationInputError');

        function addCalibrationCompoundRow() {
            const rowDiv = document.createElement('div');
            rowDiv.classList.add('compound-row', 'items-center'); // Added items-center for vertical alignment

            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = 'Compound Name';
            nameInput.classList.add('form-input', 'flex-grow'); // flex-grow to take available space

            const concInput = document.createElement('input');
            concInput.type = 'number';
            concInput.step = 'any';
            concInput.placeholder = 'Concentration';
            concInput.classList.add('form-input', 'w-32'); // Fixed width for concentration

            const unitSelect = document.createElement('select');
            unitSelect.classList.add('analyzer-select', 'form-input'); // Use form-input for consistent styling
            const ppmOption = document.createElement('option');
            ppmOption.value = 'ppm';
            ppmOption.textContent = 'PPM';
            const percentOption = document.createElement('option');
            percentOption.value = 'percent';
            percentOption.textContent = '%';
            unitSelect.appendChild(ppmOption);
            unitSelect.appendChild(percentOption);

            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.textContent = 'Remove';
            removeBtn.classList.add('remove-compound-btn');
            removeBtn.onclick = () => rowDiv.remove();

            rowDiv.appendChild(nameInput);
            rowDiv.appendChild(concInput);
            rowDiv.appendChild(unitSelect);
            rowDiv.appendChild(removeBtn);
            calibrationCompoundsContainer.appendChild(rowDiv);
        }

        addCalibrationCompoundBtn.addEventListener('click', addCalibrationCompoundRow);
        
        analyzeCalibrationBtn.addEventListener('click', async () => {
            calibrationResultsDiv.innerHTML = '';
            calibrationInputErrorDiv.style.display = 'none';

            const standardName = calibrationGasStandardNameInput.value.trim();
            if (!standardName) {
                calibrationInputErrorDiv.textContent = "Please enter a name for the calibration gas standard.";
                calibrationInputErrorDiv.style.display = 'block';
                calibrationGasStandardNameInput.focus();
                return;
            }

            const components = [];
            const compoundRows = calibrationCompoundsContainer.querySelectorAll('.compound-row');
            if (compoundRows.length === 0) {
                calibrationInputErrorDiv.textContent = "Please add at least one compound to the calibration standard.";
                calibrationInputErrorDiv.style.display = 'block';
                return;
            }

            let isValid = true;
            compoundRows.forEach(row => {
                const compoundName = row.children[0].value.trim();
                const concentrationStr = row.children[1].value.trim();
                const unit = row.children[2].value;

                if (!compoundName || !concentrationStr) {
                    isValid = false;
                    return; // breakout of forEach for this row, isValid will be caught later
                }
                
                let concentrationPPM = parseFloat(concentrationStr);
                if (isNaN(concentrationPPM)) {
                    isValid = false;
                    return;
                }

                if (unit === 'percent') {
                    concentrationPPM *= 10000; // 1% = 10,000 PPM
                }
                components.push({ compoundName, concentrationPPM });
            });

            if (!isValid) {
                calibrationInputErrorDiv.textContent = "Please ensure all compound names and concentrations are filled correctly.";
                calibrationInputErrorDiv.style.display = 'block';
                return;
            }
            
            const calibrationGases = [{ // AI expects an array of calibration gases
                calibrationGasName: standardName,
                components: components
            }];

            calibrationLoadingDiv.style.display = 'block';
            analyzeCalibrationBtn.disabled = true;
            analyzeCalibrationBtn.textContent = 'Analyzing...';

            const prompt = `You are an expert analytical chemist. Your task is to determine if a laboratory can analyze a series of calibration gas mixtures based on their current analytical capabilities.

Current Analytical Capabilities (gasData):
${JSON.stringify(gasData, null, 2)}

Calibration Gas Mixtures to Analyze:
${JSON.stringify(calibrationGases, null, 2)}

For each calibration gas mixture provided, please perform the following analysis:
1. Identify each component and its concentration (in PPM) in the mixture.
2. For each component, check against the 'Current Analytical Capabilities (gasData)':
    a. Does an analyte with a matching 'name' exist in gasData? (Perform a case-insensitive and trim-space comparison for names if possible, but primarily rely on exact match from the provided 'gasData' names).
    b. If it exists, does the component's 'concentrationPPM' fall within any of the 'current' min/max ranges specified for that analyte in gasData? Remember that 'min' and 'max' in gasData are also in PPM. A component is considered analyzable if its concentration falls within *any* of the defined current ranges for that analyte.
3. Based on the above, determine if ALL components in the calibration gas mixture can be fully analyzed with the current capabilities.
4. If a component cannot be analyzed, state the reason (e.g., "Compound not in capabilities list", "Concentration X ppm is outside current detectable ranges [A-B ppm, C-D ppm]", "No current capability ranges defined for this compound").

Provide your response as a JSON array, where each object in the array corresponds to one calibration gas from the input. Each object must follow this exact schema:
{
  "calibrationGasName": "string (name of the calibration gas)",
  "canAnalyzeFully": "boolean (true if all components can be analyzed, false otherwise)",
  "analysisDetails": "string (a brief summary of the findings for this gas, including reasons if it cannot be fully analyzed)",
  "problematicComponents": [ 
    {
      "compoundName": "string",
      "concentrationPPM": "number",
      "reason": "string (e.g., 'Not in current capabilities list', 'Concentration X ppm is outside detectable ranges [A-B ppm, C-D ppm]', 'No current ranges defined')"
    }
  ]
}
If 'canAnalyzeFully' is true, 'problematicComponents' should be an empty array.
Ensure your output is ONLY the JSON array, with no other text before or after it.
`;

            try {
                let chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
                const payload = {
                    contents: chatHistory,
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "ARRAY",
                            items: {
                                type: "OBJECT",
                                properties: {
                                    "calibrationGasName": { "type": "STRING" },
                                    "canAnalyzeFully": { "type": "BOOLEAN" },
                                    "analysisDetails": { "type": "STRING" },
                                    "problematicComponents": {
                                        "type": "ARRAY",
                                        "items": {
                                            "type": "OBJECT",
                                            "properties": {
                                                "compoundName": { "type": "STRING" },
                                                "concentrationPPM": { "type": "NUMBER" },
                                                "reason": { "type": "STRING" }
                                            },
                                            required: ["compoundName", "concentrationPPM", "reason"]
                                        }
                                    }
                                },
                                required: ["calibrationGasName", "canAnalyzeFully", "analysisDetails", "problematicComponents"]
                            }
                        }
                    }
                };
                const apiKey = "AIzaSyBtZCW7y39Vs5jM6BxHnkV8maU5qZ6IV6A"; // Provided by Canvas environment
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
        
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    const errorBody = await response.text();
                    throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
                }
        
                const result = await response.json();
        
                if (result.candidates && result.candidates[0]?.content?.parts[0]?.text) {
                    const aiJsonText = result.candidates[0].content.parts[0].text;
                    try {
                        const analysisResults = JSON.parse(aiJsonText);
                        displayCalibrationResults(analysisResults);
                    } catch (parseError) {
                         console.error("Error parsing AI JSON response:", parseError, "Raw AI response:", aiJsonText);
                         calibrationResultsDiv.innerHTML = `<p class="text-red-500">Error: Could not parse the AI's response. Please check console for details. Raw output: ${aiJsonText}</p>`;
                    }
                } else {
                    console.error("Gemini API response structure unexpected or content missing:", result);
                    calibrationResultsDiv.innerHTML = `<p class="text-red-500">Error: Could not retrieve analysis from AI. Unexpected response structure.</p>`;
                }
            } catch (error) {
                console.error('Error fetching AI calibration analysis:', error);
                calibrationResultsDiv.innerHTML = `<p class="text-red-500">Error fetching AI analysis: ${error.message}</p>`;
            } finally {
                calibrationLoadingDiv.style.display = 'none';
                analyzeCalibrationBtn.disabled = false;
                analyzeCalibrationBtn.textContent = '🔬 Analyze Calibration Standard';
            }
        });

        function displayCalibrationResults(results) {
            calibrationResultsDiv.innerHTML = ''; 
            if (!Array.isArray(results)) {
                calibrationResultsDiv.innerHTML = `<p class="text-red-500">Error: AI response was not in the expected array format.</p>`;
                return;
            }

            results.forEach(gasAnalysis => {
                const resultDiv = document.createElement('div');
                let content = `<h4>${gasAnalysis.calibrationGasName || 'Unnamed Gas'}</h4>`;
                content += `<p><strong>Overall:</strong> <span class="${gasAnalysis.canAnalyzeFully ? 'can-analyze' : 'cannot-analyze'}">${gasAnalysis.canAnalyzeFully ? 'CAN ANALYZE FULLY' : 'CANNOT ANALYZE FULLY'}</span></p>`;
                content += `<p><strong>Details:</strong> ${gasAnalysis.analysisDetails || 'No details provided.'}</p>`;

                if (gasAnalysis.problematicComponents && gasAnalysis.problematicComponents.length > 0) {
                    content += `<p><strong>Problematic Components:</strong></p><ul>`;
                    gasAnalysis.problematicComponents.forEach(prob => {
                        content += `<li>- ${prob.compoundName} (${prob.concentrationPPM} ppm): ${prob.reason}</li>`;
                    });
                    content += `</ul>`;
                }
                resultDiv.innerHTML = content;
                calibrationResultsDiv.appendChild(resultDiv);
            });
        }


        // Initial setup
        drawScaleLabels();
        renderChart();
        addRangeInput('current'); 
        addRangeInput('target'); 
        addCalibrationCompoundRow(); // Add one compound row by default for the new analyzer

    </script>
</body>
</html>
