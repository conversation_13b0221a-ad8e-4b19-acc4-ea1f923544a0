# Deployment Guide for Alpha Gas Solution

## 🚀 Quick Deployment Options

### 1. Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variable
vercel env add GEMINI_API_KEY
```

### 2. Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up

# Set environment variable in Railway dashboard
```

### 3. Heroku
```bash
# Install Heroku CLI and login
heroku create your-app-name
git push heroku main

# Set environment variable
heroku config:set GEMINI_API_KEY=your_api_key_here
```

### 4. Netlify (with Functions)
1. Create `netlify/functions/analyze-calibration.js`
2. Move API logic there
3. Deploy via Netlify dashboard or CLI

### 5. DigitalOcean App Platform
1. Connect your GitHub repository
2. Set environment variable in dashboard
3. Deploy automatically

## 🔧 Environment Variables Required

For ALL platforms, you need to set:
```
GEMINI_API_KEY=your_actual_api_key_here
PORT=3000
```

## 📁 Files to Deploy

Include these files in your deployment:
- `index.html`
- `styles.css`
- `*.js` files
- `package.json`
- `server.js`

**DO NOT DEPLOY:**
- `.env` (local only)
- `node_modules/` (auto-installed)
- `test.sh`
- `*.backup` files

## ✅ Pre-deployment Checklist

- [ ] Test locally with `npm start`
- [ ] Run `./test.sh` to verify setup
- [ ] Ensure `.env` is in `.gitignore`
- [ ] Update README with your deployment URL
- [ ] Set environment variables on hosting platform
- [ ] Test the deployed application

## 🔒 Security Best Practices

1. **Never commit `.env` files**
2. **Use HTTPS in production**
3. **Set environment variables on hosting platform**
4. **Monitor API usage and costs**
5. **Implement rate limiting if needed**

## 🐛 Troubleshooting

### Common Issues:

**API Key Not Working:**
- Verify key is set correctly in hosting platform
- Check Google Cloud Console for API quotas
- Ensure key has Gemini API access

**Server Not Starting:**
- Check Node.js version (14+ required)
- Verify all dependencies are installed
- Check port availability

**CORS Issues:**
- Ensure server.js includes CORS middleware
- Check if hosting platform modifies headers
