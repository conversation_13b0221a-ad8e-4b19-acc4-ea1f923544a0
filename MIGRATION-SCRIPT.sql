-- 🗃️ CRITICAL: Run this in your Supabase SQL Editor FIRST
-- This migration enables user-specific data isolation

-- Step 1: Add user_id column to link records to authenticated users
ALTER TABLE gas_analytes ADD COLUMN user_id UUID REFERENCES auth.users(id);

-- Step 2: Create database index for better performance
CREATE INDEX IF NOT EXISTS idx_gas_analytes_user_id ON gas_analytes(user_id);

-- Step 3: Clear existing session-based data (RECOMMENDED)
-- This removes the shared data that was causing conflicts
DELETE FROM gas_analytes WHERE user_session IS NOT NULL;

-- Step 4: Remove the old session column
ALTER TABLE gas_analytes DROP COLUMN user_session;

-- Step 5: Enable Row Level Security (RLS) - This is CRITICAL for data isolation
ALTER TABLE gas_analytes ENABLE ROW LEVEL SECURITY;

-- Step 6: Create policy so users can only see their own data
CREATE POLICY "Users can access own analytes" ON gas_analytes
FOR ALL USING (auth.uid() = user_id);

-- Step 7: Grant necessary permissions to authenticated users
GRANT ALL ON gas_analytes TO authenticated;
GRANT USAGE ON SEQUENCE gas_analytes_id_seq TO authenticated;

-- 🎯 VERIFICATION QUERIES (run these to confirm setup):
-- Check if user_id column exists:
-- SELECT column_name FROM information_schema.columns WHERE table_name = 'gas_analytes' AND column_name = 'user_id';

-- Check if RLS is enabled:
-- SELECT relname, relrowsecurity FROM pg_class WHERE relname = 'gas_analytes';

-- Check if policy exists:
-- SELECT policyname FROM pg_policies WHERE tablename = 'gas_analytes';
