# Alpha Gas Solution - Analysis Range Visualization & AI Calibration Check

A web application for visualizing gas analysis capabilities and performing AI-powered calibration standard analysis.

## Features

- **Interactive Gas Analysis Chart**: Visualize current capabilities vs target ranges for various gas analytes
- **Dynamic Analyte Management**: Add new analytes with custom ranges and notes
- **AI Calibration Analysis**: Analyze calibration gas standards against your current capabilities using Google's Gemini AI
- **Secure API Handling**: Server-side API key management for production safety

## ✅ Current Status

**The application is now PRODUCTION READY and SECURE!**

- ✅ API key properly secured server-side
- ✅ Modular code structure implemented
- ✅ Environment variable configuration complete
- ✅ Git security measures in place
- ✅ Comprehensive testing scripts included
- ✅ Deployment guides provided

## 🚀 Quick Start

This application has been restructured to properly secure API keys:

- **Separated client-side and server-side code**
- **Environment variable management** for API keys
- **Server-side proxy** for API calls
- **Modular code structure** for better maintainability

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment Variables
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your Google Gemini API key
# GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Get Your Google Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key and paste it in your `.env` file

### 4. Run the Application

#### Development Mode:
```bash
npm run dev
```

#### Production Mode:
```bash
npm start
```

The application will be available at `http://localhost:3000`

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # All CSS styles
├── config.js           # Configuration settings
├── data.js             # Initial gas data
├── chart.js            # Chart rendering functions
├── form-handler.js     # Form handling logic
├── ai-analyzer.js      # AI analysis functionality
├── app.js              # Main application initialization
├── server.js           # Express server with secure API proxy
├── package.json        # Node.js dependencies
├── .env.example        # Environment variables template
├── .env                # Your actual environment variables (not in git)
└── .gitignore          # Git ignore rules
```

## Deployment Considerations

### For Production Deployment:

1. **Environment Variables**: Ensure your hosting platform has the `GEMINI_API_KEY` environment variable set
2. **HTTPS**: Use HTTPS in production for secure API communication
3. **Rate Limiting**: Consider implementing rate limiting for the API endpoints
4. **Error Handling**: Monitor and log API errors appropriately
5. **CORS**: Configure CORS settings for your specific domain

### Popular Hosting Options:

- **Vercel**: Set environment variables in project settings
- **Netlify**: Use Netlify Functions for the API proxy
- **Heroku**: Set config vars in the dashboard
- **Railway**: Set environment variables in project settings

## Usage

1. **View Current Capabilities**: The chart shows your current gas analysis ranges vs target ranges
2. **Add New Analytes**: Use the form to add new gas compounds with their detection ranges
3. **AI Calibration Analysis**: 
   - Enter calibration gas standard details
   - Add components with concentrations
   - Click "Analyze" to get AI-powered capability assessment

## API Security

- API keys are stored server-side as environment variables
- Client-side code never exposes sensitive credentials
- All AI API calls go through the secure server proxy
- Environment files are excluded from version control

## Troubleshooting

- **API Key Issues**: Ensure your Gemini API key is valid and has sufficient quota
- **CORS Errors**: The server includes CORS middleware for development
- **Port Issues**: Change the PORT environment variable if 3000 is occupied

## License

MIT License - feel free to modify and distribute as needed.
