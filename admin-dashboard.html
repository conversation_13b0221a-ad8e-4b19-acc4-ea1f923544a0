<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="elevated-saas-ui.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="saas-min-h-screen">
        <!-- Navigation -->
        <nav class="saas-nav">
            <div class="saas-nav-container">
                <div class="saas-flex saas-items-center saas-gap-md">
                    <h1 class="saas-nav-brand">
                        <span class="brand-icon">◆</span> Admin Dashboard
                    </h1>
                    <span id="adminInfo" class="saas-text-muted saas-text-sm"></span>
                </div>
                <div class="saas-nav-menu">
                    <a href="./app.html" class="saas-nav-link">
                        📊 Main App
                    </a>
                    <button id="signOutBtn" class="saas-btn saas-btn-error">
                        Sign Out
                    </button>
                </div>
            </div>
        </nav>

        <div class="saas-container saas-mt-xl">
            <!-- Loading State -->
            <div id="loadingState" class="saas-text-center saas-py-3xl">
                <div class="saas-card max-w-md saas-mx-auto saas-fade-in">
                    <div class="saas-fade-in saas-mb-lg">⏳</div>
                    <p class="saas-text-secondary">Loading admin dashboard...</p>
                </div>
            </div>

            <!-- Access Denied -->
            <div id="accessDenied" class="hidden saas-text-center saas-py-3xl">
                <div class="saas-card max-w-md saas-mx-auto saas-fade-in">
                    <div class="text-6xl saas-mb-lg">🚫</div>
                    <h2 class="saas-heading-md saas-mb-lg">Access Denied</h2>
                    <p class="saas-text-secondary saas-mb-xl">You don't have admin privileges to access this dashboard.</p>
                    <a href="./app.html" class="saas-btn saas-btn-primary">
                        Go to Main App
                    </a>
                </div>
            </div>

            <!-- Admin Dashboard -->
            <div id="adminDashboard" class="hidden space-y-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="glass-card aurora-fade-in">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white/70 text-sm font-medium uppercase tracking-wider">Pending Approvals</p>
                                <p id="pendingCount" class="text-2xl font-bold text-aurora-warning">-</p>
                            </div>
                            <div class="text-aurora-warning text-3xl">⏳</div>
                        </div>
                    </div>
                    <div class="glass-card aurora-fade-in">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white/70 text-sm font-medium uppercase tracking-wider">Active Users</p>
                                <p id="activeCount" class="text-2xl font-bold text-aurora-success">-</p>
                            </div>
                            <div class="text-aurora-success text-3xl">👥</div>
                        </div>
                    </div>
                    <div class="glass-card aurora-fade-in">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white/70 text-sm font-medium uppercase tracking-wider">Total Users</p>
                                <p id="totalCount" class="text-2xl font-bold text-aurora-primary">-</p>
                            </div>
                            <div class="text-aurora-primary text-3xl">📊</div>
                        </div>
                    </div>
                    <div class="glass-card aurora-fade-in">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white/70 text-sm font-medium uppercase tracking-wider">Admin Users</p>
                                <p id="adminCount" class="text-2xl font-bold text-aurora-secondary">-</p>
                            </div>
                            <div class="text-aurora-secondary text-3xl">🛡️</div>
                        </div>
                    </div>
                </div>

                <!-- User Management -->
                <div class="glass-panel aurora-fade-in">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="aurora-section-title">User Management</h2>
                        <button id="refreshBtn" class="aurora-btn aurora-btn-primary">
                            <span>🔄 Refresh</span>
                        </button>
                    </div>
                    <div class="aurora-table-container">
                        <table class="aurora-table">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <!-- Users will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Audit Log -->
                <div class="glass-panel aurora-fade-in">
                    <h2 class="aurora-section-title mb-6">Recent Admin Actions</h2>
                    <div id="auditLog" class="space-y-3 max-h-64 overflow-y-auto aurora-scrollbar">
                        <!-- Audit entries will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Action Modal -->
    <div id="userModal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 w-96">
            <div class="glass-container">
                <h3 id="modalTitle" class="aurora-section-title mb-6"></h3>
                <div id="modalContent" class="space-y-4 mb-6">
                    <!-- Modal content will be populated here -->
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="modalCancel" class="aurora-btn aurora-btn-ghost">
                        <span>Cancel</span>
                    </button>
                    <button id="modalConfirm" class="aurora-btn aurora-btn-primary">
                        <span>Confirm</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script type="module">
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        let currentUser = null;
        let users = [];

        // DOM Elements
        const loadingState = document.getElementById('loadingState');
        const accessDenied = document.getElementById('accessDenied');
        const adminDashboard = document.getElementById('adminDashboard');
        const userTableBody = document.getElementById('userTableBody');
        const userModal = document.getElementById('userModal');

        // Initialize dashboard
        async function initDashboard() {
            try {
                // Check authentication
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error || !session?.user) {
                    window.location.href = './direct-login-simple.html';
                    return;
                }

                currentUser = session.user;
                document.getElementById('adminInfo').textContent = `Logged in as: ${currentUser.email}`;

                // Check admin privileges
                const { data: profile, error: profileError } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .single();

                if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'approved') {
                    showAccessDenied();
                    return;
                }

                // Load admin dashboard
                await loadDashboard();

            } catch (error) {
                console.error('Dashboard initialization error:', error);
                showAccessDenied();
            }
        }

        function showAccessDenied() {
            loadingState.classList.add('hidden');
            accessDenied.classList.remove('hidden');
        }

        async function loadDashboard() {
            try {
                loadingState.classList.add('hidden');
                adminDashboard.classList.remove('hidden');

                await Promise.all([
                    loadUsers(),
                    loadStats(),
                    loadAuditLog()
                ]);

            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        async function loadUsers() {
            try {
                const { data, error } = await supabase
                    .from('user_profiles')
                    .select(`
                        *,
                        approved_by_profile:user_profiles!user_profiles_approved_by_fkey(full_name)
                    `)
                    .order('created_at', { ascending: false });

                if (error) throw error;

                users = data || [];
                renderUserTable();

            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        async function loadStats() {
            try {
                const stats = {
                    pending: users.filter(u => u.status === 'pending').length,
                    active: users.filter(u => u.status === 'approved').length,
                    total: users.length,
                    admin: users.filter(u => u.role === 'admin' && u.status === 'approved').length
                };

                document.getElementById('pendingCount').textContent = stats.pending;
                document.getElementById('activeCount').textContent = stats.active;
                document.getElementById('totalCount').textContent = stats.total;
                document.getElementById('adminCount').textContent = stats.admin;

            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        async function loadAuditLog() {
            try {
                const { data, error } = await supabase
                    .from('admin_audit_log')
                    .select(`
                        *,
                        admin:user_profiles!admin_audit_log_admin_id_fkey(full_name, email),
                        target:user_profiles!admin_audit_log_target_user_id_fkey(full_name, email)
                    `)
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                const auditContainer = document.getElementById('auditLog');
                auditContainer.innerHTML = '';

                if (!data || data.length === 0) {
                    auditContainer.innerHTML = '<p class="text-gray-500 text-sm">No recent admin actions</p>';
                    return;
                }

                data.forEach(entry => {
                    const div = document.createElement('div');
                    div.className = 'border-l-4 border-blue-500 pl-3 py-2 bg-gray-50 rounded';
                    div.innerHTML = `
                        <p class="text-sm text-gray-800">
                            <strong>${entry.admin?.full_name || 'System'}</strong> ${entry.action}
                            ${entry.target ? `<strong>${entry.target.full_name || entry.target.email}</strong>` : ''}
                        </p>
                        <p class="text-xs text-gray-500">${new Date(entry.created_at).toLocaleString()}</p>
                    `;
                    auditContainer.appendChild(div);
                });

            } catch (error) {
                console.error('Error loading audit log:', error);
            }
        }

        function renderUserTable() {
            userTableBody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900">${user.full_name || 'N/A'}</div>
                                <div class="text-sm text-gray-500">${user.email}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full role-${user.role}">
                            ${user.role.toUpperCase()}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full status-${user.status}">
                            ${user.status.toUpperCase()}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(user.created_at).toLocaleDateString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                        ${user.status === 'pending' ? `
                            <button onclick="approveUser('${user.user_id}')" class="bg-green-500 text-white px-2 py-1 rounded text-xs hover:bg-green-600">Approve</button>
                            <button onclick="rejectUser('${user.user_id}')" class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600">Reject</button>
                        ` : ''}
                        ${user.status === 'approved' && user.role !== 'admin' ? `
                            <button onclick="promoteUser('${user.user_id}')" class="bg-purple-500 text-white px-2 py-1 rounded text-xs hover:bg-purple-600">Promote</button>
                        ` : ''}
                        ${user.status === 'approved' ? `
                            <button onclick="suspendUser('${user.user_id}')" class="bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600">Suspend</button>
                        ` : ''}
                        ${user.user_id !== currentUser.id ? `
                            <button onclick="editUser('${user.user_id}')" class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600">Edit</button>
                        ` : ''}
                    </td>
                `;
                userTableBody.appendChild(row);
            });
        }

        // User action functions
        window.approveUser = async (userId) => {
            await updateUserStatus(userId, 'approved', 'user', 'approved user');
        };

        window.rejectUser = async (userId) => {
            await updateUserStatus(userId, 'rejected', null, 'rejected user');
        };

        window.suspendUser = async (userId) => {
            await updateUserStatus(userId, 'suspended', null, 'suspended user');
        };

        window.promoteUser = async (userId) => {
            await updateUserStatus(userId, 'approved', 'admin', 'promoted user to admin');
        };

        async function updateUserStatus(userId, status, role = null, action) {
            try {
                const updateData = {
                    status,
                    approved_by: currentUser.id,
                    approved_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                if (role) {
                    updateData.role = role;
                }

                const { error } = await supabase
                    .from('user_profiles')
                    .update(updateData)
                    .eq('user_id', userId);

                if (error) throw error;

                // Log admin action
                await supabase
                    .from('admin_audit_log')
                    .insert({
                        admin_id: currentUser.id,
                        action,
                        target_user_id: userId,
                        details: { status, role }
                    });

                await loadUsers();
                await loadStats();
                await loadAuditLog();

            } catch (error) {
                console.error('Error updating user:', error);
                alert('Error updating user: ' + error.message);
            }
        }

        // Event listeners
        document.getElementById('signOutBtn').addEventListener('click', async () => {
            await supabase.auth.signOut();
            window.location.href = './direct-login-simple.html';
        });

        document.getElementById('refreshBtn').addEventListener('click', () => {
            loadDashboard();
        });

        // Initialize on page load
        initDashboard();
    </script>
</body>
</html>
