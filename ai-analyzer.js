// AI Calibration Gas Analyzer

// DOM Elements - Initialize as null and set up later
let calibrationGasStandardNameInput = null;
let addCalibrationCompoundBtn = null;
let calibrationCompoundsContainer = null;
let analyzeCalibrationBtn = null;
let calibrationResultsDiv = null;
let calibrationLoadingDiv = null;
let calibrationInputErrorDiv = null;

function initializeDOMElements() {
    console.log('🔬 AI Analyzer: Initializing DOM elements...');
    
    // Wait a moment to ensure DOM is fully ready
    if (document.readyState === 'loading') {
        console.warn('⚠️ AI Analyzer: DOM still loading, may cause issues');
    }
    
    calibrationGasStandardNameInput = document.getElementById('calibrationGasStandardName');
    addCalibrationCompoundBtn = document.getElementById('addCalibrationCompoundBtn');
    calibrationCompoundsContainer = document.getElementById('calibrationCompoundsContainer');
    analyzeCalibrationBtn = document.getElementById('analyzeCalibrationBtn');
    calibrationResultsDiv = document.getElementById('calibrationResults');
    calibrationLoadingDiv = document.getElementById('calibrationLoading');
    calibrationInputErrorDiv = document.getElementById('calibrationInputError');

    // Check if all elements exist
    const elements = {
        'calibrationGasStandardName': calibrationGasStandardNameInput,
        'addCalibrationCompoundBtn': addCalibrationCompoundBtn,
        'calibrationCompoundsContainer': calibrationCompoundsContainer,
        'analyzeCalibrationBtn': analyzeCalibrationBtn,
        'calibrationResults': calibrationResultsDiv,
        'calibrationLoading': calibrationLoadingDiv,
        'calibrationInputError': calibrationInputErrorDiv
    };

    let missingElements = [];
    Object.keys(elements).forEach(key => {
        if (!elements[key]) {
            missingElements.push(key);
            console.error(`❌ AI Analyzer: Missing DOM element: ${key}`);
        } else {
            console.log(`✅ AI Analyzer: Found DOM element: ${key} (${elements[key].tagName})`);
        }
    });

    if (missingElements.length > 0) {
        console.error(`❌ AI Analyzer: Cannot initialize - missing ${missingElements.length} DOM elements:`, missingElements);
        
        // Try to provide a helpful fallback
        if (missingElements.includes('calibrationResults')) {
            console.log('🔧 AI Analyzer: Attempting to create missing calibrationResults element...');
            const container = document.querySelector('.calibration-analyzer-container');
            if (container) {
                const resultsDiv = document.createElement('div');
                resultsDiv.id = 'calibrationResults';
                container.appendChild(resultsDiv);
                calibrationResultsDiv = resultsDiv;
                console.log('✅ AI Analyzer: Created missing calibrationResults element');
            }
        }
        
        return false;
    }

    console.log('✅ AI Analyzer: All DOM elements found successfully');
    return true;
}

function addCalibrationCompoundRow() {
    // Use the improved implementation from app-simple.html
    // This function is now defined in the main HTML file for better integration
    // Check if the HTML-based function exists and use it, otherwise fallback to this implementation
    
    // Ensure container is available - check both global variable and direct DOM access
    let container = calibrationCompoundsContainer;
    if (!container) {
        container = document.getElementById('calibrationCompoundsContainer');
        if (!container) {
            console.error('❌ AI Analyzer: Cannot add compound row - container not found');
            return false;
        }
    }

    const rowDiv = document.createElement('div');
    rowDiv.className = 'compound-row md-flex md-gap-md md-items-center md-mb-md';
    rowDiv.style.padding = '8px';
    rowDiv.style.backgroundColor = 'var(--md-surface-container-lowest)';
    rowDiv.style.borderRadius = 'var(--md-radius-md)';
    rowDiv.style.border = '1px solid var(--md-outline-variant)';
    
    rowDiv.innerHTML = `
        <div class="md-flex-1">
            <label class="md-label md-body-small md-text-secondary">Compound Name</label>
            <input type="text" class="md-input" placeholder="e.g., CH4, CO2, H2S, NH3, SO2" style="margin-top: 4px; font-size: 16px;">
        </div>
        <div style="min-width: 140px;">
            <label class="md-label md-body-small md-text-secondary">Concentration</label>
            <input type="number" class="md-input" placeholder="e.g., 1000" step="0.01" min="0" style="margin-top: 4px; font-size: 16px;">
        </div>
        <div style="min-width: 100px;">
            <label class="md-label md-body-small md-text-secondary">Unit</label>
            <select class="md-input" style="margin-top: 4px; font-size: 16px;">
                <option value="ppm">ppm</option>
                <option value="ppb">ppb</option>
                <option value="mg/m³">mg/m³</option>
                <option value="µg/m³">µg/m³</option>
                <option value="%">%</option>
                <option value="% LEL">% LEL</option>
                <option value="% UEL">% UEL</option>
                <option value="% volume">% volume</option>
            </select>
        </div>
        <div style="padding-top: 18px;">
            <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="removeCalibrationCompound(this)" title="Remove compound">
                <span class="material-icons">delete</span>
            </button>
        </div>
    `;
    container.appendChild(rowDiv);
    
    // Focus on the compound name input for better UX
    setTimeout(() => {
        const nameInput = rowDiv.querySelector('input[type="text"]');
        if (nameInput) {
            nameInput.focus();
        }
    }, 100);
    
    console.log('✅ AI Analyzer: Compound row added successfully');
    return true;
}

// Secure API calling function - uses server-side proxy with retry logic
async function callGeminiAPI(prompt) {
    const maxRetries = 3;
    const baseDelay = 2000; // 2 seconds

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`AI API attempt ${attempt}/${maxRetries}...`);
            
            const response = await fetch('/api/analyze-calibration', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt: prompt })
            });

            if (response.status === 503) {
                // Model overloaded - specific handling
                const errorBody = await response.text();
                console.warn(`Attempt ${attempt}: Model overloaded (503), retrying...`);
                
                if (attempt === maxRetries) {
                    throw new Error('AI model is currently overloaded. Please try again in a few minutes.');
                }
                
                // Exponential backoff for 503 errors
                const delay = baseDelay * Math.pow(2, attempt - 1);
                console.log(`Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
            }

            if (!response.ok) {
                const errorBody = await response.text();
                throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
            }

            const result = await response.json();
            console.log(`AI API success on attempt ${attempt}`);
            return result;

        } catch (error) {
            console.error(`Attempt ${attempt} failed:`, error);
            
            if (attempt === maxRetries) {
                // Final attempt failed
                throw error;
            }
            
            // Wait before retrying (but not for 503 errors, they have their own delay)
            if (!error.message.includes('503')) {
                const delay = baseDelay * attempt;
                console.log(`Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
}

function generatePrompt(calibrationGases, gasData) {
    return `You are an expert analytical chemist. Your task is to determine if a laboratory can analyze a series of calibration gas mixtures based on their current analytical capabilities.

Current Analytical Capabilities (gasData):
${JSON.stringify(gasData, null, 2)}

Calibration Gas Mixtures to Analyze:
${JSON.stringify(calibrationGases, null, 2)}

For each calibration gas mixture provided, please perform the following analysis:
1. Identify each component and its concentration (in PPM) in the mixture.
2. For each component, check against the 'Current Analytical Capabilities (gasData)':
    a. Does an analyte with a matching 'name' exist in gasData? (Perform a case-insensitive and trim-space comparison for names if possible, but primarily rely on exact match from the provided 'gasData' names).
    b. If it exists, does the component's 'concentrationPPM' fall within any of the 'current' min/max ranges specified for that analyte in gasData? Remember that 'min' and 'max' in gasData are also in PPM. A component is considered analyzable if its concentration falls within *any* of the defined current ranges for that analyte.
3. Based on the above, determine if ALL components in the calibration gas mixture can be fully analyzed with the current capabilities.
4. If a component cannot be analyzed, state the reason (e.g., "Compound not in capabilities list", "Concentration X ppm is outside current detectable ranges [A-B ppm, C-D ppm]", "No current capability ranges defined for this compound").

Provide your response as a JSON array, where each object in the array corresponds to one calibration gas from the input. Each object must follow this exact schema:
{
  "calibrationGasName": "string (name of the calibration gas)",
  "canAnalyzeFully": "boolean (true if all components can be analyzed, false otherwise)",
  "analysisDetails": "string (a brief summary of the findings for this gas, including reasons if it cannot be fully analyzed)",
  "problematicComponents": [ 
    {
      "compoundName": "string",
      "concentrationPPM": "number",
      "reason": "string (e.g., 'Not in current capabilities list', 'Concentration X ppm is outside detectable ranges [A-B ppm, C-D ppm]', 'No current ranges defined')"
    }
  ]
}
If 'canAnalyzeFully' is true, 'problematicComponents' should be an empty array.
Ensure your output is ONLY the JSON array, with no other text before or after it.`;
}

function displayCalibrationResults(results) {
    calibrationResultsDiv.innerHTML = ''; 
    if (!Array.isArray(results)) {
        calibrationResultsDiv.innerHTML = `<p class="md-body-small" style="color: var(--md-error);">Error: AI response was not in the expected array format.</p>`;
        return;
    }

    results.forEach(gasAnalysis => {
        const resultDiv = document.createElement('div');
        resultDiv.classList.add('md-card', 'md-mb-md');
        let content = `<h4 class="md-headline-small">${gasAnalysis.calibrationGasName || 'Unnamed Gas'}</h4>`;
        content += `<p class="md-body-medium md-mb-sm"><strong>Overall:</strong> <span class="${gasAnalysis.canAnalyzeFully ? 'can-analyze' : 'cannot-analyze'}">${gasAnalysis.canAnalyzeFully ? 'CAN ANALYZE FULLY' : 'CANNOT ANALYZE FULLY'}</span></p>`;
        content += `<p class="md-body-small md-text-secondary md-mb-sm"><strong>Details:</strong> ${gasAnalysis.analysisDetails || 'No details provided.'}</p>`;

        if (gasAnalysis.problematicComponents && gasAnalysis.problematicComponents.length > 0) {
            content += `<p class="md-body-small md-text-secondary"><strong>Problematic Components:</strong></p><ul class="md-list">`;
            gasAnalysis.problematicComponents.forEach(prob => {
                content += `<li class="md-body-small">- ${prob.compoundName} (${prob.concentrationPPM} ppm): ${prob.reason}</li>`;
            });
            content += `</ul>`;
        }
        resultDiv.innerHTML = content;
        calibrationResultsDiv.appendChild(resultDiv);
    });
}

function initializeAIAnalyzer() {
    console.log('🔬 AI Analyzer: Starting initialization...');
    
    // Initialize DOM elements first
    const domInitialized = initializeDOMElements();
    if (!domInitialized) {
        console.error('❌ AI Analyzer: DOM initialization failed');
        // Try to show error in whatever element we can find
        const errorTargets = [
            document.getElementById('calibrationResults'),
            document.querySelector('.calibration-analyzer-container'),
            document.body
        ];
        
        for (const target of errorTargets) {
            if (target) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'md-card md-mb-md';
                errorDiv.style.borderColor = 'var(--md-error)';
                errorDiv.style.backgroundColor = 'rgba(220, 38, 38, 0.05)';
                errorDiv.innerHTML = `
                    <p class="md-body-medium" style="color: var(--md-error);">❌ AI Analyzer Initialization Failed</p>
                    <p class="md-body-small md-text-secondary md-mt-sm">Could not initialize AI analyzer due to missing DOM elements. Please check console for details.</p>
                `;
                target.appendChild(errorDiv);
                break;
            }
        }
        return false;
    }

    console.log('🔬 AI Analyzer: Setting up event listeners...');
    
    // Set up add compound button
    if (addCalibrationCompoundBtn) {
        addCalibrationCompoundBtn.addEventListener('click', () => {
            console.log('🔬 AI Analyzer: Add compound button clicked');
            addCalibrationCompoundRow();
        });
        console.log('✅ AI Analyzer: Add compound button listener attached');
    } else {
        console.error('❌ AI Analyzer: Cannot attach listener to add compound button - element not found');
    }
    
    // Set up analyze button
    if (analyzeCalibrationBtn) {
        analyzeCalibrationBtn.addEventListener('click', async () => {
            console.log('🔬 AI Analyzer: Analyze button clicked');
            calibrationResultsDiv.innerHTML = '';
            calibrationInputErrorDiv.style.display = 'none';

        const standardName = calibrationGasStandardNameInput.value.trim();
        if (!standardName) {
            calibrationInputErrorDiv.textContent = "Please enter a name for the calibration gas standard.";
            calibrationInputErrorDiv.style.display = 'block';
            calibrationGasStandardNameInput.focus();
            return;
        }

        const components = [];
        const compoundRows = calibrationCompoundsContainer.querySelectorAll('.compound-row');
        if (compoundRows.length === 0) {
            calibrationInputErrorDiv.textContent = "Please add at least one compound to the calibration standard.";
            calibrationInputErrorDiv.style.display = 'block';
            return;
        }

        let isValid = true;
        compoundRows.forEach((row, index) => {
            // Updated to work with the new labeled input structure
            const nameInput = row.querySelector('input[type="text"]');
            const concInput = row.querySelector('input[type="number"]');
            const unitSelect = row.querySelector('select');

            if (!nameInput || !concInput || !unitSelect) {
                console.error(`❌ Missing input elements in compound row ${index + 1}`);
                isValid = false;
                return;
            }

            const compoundName = nameInput.value.trim();
            const concentrationStr = concInput.value.trim();
            const unit = unitSelect.value;

            if (!compoundName || !concentrationStr) {
                isValid = false;
                // Highlight the empty fields
                if (!compoundName) {
                    nameInput.classList.add('md-error');
                    nameInput.style.borderColor = 'var(--md-error)';
                }
                if (!concentrationStr) {
                    concInput.classList.add('md-error');
                    concInput.style.borderColor = 'var(--md-error)';
                }
                return;
            } else {
                // Remove error styling if fields are now valid
                nameInput.classList.remove('md-error');
                nameInput.style.borderColor = '';
                concInput.classList.remove('md-error');
                concInput.style.borderColor = '';
            }
            
            let concentrationPPM = parseFloat(concentrationStr);
            if (isNaN(concentrationPPM)) {
                isValid = false;
                concInput.classList.add('md-error');
                concInput.style.borderColor = 'var(--md-error)';
                return;
            }

            // Convert different units to PPM for consistency
            switch(unit) {
                case '%':
                case 'percent':
                    concentrationPPM *= 10000; // 1% = 10,000 PPM
                    break;
                case 'ppb':
                    concentrationPPM /= 1000; // 1000 ppb = 1 ppm
                    break;
                case 'mg/m³':
                    // Approximate conversion (depends on molecular weight, assuming typical values)
                    concentrationPPM *= 0.5; // Rough conversion factor
                    break;
                case 'µg/m³':
                    concentrationPPM *= 0.0005; // Rough conversion factor
                    break;
                case '% LEL':
                case '% UEL':
                case '% volume':
                    concentrationPPM *= 10000; // Treat as percentage
                    break;
                case 'ppm':
                default:
                    // Already in PPM, no conversion needed
                    break;
            }
            
            components.push({ 
                compoundName, 
                concentrationPPM,
                originalConcentration: concentrationStr,
                originalUnit: unit
            });
        });

        if (!isValid) {
            calibrationInputErrorDiv.textContent = "Please fill in all compound names and concentrations. Make sure all concentration values are valid numbers.";
            calibrationInputErrorDiv.style.display = 'block';
            calibrationInputErrorDiv.classList.remove('md-hidden');
            return;
        }
        
        const calibrationGases = [{
            calibrationGasName: standardName,
            components: components
        }];

        calibrationLoadingDiv.style.display = 'block';
        analyzeCalibrationBtn.disabled = true;
        analyzeCalibrationBtn.textContent = 'Analyzing...';

        try {
            const prompt = generatePrompt(calibrationGases, gasData);
            const result = await callGeminiAPI(prompt);
    
            if (result.candidates && result.candidates[0]?.content?.parts[0]?.text) {
                const aiJsonText = result.candidates[0].content.parts[0].text;
                try {
                    const analysisResults = JSON.parse(aiJsonText);
                    displayCalibrationResults(analysisResults);
                } catch (parseError) {
                     console.error("Error parsing AI JSON response:", parseError, "Raw AI response:", aiJsonText);
                     calibrationResultsDiv.innerHTML = `<p class="text-red-500">Error: Could not parse the AI's response. Please check console for details. Raw output: ${aiJsonText}</p>`;
                }
            } else {
                console.error("Gemini API response structure unexpected or content missing:", result);
                calibrationResultsDiv.innerHTML = `<p class="text-red-500">Error: Could not retrieve analysis from AI. Unexpected response structure.</p>`;
            }
        } catch (error) {
            console.error('Error fetching AI calibration analysis:', error);
            
            // Provide specific error messaging
            let errorMessage = 'Error fetching AI analysis';
            
            if (error.message.includes('overloaded')) {
                errorMessage = '🤖 AI Model Temporarily Overloaded - The AI service is currently experiencing high demand. Please try again in a few minutes.';
            } else if (error.message.includes('503')) {
                errorMessage = '⚠️ Service Temporarily Unavailable - The AI analysis service is temporarily unavailable. Please try again later.';
            } else if (error.message.includes('API key')) {
                errorMessage = '🔑 Configuration Error - API authentication issue. Please contact support.';
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
                errorMessage = '🌐 Network Error - Please check your internet connection and try again.';
            } else {
                errorMessage = `❌ Analysis Error: ${error.message}`;
            }
            
            calibrationResultsDiv.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p class="text-red-600 font-medium">${errorMessage}</p>
                    <button onclick="location.reload()" class="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                        🔄 Try Again
                    </button>
                </div>
            `;
        } finally {
            calibrationLoadingDiv.style.display = 'none';
            analyzeCalibrationBtn.disabled = false;
            analyzeCalibrationBtn.textContent = '🔬 Analyze Calibration Standard';
        }
    });
        console.log('✅ AI Analyzer: Analyze button listener attached');
    } else {
        console.error('❌ AI Analyzer: Cannot attach listener to analyze button - element not found');
    }
    
    console.log('✅ AI Analyzer: Initialization complete');
    
    // Automatically add first compound row for better UX
    setTimeout(() => {
        if (addCalibrationCompoundRow()) {
            console.log('✅ AI Analyzer: Initial compound row added automatically');
        }
    }, 100);
    
    return true;
}

// Global function to remove calibration compound rows
function removeCalibrationCompound(button) {
    const row = button.closest('.compound-row');
    if (row) {
        // Add a smooth removal animation
        row.style.transition = 'all 0.3s ease-out';
        row.style.opacity = '0';
        row.style.transform = 'translateX(-100%)';
        
        setTimeout(() => {
            row.remove();
            console.log('✅ AI Analyzer: Calibration compound row removed');
        }, 300);
    }
}

// Make the function globally available
window.removeCalibrationCompound = removeCalibrationCompound;

// Make sure the function is available globally and initialize when DOM is ready
window.initializeAIAnalyzer = initializeAIAnalyzer;
window.addCalibrationCompoundRow = addCalibrationCompoundRow;

// Initialize immediately if DOM is already loaded, or wait for it
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // Don't auto-initialize here since it's called from app.html initialization
        console.log('🔬 AI Analyzer: DOM ready, waiting for manual initialization');
    });
} else {
    console.log('🔬 AI Analyzer: DOM already loaded, ready for initialization');
}
