// Vercel Serverless Function for AI Analysis
module.exports = async function handler(req, res) {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { prompt } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }

        // Get API key from environment variable
        const apiKey = process.env.GEMINI_API_KEY;
        console.log('API Key configured:', apiKey ? 'Yes' : 'No');
        
        if (!apiKey) {
            console.error('GEMINI_API_KEY not found in environment variables');
            return res.status(500).json({ error: 'API key not configured' });
        }

        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = {
            contents: chatHistory,
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "ARRAY",
                    items: {
                        type: "OBJECT",
                        properties: {
                            "calibrationGasName": { "type": "STRING" },
                            "canAnalyzeFully": { "type": "BOOLEAN" },
                            "analysisDetails": { "type": "STRING" },
                            "problematicComponents": {
                                "type": "ARRAY",
                                "items": {
                                    "type": "OBJECT",
                                    "properties": {
                                        "compoundName": { "type": "STRING" },
                                        "concentrationPPM": { "type": "NUMBER" },
                                        "reason": { "type": "STRING" }
                                    },
                                    required: ["compoundName", "concentrationPPM", "reason"]
                                }
                            }
                        },
                        required: ["calibrationGasName", "canAnalyzeFully", "analysisDetails", "problematicComponents"]
                    }
                }
            }
        };

        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
        console.log('Making request to Gemini API...');

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        console.log('Gemini API response status:', response.status);

        if (!response.ok) {
            const errorBody = await response.text();
            console.error('Gemini API error:', errorBody);
            
            // Handle specific error cases
            if (response.status === 503) {
                return res.status(503).json({ 
                    error: 'The AI model is currently overloaded. Please try again in a few minutes.',
                    retryAfter: 60 // seconds
                });
            } else if (response.status === 429) {
                return res.status(429).json({ 
                    error: 'Rate limit exceeded. Please wait before making another request.',
                    retryAfter: 30 // seconds
                });
            } else if (response.status === 400) {
                return res.status(400).json({ 
                    error: 'Invalid request. Please check your input and try again.',
                    details: errorBody
                });
            }
            
            throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
        }

        const result = await response.json();
        console.log('Successful API response received');
        return res.json(result);

    } catch (error) {
        console.error('Error calling Gemini API:', error);
        return res.status(500).json({ error: 'Failed to analyze calibration data', details: error.message });
    }
}
