// Debug endpoint to check what's available
module.exports = function handler(req, res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    const apiKey = process.env.GEMINI_API_KEY;
    const hasApiKey = !!apiKey;
    const apiKeyLength = apiKey ? apiKey.length : 0;
    
    res.json({ 
        message: 'Debug endpoint working',
        method: req.method,
        url: req.url,
        hasApiKey,
        apiKeyLength,
        env: Object.keys(process.env).filter(key => key.includes('GEMINI')),
        timestamp: new Date().toISOString(),
        headers: req.headers
    });
}
