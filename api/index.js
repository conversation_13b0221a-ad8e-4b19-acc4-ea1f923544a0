// Default API endpoint
module.exports = async function handler(req, res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    res.status(200).json({ 
        message: 'Alpha Gas Solution API is running',
        version: '1.0.0',
        endpoints: [
            '/api/analyze-calibration - POST - AI calibration analysis',
            '/api/debug - GET - Debug information',
            '/api/hello - GET - Hello world test'
        ]
    });
};