// Simple test endpoint for Vercel
module.exports = function handler(req, res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    const apiKey = process.env.GEMINI_API_KEY;
    
    res.json({ 
        message: 'Test endpoint working',
        method: req.method,
        hasApiKey: !!apiKey,
        timestamp: new Date().toISOString()
    });
}
