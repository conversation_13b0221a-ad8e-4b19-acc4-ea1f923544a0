<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Analysis Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Google+Sans+Display:wght@400;500;600&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        /* Chart styling */
        .md-chart-container {
            margin-bottom: 2rem;
        }
        
        .md-chart-row {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: var(--md-surface);
            border-radius: 8px;
            border: 1px solid var(--md-outline);
        }
        
        .md-chart-label {
            min-width: 150px;
            font-weight: 500;
            margin-right: 1rem;
        }
        
        .md-chart-bars {
            flex: 1;
            position: relative;
            height: 40px;
            background: #f5f5f5;
            border-radius: 4px;
            margin-right: 1rem;
        }
        
        .md-chart-bar {
            position: absolute;
            height: 18px;
            border-radius: 3px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .md-chart-bar-current {
            background: linear-gradient(90deg, var(--google-blue), var(--google-blue-light));
            border: 2px solid var(--google-blue-dark);
        }
        
        .md-chart-bar-target {
            background: linear-gradient(90deg, var(--google-green), var(--google-green-light));
            border: 2px solid var(--google-green);
            top: 60%;
        }
        
        .md-chart-scale {
            height: 20px;
            margin-top: 1rem;
            border-top: 1px solid var(--md-outline);
            position: relative;
        }
        
        .md-loading-spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--md-outline);
            border-top: 3px solid var(--md-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .md-icon-sm {
            font-size: 16px;
            margin-right: 4px;
        }
    </style>
</head>
<body>
    <!-- Authentication Loading Screen -->
    <div id="authLoadingScreen" class="md-flex md-items-center md-justify-center" style="position: fixed; inset: 0; background: rgba(255,255,255,0.95); z-index: 1000;">
        <div class="md-card md-text-center">
            <div class="md-loading-spinner" style="margin: 0 auto 16px;"></div>
            <h3 class="md-title-large md-mb-sm">🔐 Verifying Authentication</h3>
            <p id="authLoadingText" class="md-text-secondary">Checking your session...</p>
            <div class="md-mt-md">
                <button onclick="retryAuth()" class="md-btn md-btn-outlined md-btn-sm">Retry</button>
                <button onclick="goToLogin()" class="md-btn md-btn-ghost md-btn-sm">Back to Login</button>
            </div>
        </div>
    </div>

    <!-- Main App Content (hidden until authenticated) -->
    <div id="mainAppContent" class="md-hidden">
        <!-- Material Design Top App Bar -->
        <header class="md-top-app-bar">
            <div class="md-top-app-bar-title">
                <span class="material-icons">analytics</span>
                Alpha Gas Solution
            </div>
            <div class="md-top-app-bar-actions">
                <span id="userStatus" class="md-chip">
                    <span id="userStatusIcon" class="material-icons">person</span>
                    <span id="userStatusText">Authenticated</span>
                </span>
                <button id="userMenuToggle" class="md-btn md-btn-text">
                    <span class="material-icons">account_circle</span>
                </button>
            </div>
        </header>

        <!-- User Menu -->
        <div id="userMenu" class="md-hidden md-card md-elevation-3 md-fade-in" style="position: absolute; top: 70px; right: 16px; min-width: 280px; z-index: 1000;">
            <div class="md-card-content">
                <div class="md-flex md-items-center md-mb-md">
                    <span class="material-icons md-text-primary md-mr-sm">account_circle</span>
                    <div>
                        <div id="userDisplayName" class="md-body-large md-font-medium">User</div>
                        <div id="userEmail" class="md-body-small md-text-secondary"><EMAIL></div>
                    </div>
                </div>
                <hr class="md-divider md-my-sm">
                <button id="signOutBtn" class="md-btn md-btn-ghost md-w-full md-justify-start">
                    <span class="material-icons md-mr-sm">logout</span>
                    Sign Out
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="md-container md-py-lg">
            <!-- Chart Section -->
            <div class="md-chart-container">
                <div class="md-flex md-items-center md-justify-between md-mb-lg">
                    <h1 class="md-headline-large md-text-center">
                        <span class="material-icons md-text-primary">analytics</span> Alpha Gas Solution
                    </h1>
                    <button id="refreshChart" class="md-btn md-btn-outlined">
                        <span class="material-icons">refresh</span>
                        Refresh
                    </button>
                </div>
                <p class="md-text-center md-text-secondary md-body-large md-mb-lg">Analysis Range Visualization & AI Calibration Check</p>

                <!-- Chart Legend -->
                <div class="md-flex md-gap-lg md-justify-center md-mb-lg">
                    <div class="md-flex md-items-center md-gap-sm">
                        <div class="md-chip md-chip-selected">Current Capability</div>
                    </div>
                    <div class="md-flex md-items-center md-gap-sm">
                        <div class="md-chip md-primary-container">Target Range (All-Rounder)</div>
                    </div>
                </div>

                <!-- Chart Container -->
                <div id="chart"></div>
                <div class="md-chart-scale"></div>
            </div>

            <!-- Add Analyte Form -->
            <div class="md-card md-mb-lg">
                <h2 class="md-title-large md-mb-lg">Add New Analyte to Chart</h2>
                <form id="addAnalyteForm">
                    <div class="md-form-group">
                        <label for="analyteName" class="md-label">Analyte Name</label>
                        <input type="text" id="analyteName" class="md-input" required placeholder="Enter analyte name">
                        <div id="analyteNameError" class="md-text-error md-body-small md-mt-xs md-hidden"></div>
                    </div>

                    <div class="md-form-group">
                        <h3 class="md-title-medium md-mb-md">Current Capabilities</h3>
                        <div id="currentRangesContainer" class="md-mb-md">
                            <!-- Current range inputs will be added here -->
                        </div>
                        <button type="button" class="md-btn md-btn-outlined md-btn-sm" onclick="addRangeInput('current')">
                            <span class="md-icon-sm">➕</span>
                            <span>Add Current Range</span>
                        </button>
                    </div>

                    <div class="md-form-group">
                        <h3 class="md-title-medium md-mb-md">Target Ranges</h3>
                        <div id="targetRangesContainer" class="md-mb-md">
                            <!-- Target range inputs will be added here -->
                        </div>
                        <button type="button" class="md-btn md-btn-outlined md-btn-sm" onclick="addRangeInput('target')">
                            <span class="md-icon-sm">➕</span>
                            <span>Add Target Range</span>
                        </button>
                    </div>
                    
                    <div class="md-form-group">
                        <label for="gapNotes" class="md-label">Gap Notes / Remarks</label>
                        <textarea id="gapNotes" class="md-textarea" placeholder="Add any notes or remarks about this analyte..."></textarea>
                    </div>

                    <!-- Save Status and Controls -->
                    <div class="md-card md-card-compact">
                        <div class="md-flex md-items-center md-justify-between md-mb-md">
                            <div id="authStatus" class="md-flex md-items-center md-gap-sm">
                                <span id="authIndicator" class="material-icons">verified_user</span>
                                <span id="authStatusText" class="md-text-secondary">Authenticated</span>
                            </div>
                            <div id="saveStatus" class="md-flex md-items-center md-gap-sm">
                                <span id="saveIndicator" class="material-icons">save</span>
                                <span id="saveStatusText" class="md-text-secondary">Ready to save</span>
                            </div>
                        </div>
                        
                        <div class="md-grid md-grid-cols-1 md:md-grid-cols-2 md-gap-md">
                            <button type="submit" class="md-btn md-btn-filled" id="addAnalyteBtn">
                                <span class="material-icons">analytics</span>
                                <span>Add Analyte to Chart</span>
                            </button>
                            <button type="button" class="md-btn md-btn-filled" id="confirmSaveBtn" title="Save data to database">
                                <span class="material-icons">save</span>
                                <span>Save to Database</span>
                            </button>
                        </div>
                        
                        <div class="md-mt-md md-p-md md-card md-card-compact">
                            <p class="md-body-small md-text-secondary">
                                <strong>Authenticated Mode:</strong> Data is automatically saved to the database and locally cached for optimal performance.
                            </p>
                        </div>
                    </div>
                </form>
            </div>

            <!-- AI Calibration Gas Analyzer Section -->
            <div class="md-card">
                <div class="md-card-header">
                    <h2 class="md-title-large">🔬 AI Calibration Gas Capability Analysis</h2>
                </div>
                <div class="md-card-content">
                    <div class="md-form-group">
                        <label for="calibrationGasStandardName" class="md-label">Calibration Gas Standard Name</label>
                        <input type="text" id="calibrationGasStandardName" class="md-input" placeholder="e.g., Daily Check Mix, Industrial Multi-Gas Standard, EPA Protocol Gas">
                        <div class="md-body-small md-text-secondary md-mt-xs">
                            💡 Enter a descriptive name for your calibration gas standard
                        </div>
                    </div>
                    
                    <div class="md-form-group">
                        <h3 class="md-title-medium md-mb-md">Components</h3>
                        <div class="md-body-small md-text-secondary md-mb-md">
                            🧪 Add the compounds present in your calibration gas standard. Click "Add Compound" to start.
                        </div>
                        <div id="calibrationCompoundsContainer" class="md-mb-md">
                            <!-- Compound input rows will be added here -->
                        </div>
                        <button type="button" id="addCalibrationCompoundBtn" class="md-btn md-btn-outlined">
                            <span class="material-icons">add</span>
                            Add Compound
                        </button>
                        <div class="md-flex md-gap-sm md-mt-sm md-flex-wrap">
                            <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('CH4', '2500', 'ppm')">
                                + CH₄
                            </button>
                            <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('CO2', '5000', 'ppm')">
                                + CO₂
                            </button>
                            <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('H2S', '25', 'ppm')">
                                + H₂S
                            </button>
                            <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('SO2', '40', 'ppm')">
                                + SO₂
                            </button>
                            <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('NH3', '50', 'ppm')">
                                + NH₃
                            </button>
                        </div>
                        <div class="md-body-small md-text-secondary md-mt-xs">
                            💡 Quick add: Click the buttons above to add common compounds with typical concentrations, or use "Add Compound" for custom entries
                        </div>
                    </div>
                    
                    <div id="calibrationInputError" class="md-text-error md-body-small md-hidden"></div>
                    
                    <button type="button" id="analyzeCalibrationBtn" class="md-btn md-btn-filled md-w-full">
                        <span class="material-icons">psychology</span>
                        Analyze Calibration Standard
                    </button>
                    
                    <div id="calibrationLoading" class="md-hidden">
                        <div class="md-flex md-items-center md-justify-center md-p-lg">
                            <div class="md-loading-spinner"></div>
                            <span class="md-ml-md md-text-secondary">Analyzing, please wait...</span>
                        </div>
                    </div>
                    
                    <div id="calibrationResults" class="md-card md-card-compact md-hidden md-mt-lg">
                        <!-- AI analysis results will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        let currentUser = null;

        function updateLoadingText(text) {
            document.getElementById('authLoadingText').textContent = text;
        }

        function showMainApp(user) {
            // Hide loading screen
            document.getElementById('authLoadingScreen').classList.add('md-hidden');
            
            // Show main app
            document.getElementById('mainAppContent').classList.remove('md-hidden');
            
            // Update user info
            document.getElementById('userDisplayName').textContent = user.user_metadata?.full_name || 'User';
            document.getElementById('userEmail').textContent = user.email;
            document.getElementById('userStatusText').textContent = user.email;
            
            console.log('✅ Main app loaded successfully for:', user.email);
        }

        function showAuthError(message) {
            document.getElementById('authLoadingScreen').innerHTML = `
                <div class="md-card md-text-center">
                    <div class="material-icons md-text-error" style="font-size: 48px; margin-bottom: 16px;">error</div>
                    <h3 class="md-title-large md-mb-sm">❌ Authentication Failed</h3>
                    <p class="md-text-secondary md-mb-lg">${message}</p>
                    <div class="md-flex md-gap-md md-justify-center">
                        <button onclick="retryAuth()" class="md-btn md-btn-filled">Retry</button>
                        <button onclick="goToLogin()" class="md-btn md-btn-outlined">Back to Login</button>
                    </div>
                </div>
            `;
        }

        async function initializeApp() {
            console.log('🚀 Initializing Alpha Gas Solution app...');
            
            try {
                updateLoadingText('Checking authentication...');
                
                // Check for existing session with multiple retries
                let session = null;
                let attempts = 0;
                const maxAttempts = 10;
                
                while (!session && attempts < maxAttempts) {
                    attempts++;
                    updateLoadingText(`Checking session (attempt ${attempts}/${maxAttempts})...`);
                    
                    const { data: { session: currentSession }, error } = await supabase.auth.getSession();
                    
                    if (error) {
                        console.error('Session check error:', error);
                        throw new Error(`Session error: ${error.message}`);
                    }
                    
                    if (currentSession && currentSession.user) {
                        session = currentSession;
                        console.log(`✅ Session found on attempt ${attempts}:`, session.user.email);
                        break;
                    }
                    
                    if (attempts < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                
                if (!session) {
                    throw new Error('No authentication session found after 10 attempts. Please log in again.');
                }
                
                currentUser = session.user;
                updateLoadingText('Authentication successful! Loading dashboard...');
                
                // Small delay to show success message
                setTimeout(async () => {
                    showMainApp(currentUser);
                    setupUserMenu();
                    setupFormHandlers();
                    await loadDataFromStorage();
                    renderChart();
                }, 1000);
                
            } catch (error) {
                console.error('❌ App initialization failed:', error);
                showAuthError(error.message);
            }
        }

        function setupUserMenu() {
            const userMenuToggle = document.getElementById('userMenuToggle');
            const userMenu = document.getElementById('userMenu');
            const signOutBtn = document.getElementById('signOutBtn');
            
            // Toggle menu
            userMenuToggle.addEventListener('click', () => {
                userMenu.classList.toggle('md-hidden');
            });
            
            // Hide menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!userMenu.contains(e.target) && !userMenuToggle.contains(e.target)) {
                    userMenu.classList.add('md-hidden');
                }
            });
            
            // Sign out
            signOutBtn.addEventListener('click', async () => {
                try {
                    updateLoadingText('Signing out...');
                    document.getElementById('authLoadingScreen').classList.remove('md-hidden');
                    document.getElementById('mainAppContent').classList.add('md-hidden');
                    
                    await supabase.auth.signOut();
                    
                    setTimeout(() => {
                        window.location.href = 'simple-auth-login.html';
                    }, 1000);
                } catch (error) {
                    console.error('Sign out error:', error);
                    alert('Error signing out: ' + error.message);
                }
            });
        }

        function retryAuth() {
            location.reload();
        }

        function goToLogin() {
            window.location.href = 'simple-auth-login.html';
        }

        // Listen for auth state changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth state change:', event);
            
            if (event === 'SIGNED_OUT') {
                window.location.href = 'simple-auth-login.html';
            }
        });

        // Initialize when page loads
        window.addEventListener('load', initializeApp);

        // Analysis functionality
        let analytesData = [];
        
        // Supabase Storage Implementation
        class SupabaseStorage {
            async getUserId() {
                const { data: { user } } = await supabase.auth.getUser();
                return user?.id || null;
            }

            async loadHybridData() {
                try {
                    const userId = await this.getUserId();
                    if (!userId) {
                        throw new Error('User not authenticated');
                    }

                    console.log('Loading hybrid data from Supabase for user:', userId);
                    
                    const { data, error } = await supabase
                        .from('gas_analytes')
                        .select('*')
                        .order('created_at');

                    if (error) {
                        console.error('Error loading hybrid data from Supabase:', error);
                        throw error;
                    }

                    if (!data || data.length === 0) {
                        console.log('No hybrid data found in Supabase');
                        return [];
                    }

                    // Transform Supabase data back to app format
                    const transformedData = data.map(item => ({
                        name: item.name,
                        current: item.current_ranges || [],
                        target: item.target_ranges || [],
                        notes: item.gap_notes || '',
                        timestamp: item.created_at,
                        isCustom: item.is_custom,
                        is_shared: item.is_shared || false,
                        data_type: item.data_type || 'user_target'
                    }));

                    console.log('Hybrid data loaded successfully from Supabase:');
                    console.log('- Shared capabilities:', transformedData.filter(d => d.is_shared).length);
                    console.log('- User targets:', transformedData.filter(d => !d.is_shared).length);
                    
                    return transformedData;
                } catch (error) {
                    console.error('Failed to load hybrid data from Supabase:', error);
                    throw error;
                }
            }

            async saveUserTargets(userTargets) {
                try {
                    const userId = await this.getUserId();
                    if (!userId) {
                        throw new Error('User not authenticated');
                    }

                    console.log('Saving user targets to Supabase...', {
                        userId: userId,
                        targetCount: userTargets.length,
                        targets: userTargets.map(t => t.name)
                    });
                    
                    // First, clear existing user targets (not shared data)
                    const { error: deleteError } = await supabase
                        .from('gas_analytes')
                        .delete()
                        .eq('user_id', userId)
                        .eq('is_shared', false);

                    if (deleteError) {
                        console.error('Error clearing existing user targets:', deleteError);
                    } else {
                        console.log('Existing user targets cleared successfully');
                    }

                    // Insert user targets only
                    if (userTargets.length > 0) {
                        const dataToInsert = userTargets.map(analyte => ({
                            user_id: userId,
                            name: analyte.name,
                            current_ranges: analyte.current || [],
                            target_ranges: analyte.target || [],
                            gap_notes: analyte.notes || '',
                            is_custom: analyte.isCustom !== false,
                            is_shared: false,
                            data_type: 'user_target'
                        }));

                        console.log('User targets to insert:', dataToInsert.length);

                        const { data, error } = await supabase
                            .from('gas_analytes')
                            .insert(dataToInsert);

                        if (error) {
                            console.error('Error saving user targets to Supabase:', error);
                            throw error;
                        }

                        console.log('User targets saved successfully to Supabase');
                        return data;
                    } else {
                        console.log('No user targets to save');
                        return [];
                    }
                } catch (error) {
                    console.error('Failed to save user targets to Supabase:', error);
                    throw error;
                }
            }
        }

        const supabaseStorage = new SupabaseStorage();
        
        function addRangeInput(type) {
            const container = document.getElementById(`${type}RangesContainer`);
            const rangeDiv = document.createElement('div');
            rangeDiv.className = 'md-flex md-gap-md md-mb-sm';
            rangeDiv.innerHTML = `
                <input type="number" class="md-input" placeholder="Min" step="0.01">
                <input type="number" class="md-input" placeholder="Max" step="0.01">
                <select class="md-input">
                    <option value="ppm">ppm</option>
                    <option value="ppb">ppb</option>
                    <option value="mg/m³">mg/m³</option>
                    <option value="%">%</option>
                </select>
                <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="removeRangeInput(this)">
                    <span class="material-icons">remove</span>
                </button>
            `;
            container.appendChild(rangeDiv);
        }

        function removeRangeInput(button) {
            button.parentElement.remove();
        }

        function addCalibrationCompoundRow() {
            const container = document.getElementById('calibrationCompoundsContainer');
            if (!container) {
                console.error('❌ Calibration compounds container not found');
                return false;
            }

            const compoundDiv = document.createElement('div');
            compoundDiv.className = 'compound-row md-flex md-gap-md md-items-center md-mb-md';
            compoundDiv.style.padding = '8px';
            compoundDiv.style.backgroundColor = 'var(--md-surface-container-lowest)';
            compoundDiv.style.borderRadius = 'var(--md-radius-md)';
            compoundDiv.style.border = '1px solid var(--md-outline-variant)';
            
            compoundDiv.innerHTML = `
                <div class="md-flex-1">
                    <label class="md-label md-body-small md-text-secondary">Compound Name</label>
                    <input type="text" class="md-input" placeholder="e.g., CH4, CO2, H2S, NH3, SO2" style="margin-top: 4px; font-size: 16px;">
                </div>
                <div style="min-width: 140px;">
                    <label class="md-label md-body-small md-text-secondary">Concentration</label>
                    <input type="number" class="md-input" placeholder="e.g., 1000" step="0.01" min="0" style="margin-top: 4px; font-size: 16px;">
                </div>
                <div style="min-width: 100px;">
                    <label class="md-label md-body-small md-text-secondary">Unit</label>
                    <select class="md-input" style="margin-top: 4px; font-size: 16px;">
                        <option value="ppm">ppm</option>
                        <option value="ppb">ppb</option>
                        <option value="mg/m³">mg/m³</option>
                        <option value="µg/m³">µg/m³</option>
                        <option value="%">%</option>
                        <option value="% LEL">% LEL</option>
                        <option value="% UEL">% UEL</option>
                        <option value="% volume">% volume</option>
                    </select>
                </div>
                <div style="padding-top: 18px;">
                    <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="removeCalibrationCompound(this)" title="Remove compound">
                        <span class="material-icons">delete</span>
                    </button>
                </div>
            `;
            container.appendChild(compoundDiv);
            
            // Focus on the compound name input for better UX
            setTimeout(() => {
                const nameInput = compoundDiv.querySelector('input[type="text"]');
                if (nameInput) {
                    nameInput.focus();
                }
                // Enhance the newly added inputs
                enhanceCompoundInputs();
            }, 100);
            
            console.log('✅ Calibration compound row added successfully');
            return true;
        }

        function removeCalibrationCompound(button) {
            const row = button.closest('.compound-row');
            if (row) {
                // Add a smooth removal animation
                row.style.transition = 'all 0.3s ease-out';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    console.log('✅ Calibration compound row removed');
                }, 300);
            }
        }

        // Function to add preset compounds with predefined values
        function addPresetCompound(name, concentration, unit) {
            // First add a new compound row
            addCalibrationCompoundRow();
            
            // Then fill it with the preset values
            setTimeout(() => {
                const rows = document.querySelectorAll('.compound-row');
                const lastRow = rows[rows.length - 1];
                if (lastRow) {
                    const inputs = lastRow.querySelectorAll('input, select');
                    if (inputs.length >= 3) {
                        inputs[0].value = name; // Compound name
                        inputs[1].value = concentration; // Concentration
                        inputs[2].value = unit; // Unit
                        
                        // Add visual feedback
                        lastRow.style.backgroundColor = 'var(--md-surface-container-high)';
                        setTimeout(() => {
                            lastRow.style.backgroundColor = 'var(--md-surface-container-lowest)';
                        }, 1000);
                        
                        console.log(`✅ Added preset compound: ${name} (${concentration} ${unit})`);
                    }
                }
            }, 100);
        }

        function renderChart() {
            const chartContainer = document.getElementById('chart');
            if (!chartContainer) return;

            chartContainer.innerHTML = '';
            
            if (analytesData.length === 0) {
                chartContainer.innerHTML = `
                    <div class="md-flex md-items-center md-justify-center md-p-xl">
                        <div class="md-text-center">
                            <span class="material-icons" style="font-size: 48px; color: var(--md-secondary);">analytics</span>
                            <p class="md-body-large md-text-secondary md-mt-md">No analytes added yet</p>
                            <p class="md-body-small md-text-secondary">Add analytes using the form below to see them visualized here</p>
                        </div>
                    </div>
                `;
                return;
            }

            // Create chart header
            const chartHeader = document.createElement('div');
            chartHeader.className = 'md-mb-lg';
            chartHeader.innerHTML = `
                <div class="md-flex md-items-center md-justify-between md-mb-md">
                    <h3 class="md-title-large">📊 Gas Analysis Chart</h3>
                    <div class="md-flex md-gap-sm">
                        <div class="md-chip md-chip-outline">
                            <span class="material-icons md-icon-sm">science</span>
                            Current: ${analytesData.reduce((sum, a) => sum + (a.current?.length || 0), 0)} ranges
                        </div>
                        <div class="md-chip md-chip-filled">
                            <span class="material-icons md-icon-sm">target</span>
                            Target: ${analytesData.reduce((sum, a) => sum + (a.target?.length || 0), 0)} ranges
                        </div>
                    </div>
                </div>
                <div class="md-flex md-gap-md md-mb-md">
                    <div class="md-flex md-items-center md-gap-sm">
                        <div style="width: 16px; height: 16px; background: linear-gradient(90deg, var(--google-blue), var(--google-blue-light)); border-radius: 3px;"></div>
                        <span class="md-body-small">Current Capability</span>
                    </div>
                    <div class="md-flex md-items-center md-gap-sm">
                        <div style="width: 16px; height: 16px; background: linear-gradient(90deg, var(--google-green), var(--google-green-light)); border-radius: 3px;"></div>
                        <span class="md-body-small">Target Range</span>
                    </div>
                </div>
            `;
            chartContainer.appendChild(chartHeader);

            // Calculate overall range for better scaling
            let globalMin = Infinity;
            let globalMax = -Infinity;
            let primaryUnit = 'ppm';

            analytesData.forEach(analyte => {
                [...(analyte.current || []), ...(analyte.target || [])].forEach(range => {
                    if (range.min < globalMin) globalMin = range.min;
                    if (range.max > globalMax) globalMax = range.max;
                    if (range.unit) primaryUnit = range.unit;
                });
            });

            // Handle edge cases
            if (globalMin === Infinity) globalMin = 0;
            if (globalMax === -Infinity) globalMax = 100;
            if (globalMax === globalMin) globalMax = globalMin + 100;

            const range = globalMax - globalMin;
            const padding = range * 0.1; // 10% padding
            const chartMin = Math.max(0, globalMin - padding);
            const chartMax = globalMax + padding;
            const chartRange = chartMax - chartMin;

            analytesData.forEach((analyte, index) => {
                const analyteRow = document.createElement('div');
                analyteRow.className = 'md-chart-row';
                
                // Create chart bars visualization with proper scaling
                let currentBars = '';
                let targetBars = '';
                
                if (analyte.current && analyte.current.length > 0) {
                    currentBars = analyte.current.map((range, rangeIndex) => {
                        const leftPos = Math.max(0, Math.min(100, ((range.min - chartMin) / chartRange) * 100));
                        const rightPos = Math.max(0, Math.min(100, ((range.max - chartMin) / chartRange) * 100));
                        const width = Math.max(1, rightPos - leftPos);
                        
                        return `
                            <div class="md-chart-bar md-chart-bar-current" 
                                 style="left: ${leftPos}%; width: ${width}%;"
                                 title="Current Range ${rangeIndex + 1}: ${range.min} - ${range.max} ${range.unit}">
                            </div>
                        `;
                    }).join('');
                }
                
                if (analyte.target && analyte.target.length > 0) {
                    targetBars = analyte.target.map((range, rangeIndex) => {
                        const leftPos = Math.max(0, Math.min(100, ((range.min - chartMin) / chartRange) * 100));
                        const rightPos = Math.max(0, Math.min(100, ((range.max - chartMin) / chartRange) * 100));
                        const width = Math.max(1, rightPos - leftPos);
                        
                        return `
                            <div class="md-chart-bar md-chart-bar-target" 
                                 style="left: ${leftPos}%; width: ${width}%;"
                                 title="Target Range ${rangeIndex + 1}: ${range.min} - ${range.max} ${range.unit}">
                            </div>
                        `;
                    }).join('');
                }

                // Create scale labels with proper values
                const scaleLabels = [];
                for (let i = 0; i <= 4; i++) {
                    const value = chartMin + (chartRange * i / 4);
                    const formattedValue = value >= 1000 ? 
                        (value / 1000).toFixed(1) + 'k' : 
                        value.toFixed(value < 10 ? 1 : 0);
                    scaleLabels.push(`
                        <div style="position: absolute; left: ${i * 25}%; font-size: 10px; color: var(--md-outline); transform: translateX(-50%);">
                            ${formattedValue}
                        </div>
                    `);
                }
                
                // Display range details
                const currentRangeTexts = analyte.current?.map(r => `${r.min}-${r.max} ${r.unit}`).join(', ') || 'None';
                const targetRangeTexts = analyte.target?.map(r => `${r.min}-${r.max} ${r.unit}`).join(', ') || 'None';
                
                analyteRow.innerHTML = `
                    <div class="md-chart-label" style="min-width: 200px;">
                        <div class="md-flex md-items-center md-gap-sm md-mb-xs">
                            <span class="material-icons md-text-primary">science</span>
                            <strong>${analyte.name}</strong>
                        </div>
                        <div class="md-body-small md-text-secondary">
                            <div>🔵 Current: ${currentRangeTexts}</div>
                            <div class="md-mt-xs">🟢 Target: ${targetRangeTexts}</div>
                        </div>
                        ${analyte.notes ? `<div class="md-body-small md-text-secondary md-mt-xs md-italic">"${analyte.notes}"</div>` : ''}
                    </div>
                    <div class="md-chart-bars" style="position: relative;">
                        ${currentBars}
                        ${targetBars}
                        <div class="md-chart-scale" style="margin-top: 8px; border-top: 1px solid var(--md-outline);">
                            ${scaleLabels.join('')}
                            <div style="position: absolute; right: -20px; top: -15px; font-size: 10px; color: var(--md-outline);">
                                ${primaryUnit}
                            </div>
                        </div>
                    </div>
                    <div class="md-flex md-items-center md-gap-sm">
                        <div class="md-flex md-flex-col md-gap-xs md-text-xs">
                            <span class="md-text-secondary">Ranges: ${(analyte.current?.length || 0) + (analyte.target?.length || 0)}</span>
                            <span class="md-text-secondary">${analyte.timestamp ? new Date(analyte.timestamp).toLocaleDateString() : ''}</span>
                        </div>
                        <button class="md-btn md-btn-ghost md-btn-sm" onclick="removeAnalyte(${index})" title="Remove this analyte">
                            <span class="material-icons">delete</span>
                        </button>
                    </div>
                `;
                chartContainer.appendChild(analyteRow);
            });

            // Add chart summary
            const chartSummary = document.createElement('div');
            chartSummary.className = 'md-card md-card-compact md-mt-md';
            chartSummary.innerHTML = `
                <h4 class="md-title-small md-mb-sm">📈 Chart Summary</h4>
                <div class="md-grid md-grid-cols-2 md:md-grid-cols-4 md-gap-sm md-text-xs">
                    <div class="md-text-center">
                        <div class="md-text-primary md-font-medium">${analytesData.length}</div>
                        <div class="md-text-secondary">Analytes</div>
                    </div>
                    <div class="md-text-center">
                        <div class="md-text-primary md-font-medium">${chartMin.toFixed(1)} - ${chartMax.toFixed(1)}</div>
                        <div class="md-text-secondary">Range (${primaryUnit})</div>
                    </div>
                    <div class="md-text-center">
                        <div class="md-text-primary md-font-medium">${analytesData.reduce((sum, a) => sum + (a.current?.length || 0), 0)}</div>
                        <div class="md-text-secondary">Current Ranges</div>
                    </div>
                    <div class="md-text-center">
                        <div class="md-text-primary md-font-medium">${analytesData.reduce((sum, a) => sum + (a.target?.length || 0), 0)}</div>
                        <div class="md-text-secondary">Target Ranges</div>
                    </div>
                </div>
            `;
            chartContainer.appendChild(chartSummary);
            
            console.log(`✅ Chart rendered with ${analytesData.length} analytes (Range: ${chartMin.toFixed(1)} - ${chartMax.toFixed(1)} ${primaryUnit})`);
        }

        async function removeAnalyte(index) {
            analytesData.splice(index, 1);
            renderChart();
            await saveDataToStorage();
        }

        async function saveDataToStorage() {
            try {
                document.getElementById('saveStatusText').textContent = 'Saving...';
                
                // Save to localStorage for immediate feedback
                localStorage.setItem('gasAnalytesData', JSON.stringify(analytesData));
                console.log('Data saved to localStorage');
                
                // Save to Supabase if authenticated
                if (currentUser) {
                    // Transform analytesData to match Supabase format
                    const userTargets = analytesData.map(analyte => ({
                        name: analyte.name,
                        current: analyte.current || [],
                        target: analyte.target || [],
                        notes: analyte.notes || '',
                        isCustom: true,
                        is_shared: false,
                        data_type: 'user_target'
                    }));
                    
                    await supabaseStorage.saveUserTargets(userTargets);
                    document.getElementById('saveStatusText').textContent = 'Saved to database';
                    console.log('✅ Data saved to Supabase');
                } else {
                    document.getElementById('saveStatusText').textContent = 'Saved locally';
                    console.log('⚠️ User not authenticated, saved locally only');
                }
            } catch (error) {
                console.error('Error saving data:', error);
                document.getElementById('saveStatusText').textContent = 'Save failed';
            }
        }

        async function loadDataFromStorage() {
            try {
                console.log('🔄 Loading data from storage...');
                
                if (currentUser) {
                    // Load from Supabase for authenticated users
                    console.log('👤 Loading data from Supabase for authenticated user:', currentUser.email);
                    const hybridData = await supabaseStorage.loadHybridData();
                    
                    console.log('📊 Raw hybrid data from Supabase:', hybridData);
                    
                    if (hybridData && hybridData.length > 0) {
                        // Transform Supabase data to analytesData format
                        analytesData = hybridData.map(item => {
                            console.log('🔄 Transforming item:', item);
                            return {
                                name: item.name,
                                current: item.current || [],
                                target: item.target || [],
                                notes: item.notes || '',
                                timestamp: item.timestamp
                            };
                        });
                        
                        console.log(`✅ Loaded ${analytesData.length} analytes from Supabase:`, analytesData);
                        
                        // Cache to localStorage
                        localStorage.setItem('gasAnalytesData', JSON.stringify(analytesData));
                    } else {
                        console.log('⚠️ No data found in Supabase, checking localStorage');
                        loadFromLocalStorage();
                    }
                } else {
                    // Load from localStorage for guest users
                    console.log('👤 Loading data from localStorage (guest mode)');
                    loadFromLocalStorage();
                }
                
                console.log('📊 Final analytesData before rendering:', analytesData);
                renderChart();
            } catch (error) {
                console.error('❌ Error loading data from Supabase:', error);
                console.log('🔄 Falling back to localStorage');
                loadFromLocalStorage();
                renderChart();
            }
        }
        
        function loadFromLocalStorage() {
            try {
                const savedData = localStorage.getItem('gasAnalytesData');
                if (savedData) {
                    analytesData = JSON.parse(savedData);
                    console.log(`✅ Loaded ${analytesData.length} analytes from localStorage`);
                } else {
                    console.log('⚠️ No data found in localStorage');
                    analytesData = [];
                }
            } catch (error) {
                console.error('Error loading from localStorage:', error);
                analytesData = [];
            }
        }

        // Form handling
        function setupFormHandlers() {
            const addAnalyteForm = document.getElementById('addAnalyteForm');
            const addCalibrationCompoundBtn = document.getElementById('addCalibrationCompoundBtn');
            const analyzeCalibrationBtn = document.getElementById('analyzeCalibrationBtn');
            const refreshChart = document.getElementById('refreshChart');
            const confirmSaveBtn = document.getElementById('confirmSaveBtn');

            addAnalyteForm.addEventListener('submit', handleAddAnalyte);
            addCalibrationCompoundBtn.addEventListener('click', addCalibrationCompoundRow);
            analyzeCalibrationBtn.addEventListener('click', handleAnalyzeCalibration);
            refreshChart.addEventListener('click', renderChart);
            confirmSaveBtn.addEventListener('click', async () => {
                await saveDataToStorage();
            });

            // Add initial range inputs
            addRangeInput('current');
            addRangeInput('target');
            addCalibrationCompoundRow();
        }

        async function handleAddAnalyte(e) {
            e.preventDefault();
            
            const analyteName = document.getElementById('analyteName').value;
            if (!analyteName) return;

            const currentRanges = extractRanges('current');
            const targetRanges = extractRanges('target');
            const notes = document.getElementById('gapNotes').value;

            const newAnalyte = {
                name: analyteName,
                current: currentRanges,
                target: targetRanges,
                notes: notes,
                timestamp: new Date().toISOString()
            };

            analytesData.push(newAnalyte);
            renderChart();
            await saveDataToStorage();

            // Reset form
            addAnalyteForm.reset();
            document.getElementById('currentRangesContainer').innerHTML = '';
            document.getElementById('targetRangesContainer').innerHTML = '';
            addRangeInput('current');
            addRangeInput('target');
        }

        function extractRanges(type) {
            const container = document.getElementById(`${type}RangesContainer`);
            const rangeDivs = container.children;
            const ranges = [];

            for (let rangeDiv of rangeDivs) {
                const inputs = rangeDiv.querySelectorAll('input');
                const select = rangeDiv.querySelector('select');
                
                if (inputs[0].value && inputs[1].value) {
                    ranges.push({
                        min: parseFloat(inputs[0].value),
                        max: parseFloat(inputs[1].value),
                        unit: select.value
                    });
                }
            }

            return ranges;
        }

        function handleAnalyzeCalibration() {
            const standardName = document.getElementById('calibrationGasStandardName').value;
            const compounds = extractCompounds();

            if (!standardName || compounds.length === 0) {
                document.getElementById('calibrationInputError').textContent = 'Please enter standard name and at least one compound';
                document.getElementById('calibrationInputError').classList.remove('md-hidden');
                return;
            }

            document.getElementById('calibrationInputError').classList.add('md-hidden');
            document.getElementById('calibrationLoading').classList.remove('md-hidden');

            // Simulate AI analysis
            setTimeout(() => {
                const resultsDiv = document.getElementById('calibrationResults');
                resultsDiv.innerHTML = `
                    <h3 class="md-title-medium md-mb-md">Analysis Results for "${standardName}"</h3>
                    <div class="md-status-success md-mb-md">
                        ✅ Analysis complete! Found ${compounds.length} compounds.
                    </div>
                    <div class="md-grid md-grid-cols-1 md:md-grid-cols-2 md-gap-md">
                        ${compounds.map(compound => `
                            <div class="md-card md-card-compact">
                                <h4 class="md-title-small">${compound.name}</h4>
                                <p class="md-body-small">${compound.concentration} ${compound.unit}</p>
                                <div class="md-status-info md-mt-sm">Suitable for calibration</div>
                            </div>
                        `).join('')}
                    </div>
                `;
                
                document.getElementById('calibrationLoading').classList.add('md-hidden');
                resultsDiv.classList.remove('md-hidden');
            }, 2000);
        }

        function extractCompounds() {
            const container = document.getElementById('calibrationCompoundsContainer');
            const compoundDivs = container.children;
            const compounds = [];

            for (let compoundDiv of compoundDivs) {
                const inputs = compoundDiv.querySelectorAll('input');
                const select = compoundDiv.querySelector('select');
                
                if (inputs[0].value && inputs[1].value) {
                    compounds.push({
                        name: inputs[0].value,
                        concentration: parseFloat(inputs[1].value),
                        unit: select.value
                    });
                }
            }

            return compounds;
        }

        // Add input event listeners for better UX
        function enhanceCompoundInputs() {
            const compoundRows = document.querySelectorAll('.compound-row');
            compoundRows.forEach(row => {
                const nameInput = row.querySelector('input[type="text"]');
                if (nameInput && !nameInput.hasAttribute('data-enhanced')) {
                    nameInput.setAttribute('data-enhanced', 'true');
                    
                    // Add autocomplete suggestions
                    nameInput.addEventListener('input', function() {
                        const commonCompounds = [
                            'CH4', 'CO2', 'H2S', 'SO2', 'NH3', 'NO', 'NO2', 
                            'O2', 'CO', 'HCl', 'HF', 'Cl2', 'H2', 'C2H4',
                            'C2H6', 'C3H8', 'C4H10', 'Benzene', 'Toluene'
                        ];
                        
                        const value = this.value.toUpperCase();
                        if (value.length > 0) {
                            const matches = commonCompounds.filter(compound => 
                                compound.startsWith(value)
                            );
                            
                            if (matches.length > 0 && matches[0] !== value) {
                                // Add visual hint
                                this.style.backgroundColor = 'var(--md-surface-container-highest)';
                                this.title = `Suggestion: ${matches[0]}`;
                            } else {
                                this.style.backgroundColor = '';
                                this.title = '';
                            }
                        }
                    });
                    
                    // Auto-uppercase chemical formulas
                    nameInput.addEventListener('blur', function() {
                        if (this.value.match(/^[a-z0-9]+$/i)) {
                            // If it looks like a chemical formula, uppercase it
                            this.value = this.value.toUpperCase();
                        }
                        this.style.backgroundColor = '';
                        this.title = '';
                    });
                }
            });
        }

        // ...existing code...
    </script>
</body>
</html>
