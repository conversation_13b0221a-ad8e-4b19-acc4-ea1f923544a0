<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Analysis Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Google+Sans+Display:wght@400;500;600&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <!-- Material Design Top App Bar -->
    <header class="md-top-app-bar">
        <div class="md-top-app-bar-title">
            <span class="material-icons">analytics</span>
            Alpha Gas Solution
        </div>
        <div class="md-top-app-bar-actions">
            <span id="userStatus" class="md-chip">
                <span id="userStatusIcon" class="material-icons">lock</span>
                <span id="userStatusText">Loading...</span>
            </span>
            <button id="userMenuToggle" class="md-btn md-btn-text">
                <span id="userMenuInitial" class="material-icons">account_circle</span>
            </button>
        </div>
    </header>

    <!-- User Menu -->
    <div id="userMenu" class="md-hidden md-card md-elevation-3 md-fade-in" style="position: absolute; top: 70px; right: 16px; min-width: 280px; z-index: 1000;">
        <div class="md-p-lg">
            <div class="md-flex md-items-center md-gap-md md-mb-md">
                <span class="material-icons md-text-primary" style="font-size: 40px;">account_circle</span>
                <div>
                    <h4 id="userName" class="md-title-medium md-mb-xs">Loading...</h4>
                    <p id="userEmail" class="md-body-medium md-text-secondary">Loading...</p>
                </div>
            </div>
            <button id="signOutBtn" class="md-btn md-btn-error md-w-full">
                <span class="material-icons">logout</span>
                Sign Out
            </button>
        </div>
    </div>

    <!-- Main Content Container -->
    <div class="md-container md-mt-lg">
        <!-- Chart Section -->
        <div class="md-chart-container">
            <div class="md-flex md-items-center md-justify-between md-mb-lg">
                <h1 class="md-headline-large md-text-center">
                    <span class="material-icons md-text-primary">analytics</span> Alpha Gas Solution
                </h1>
                <button id="refreshChart" class="md-btn md-btn-outlined">
                    <span class="material-icons">refresh</span>
                    Refresh
                </button>
            </div>
            <p class="md-text-center md-text-secondary md-body-large md-mb-lg">Analysis Range Visualization & AI Calibration Check</p>

            <!-- Chart Legend -->
            <div class="md-flex md-gap-lg md-justify-center md-mb-lg">
                <div class="md-flex md-items-center md-gap-sm">
                    <div class="md-chip md-chip-selected">Current Capability</div>
                </div>
                <div class="md-flex md-items-center md-gap-sm">
                    <div class="md-chip md-primary-container">Target Range (All-Rounder)</div>
                </div>
            </div>

            <!-- Chart Container -->
            <div id="chart"></div>
            <div class="md-chart-scale"></div>
        </div>

        <!-- Add Analyte Form -->
        <div class="md-card md-mb-lg">
            <h2 class="md-title-large md-mb-lg">Add New Analyte to Chart</h2>
            <form id="addAnalyteForm">
                <div class="md-form-group">
                    <label for="analyteName" class="md-label">Analyte Name</label>
                    <input type="text" id="analyteName" class="md-input" required placeholder="Enter analyte name">
                    <div id="analyteNameError" class="md-text-error md-body-small md-mt-xs md-hidden"></div>
                </div>

                    <div class="md-form-group">
                        <h3 class="md-title-medium md-mb-md">Current Capabilities</h3>
                        <div id="currentRangesContainer" class="md-mb-md">
                            <!-- Current range inputs will be added here -->
                        </div>
                        <button type="button" class="md-btn md-btn-outlined md-btn-sm" onclick="addRangeInput('current')">
                            <span class="saas-icon-sm">➕</span>
                            <span class="saas-form-text">Add Current Range</span>
                        </button>
                    </div>

                    <div class="md-form-group">
                        <h3 class="md-title-medium md-mb-md">Target Ranges</h3>
                        <div id="targetRangesContainer" class="md-mb-md">
                            <!-- Target range inputs will be added here -->
                        </div>
                        <button type="button" class="md-btn md-btn-outlined md-btn-sm" onclick="addRangeInput('target')">
                            <span class="saas-icon-sm">➕</span>
                            <span class="saas-form-text">Add Target Range</span>
                        </button>
                    </div>
                    
                    <div class="md-form-group">
                        <label for="gapNotes" class="md-label">Gap Notes / Remarks</label>
                        <textarea id="gapNotes" class="md-textarea" placeholder="Add any notes or remarks about this analyte..."></textarea>
                    </div>

                    <!-- Save Status and Controls -->
                    <div class="saas-card saas-card-compact saas-bg-subtle">
                        <div class="saas-flex saas-items-center saas-justify-between md-mb-md">
                            <div id="authStatus" class="saas-flex saas-items-center saas-gap-sm">
                                <span id="authIndicator" class="saas-icon-base">⚡</span>
                                <span id="authStatusText" class="saas-status-text saas-text-secondary">Checking authentication...</span>
                            </div>
                            <div id="saveStatus" class="saas-flex saas-items-center saas-gap-sm hidden">
                                <span id="saveIndicator" class="saas-icon-base">💾</span>
                                <span id="saveStatusText" class="saas-status-text saas-text-secondary">Ready to save</span>
                            </div>
                        </div>
                        
                        <div class="saas-grid saas-grid-cols-1 md:saas-grid-cols-2 saas-gap-md">
                            <button type="submit" class="md-btn md-btn-filled" id="addAnalyteBtn">
                                <span class="saas-icon-sm">📊</span>
                                <span>Add Analyte to Chart</span>
                            </button>
                            <button type="button" class="md-btn md-btn-filled" id="confirmSaveBtn" title="Manually confirm data persistence to database">
                                <span id="saveButtonText">
                                    <span class="saas-icon-sm">💾</span>
                                    <span>Confirm Save to Database</span>
                                </span>
                            </button>
                        </div>
                        
                        <div class="saas-mt-md saas-p-md" style="background-color: var(--saas-bg-subtle); border-radius: var(--radius);">
                            <p class="saas-form-helper">
                                <strong>Guest Mode:</strong> Data saved locally only (lost on refresh)<br>
                                <strong>Authenticated:</strong> Data persisted to database + local cache
                            </p>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- AI Calibration Gas Analyzer Section -->
        <div class="saas-card">
            <div class="saas-card-header">
                <h2 class="saas-card-title">🔬 AI Calibration Gas Capability Analysis</h2>
            </div>
            <div class="saas-card-content">
                <div class="md-form-group">
                    <label for="calibrationGasStandardName" class="md-label">Calibration Gas Standard Name</label>
                    <input type="text" id="calibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix">
                </div>
                
                <div class="md-form-group">
                    <h3 class="md-title-medium md-mb-md">Components</h3>
                    <div id="calibrationCompoundsContainer" class="md-mb-md">
                        <!-- Compound input rows will be added here -->
                    </div>
                    <button type="button" id="addCalibrationCompoundBtn" class="saas-btn saas-btn-ghost">
                        ➕ Add Compound
                    </button>
                </div>
                
                <div id="calibrationInputError" class="saas-text-error saas-text-sm hidden"></div>
                
                <button type="button" id="analyzeCalibrationBtn" class="md-btn md-btn-filled md-w-full">
                    🔬 Analyze Calibration Standard
                </button>
                
                <div id="calibrationLoading" class="hidden">
                    <div class="saas-flex saas-items-center saas-justify-center saas-p-lg">
                        <div class="saas-fade-in">
                            <span class="text-2xl">🔬</span>
                        </div>
                        <span class="saas-ml-md saas-text-secondary">Analyzing, please wait...</span>
                    </div>
                </div>
                
                <div id="calibrationResults" class="saas-card saas-card-compact hidden saas-mt-lg" style="background-color: var(--saas-bg-subtle);">
                    <!-- AI analysis results will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="data.js"></script>
    <script type="module" src="supabase-client.js"></script>
    <script type="module" src="storage.js"></script>
    <script src="chart.js"></script>
    <script src="form-handler.js"></script>
    <script src="ai-analyzer.js"></script>
    <script src="app.js"></script>
    
    <!-- Main App Initialization -->
    <script type="module">
        import { authManager } from './auth.js';
        import { roleManager } from './role-manager.js';

        // Make authManager globally available for form-handler.js
        window.authManager = authManager;
        window.roleManager = roleManager;

        async function initializeAuthenticatedApp() {
            console.log('🚀 Initializing authenticated Alpha Gas Solution application...');
            
            try {
                // Check authentication with a more robust retry mechanism
                console.log('🔍 Checking authentication state...');
                let user = await authManager.init();
                
                // If no user initially, wait and retry multiple times
                if (!user) {
                    console.log('⏳ No user found initially, checking session with retries...');
                    
                    for (let attempt = 1; attempt <= 5; attempt++) {
                        console.log(`🔄 Authentication attempt ${attempt}/5...`);
                        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                        
                        // Try to get session directly
                        const { data: { session } } = await supabase.auth.getSession();
                        if (session && session.user) {
                            user = session.user;
                            authManager.currentUser = user;
                            console.log(`✅ Session found on attempt ${attempt}, user authenticated:`, user.email);
                            break;
                        }
                    }
                }
                
                if (!user) {
                    console.log('❌ Not authenticated after all attempts');
                    // Show authentication required message with manual redirect option
                    document.body.innerHTML = `
                        <div class="saas-min-h-screen saas-flex saas-items-center saas-justify-center saas-p-lg">
                            <div class="saas-card saas-text-center saas-max-w-md">
                                <h2 class="saas-heading-lg md-mb-md">🔐 Authentication Required</h2>
                                <p class="saas-text-secondary saas-mb-lg">Please sign in to access the Alpha Gas Solution dashboard</p>
                                <div class="saas-flex saas-gap-md saas-justify-center">
                                    <a href="./simple-auth-login.html" class="saas-btn saas-btn-primary">
                                        Go to Login
                                    </a>
                                    <button onclick="location.reload()" class="saas-btn saas-btn-secondary">
                                        Retry
                                    </button>
                                </div>
                                <p class="saas-text-xs saas-text-secondary saas-mt-md">
                                    If you just logged in, try clicking "Retry"
                                </p>
                            </div>
                        </div>
                    `;
                    return;
                }
                
                console.log(`✅ User authenticated: ${user.email}`);
                
                // Initialize role management
                await roleManager.init();
                const roleInfo = roleManager.setupUI();
                console.log(`👤 User role: ${roleManager.getRole()}, status: ${roleManager.getStatus()}`);
                
                // Setup user menu with role information
                setupUserMenu(user, roleInfo);
                
                // Load data from Supabase/localStorage
                try {
                    await loadDataFromLocalStorage();
                    console.log('✅ Data loaded successfully');
                } catch (error) {
                    console.error('❌ Error loading data:', error);
                }
                
                // Ensure authentication is fully ready before initializing form handler
                console.log('🔐 Ensuring authentication is ready before component initialization...');
                await authManager.ensureReady();
                console.log('✅ Authentication confirmed ready for component initialization');
                
                // Initialize all components
                drawScaleLabels();
                renderChart();
                initializeFormHandler();  // Now safe to initialize with auth ready
                initializeAIAnalyzer();
                
                // Add data management controls
                addDataManagementControls();
                
                // Add initial range inputs
                addRangeInput('current'); 
                addRangeInput('target'); 
                
                // Add initial compound row for calibration analyzer
                addCalibrationCompoundRow();
                
                console.log('✅ Alpha Gas Solution application initialized successfully');
                
            } catch (error) {
                console.error('❌ Error initializing app:', error);
                // Redirect to our working login page
                window.location.href = './simple-auth-login.html';
            }
        }

        function setupUserMenu(user, roleInfo) {
            const userMenu = document.getElementById('userMenu');
            const userMenuToggle = document.getElementById('userMenuToggle');
            const userName = document.getElementById('userName');
            const userEmail = document.getElementById('userEmail');
            const userInitial = document.getElementById('userInitial');
            const userMenuInitial = document.getElementById('userMenuInitial');
            const signOutBtn = document.getElementById('signOutBtn');

            // Set user info
            const name = user.user_metadata?.full_name || user.email.split('@')[0];
            const initial = name.charAt(0).toUpperCase();
            
            userName.textContent = name;
            userEmail.textContent = user.email;
            userInitial.textContent = initial;
            userMenuInitial.textContent = initial;

            // Add role information to user menu
            const roleInfo_display = document.createElement('div');
            roleInfo_display.className = 'text-xs text-gray-500 mb-2';
            roleInfo_display.innerHTML = `
                Role: <span class="font-medium">${roleManager.getRole().charAt(0).toUpperCase() + roleManager.getRole().slice(1)}</span><br>
                Status: <span class="font-medium">${roleManager.getStatus().charAt(0).toUpperCase() + roleManager.getStatus().slice(1)}</span>
            `;
            userMenu.querySelector('.flex.items-center').parentNode.insertBefore(roleInfo_display, userMenu.querySelector('hr'));

            // Show user menu toggle
            userMenuToggle.classList.remove('hidden');

            // Toggle menu visibility
            userMenuToggle.addEventListener('click', () => {
                userMenu.classList.toggle('hidden');
            });

            // Hide menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!userMenu.contains(e.target) && !userMenuToggle.contains(e.target)) {
                    userMenu.classList.add('hidden');
                }
            });

            // Sign out
            signOutBtn.addEventListener('click', async () => {
                try {
                    await authManager.signOut();
                    // Redirect to our working login page
                    window.location.href = './simple-auth-login.html';
                } catch (error) {
                    console.error('Sign out error:', error);
                }
            });
        }

        // Initialize the app
        initializeAuthenticatedApp();
    </script>
</body>
</html>
