// Main application initialization

document.addEventListener('DOMContentLoaded', async function() {
    console.log('🔧 Basic components initialization (non-authenticated parts)...');
    
    // Only initialize non-authenticated components here
    // Authentication-dependent initialization happens in app.html module script
    
    // Load initial data (will use cached/guest mode if not authenticated)
    try {
        await loadDataFromLocalStorage();
        console.log('✅ Initial data loaded');
    } catch (error) {
        console.error('❌ Error loading initial data:', error);
    }
    
    // Initialize basic chart components (non-authenticated)
    drawScaleLabels();
    renderChart();
    
    // Note: initializeFormHandler() is called in app.html after authentication
    // Note: initializeAIAnalyzer() is called in app.html after authentication
    
    console.log('✅ Basic components initialized (waiting for authentication)');
});
