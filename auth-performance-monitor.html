<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Performance Monitor</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-4">🔐 Authentication Performance Monitor</h1>
            <p class="text-gray-600 mb-6">Monitor authentication performance and identify bottlenecks in real-time</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900">Average Response Time</h3>
                    <p id="avgTime" class="text-2xl font-bold text-blue-600">-- ms</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <h3 class="font-semibold text-green-900">Success Rate</h3>
                    <p id="successRate" class="text-2xl font-bold text-green-600">--%</p>
                </div>
                <div class="bg-red-50 rounded-lg p-4">
                    <h3 class="font-semibold text-red-900">Timeout Rate</h3>
                    <p id="timeoutRate" class="text-2xl font-bold text-red-600">--%</p>
                </div>
            </div>
            
            <div class="flex gap-4 mb-6">
                <button id="startTest" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Start Performance Test
                </button>
                <button id="stopTest" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 hidden">
                    Stop Test
                </button>
                <button id="clearResults" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Clear Results
                </button>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">📊 Test Results</h2>
            <div id="results" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-500 italic">No tests run yet. Click "Start Performance Test" to begin.</p>
            </div>
        </div>
    </div>

    <script type="module">
        // Import auth manager
        let authManager;
        let testInterval;
        let testResults = [];
        let isTestRunning = false;

        try {
            const authModule = await import('./auth.js');
            authManager = authModule.authManager;
            console.log('✅ Auth manager loaded successfully');
        } catch (error) {
            console.error('❌ Failed to load auth manager:', error);
            updateResults('ERROR: Failed to load authentication module', 'text-red-600');
        }

        // DOM elements
        const startBtn = document.getElementById('startTest');
        const stopBtn = document.getElementById('stopTest');
        const clearBtn = document.getElementById('clearResults');
        const resultsDiv = document.getElementById('results');
        const avgTimeEl = document.getElementById('avgTime');
        const successRateEl = document.getElementById('successRate');
        const timeoutRateEl = document.getElementById('timeoutRate');

        // Test authentication performance
        async function testAuthPerformance() {
            const startTime = Date.now();
            const testId = `test-${Date.now()}`;
            
            try {
                // Create timeout promise
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Test timeout')), 3000);
                });
                
                // Test auth initialization
                const authPromise = authManager.init();
                
                // Race between auth and timeout
                const result = await Promise.race([authPromise, timeoutPromise]);
                const elapsed = Date.now() - startTime;
                
                const testResult = {
                    id: testId,
                    timestamp: new Date().toLocaleTimeString(),
                    duration: elapsed,
                    success: true,
                    timeout: false,
                    hasUser: !!result,
                    userEmail: result?.email || 'No user'
                };
                
                testResults.push(testResult);
                updateResults(`✅ [${testResult.timestamp}] Auth test completed in ${elapsed}ms - User: ${testResult.userEmail}`, 'text-green-600');
                
                return testResult;
                
            } catch (error) {
                const elapsed = Date.now() - startTime;
                const isTimeout = error.message === 'Test timeout';
                
                const testResult = {
                    id: testId,
                    timestamp: new Date().toLocaleTimeString(),
                    duration: elapsed,
                    success: false,
                    timeout: isTimeout,
                    error: error.message
                };
                
                testResults.push(testResult);
                
                if (isTimeout) {
                    updateResults(`⏱️ [${testResult.timestamp}] Auth test timed out after ${elapsed}ms`, 'text-yellow-600');
                } else {
                    updateResults(`❌ [${testResult.timestamp}] Auth test failed after ${elapsed}ms: ${error.message}`, 'text-red-600');
                }
                
                return testResult;
            }
        }

        // Update results display
        function updateResults(message, className = 'text-gray-800') {
            const resultElement = document.createElement('div');
            resultElement.className = `text-sm ${className} font-mono`;
            resultElement.textContent = message;
            
            // Clear the "no tests" message if this is the first result
            if (resultsDiv.querySelector('.text-gray-500')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // Update statistics
        function updateStats() {
            if (testResults.length === 0) return;
            
            const successfulTests = testResults.filter(t => t.success);
            const timeoutTests = testResults.filter(t => t.timeout);
            
            // Average time
            const avgTime = testResults.reduce((sum, t) => sum + t.duration, 0) / testResults.length;
            avgTimeEl.textContent = `${Math.round(avgTime)} ms`;
            
            // Success rate
            const successRate = (successfulTests.length / testResults.length) * 100;
            successRateEl.textContent = `${Math.round(successRate)}%`;
            
            // Timeout rate
            const timeoutRate = (timeoutTests.length / testResults.length) * 100;
            timeoutRateEl.textContent = `${Math.round(timeoutRate)}%`;
        }

        // Start performance testing
        function startPerformanceTest() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            startBtn.classList.add('hidden');
            stopBtn.classList.remove('hidden');
            
            updateResults('🚀 Starting performance test...', 'text-blue-600');
            
            // Run test immediately, then every 5 seconds
            testAuthPerformance().then(() => updateStats());
            
            testInterval = setInterval(async () => {
                await testAuthPerformance();
                updateStats();
            }, 5000);
        }

        // Stop performance testing
        function stopPerformanceTest() {
            if (!isTestRunning) return;
            
            isTestRunning = false;
            startBtn.classList.remove('hidden');
            stopBtn.classList.add('hidden');
            
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
            }
            
            updateResults('🛑 Performance test stopped', 'text-blue-600');
        }

        // Clear results
        function clearResults() {
            testResults = [];
            resultsDiv.innerHTML = '<p class="text-gray-500 italic">No tests run yet. Click "Start Performance Test" to begin.</p>';
            avgTimeEl.textContent = '-- ms';
            successRateEl.textContent = '--%';
            timeoutRateEl.textContent = '--%';
            updateResults('🧹 Results cleared', 'text-gray-600');
        }

        // Event listeners
        startBtn.addEventListener('click', startPerformanceTest);
        stopBtn.addEventListener('click', stopPerformanceTest);
        clearBtn.addEventListener('click', clearResults);

        // Initial status
        updateResults('🔄 Authentication performance monitor ready', 'text-blue-600');
    </script>
</body>
</html>
