<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test Suite</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">🧪 Authentication Flow Test Suite</h1>
            <p class="text-gray-600 mb-6">Comprehensive testing of all authentication scenarios and performance metrics</p>
            
            <!-- Status Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900">Test Status</h3>
                    <p id="testStatus" class="text-xl font-bold text-blue-600">Ready</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <h3 class="font-semibold text-green-900">Passed Tests</h3>
                    <p id="passedTests" class="text-xl font-bold text-green-600">0</p>
                </div>
                <div class="bg-red-50 rounded-lg p-4">
                    <h3 class="font-semibold text-red-900">Failed Tests</h3>
                    <p id="failedTests" class="text-xl font-bold text-red-600">0</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-900">Average Time</h3>
                    <p id="avgTime" class="text-xl font-bold text-yellow-600">--ms</p>
                </div>
            </div>
            
            <!-- Test Controls -->
            <div class="flex gap-4 mb-6">
                <button id="runAllTests" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                    🚀 Run All Tests
                </button>
                <button id="runQuickTest" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    ⚡ Quick Test
                </button>
                <button id="runStressTest" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                    💪 Stress Test
                </button>
                <button id="clearResults" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    🧹 Clear
                </button>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Detailed Results -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">📊 Test Results</h2>
                <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                    <p class="text-gray-500 italic">No tests run yet. Click a test button to begin.</p>
                </div>
            </div>
            
            <!-- Live Performance Monitoring -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">📈 Live Performance</h2>
                <div id="performanceMetrics" class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium">Auth Module Load</span>
                        <span id="authLoadTime" class="text-gray-600">--ms</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium">Session Check</span>
                        <span id="sessionCheckTime" class="text-gray-600">--ms</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium">Total Auth Time</span>
                        <span id="totalAuthTime" class="text-gray-600">--ms</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium">Timeout Effectiveness</span>
                        <span id="timeoutEffect" class="text-gray-600">--</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Scenarios -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4">🎯 Test Scenarios</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="border rounded p-4">
                    <h3 class="font-semibold mb-2">⚡ Speed Test</h3>
                    <p class="text-sm text-gray-600 mb-3">Tests if auth completes within 3 seconds</p>
                    <button onclick="runSpeedTest()" class="text-blue-600 hover:text-blue-800 text-sm">Run Test</button>
                </div>
                <div class="border rounded p-4">
                    <h3 class="font-semibold mb-2">🔄 Timeout Test</h3>
                    <p class="text-sm text-gray-600 mb-3">Verifies timeout protection works</p>
                    <button onclick="runTimeoutTest()" class="text-blue-600 hover:text-blue-800 text-sm">Run Test</button>
                </div>
                <div class="border rounded p-4">
                    <h3 class="font-semibold mb-2">🔁 Retry Test</h3>
                    <p class="text-sm text-gray-600 mb-3">Tests multiple auth attempts</p>
                    <button onclick="runRetryTest()" class="text-blue-600 hover:text-blue-800 text-sm">Run Test</button>
                </div>
                <div class="border rounded p-4">
                    <h3 class="font-semibold mb-2">📱 Mobile Test</h3>
                    <p class="text-sm text-gray-600 mb-3">Simulates slower mobile connection</p>
                    <button onclick="runMobileTest()" class="text-blue-600 hover:text-blue-800 text-sm">Run Test</button>
                </div>
                <div class="border rounded p-4">
                    <h3 class="font-semibold mb-2">🌐 Network Test</h3>
                    <p class="text-sm text-gray-600 mb-3">Tests various network conditions</p>
                    <button onclick="runNetworkTest()" class="text-blue-600 hover:text-blue-800 text-sm">Run Test</button>
                </div>
                <div class="border rounded p-4">
                    <h3 class="font-semibold mb-2">🔒 Security Test</h3>
                    <p class="text-sm text-gray-600 mb-3">Validates security measures</p>
                    <button onclick="runSecurityTest()" class="text-blue-600 hover:text-blue-800 text-sm">Run Test</button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Test Suite State
        let authManager;
        let testResults = [];
        let passedCount = 0;
        let failedCount = 0;
        let totalTimes = [];

        // Load authentication module
        try {
            const authModule = await import('./auth.js');
            authManager = authModule.authManager;
            addResult('✅ Authentication module loaded successfully', 'success');
            updateAuthLoadTime(50); // Placeholder
        } catch (error) {
            addResult('❌ Failed to load authentication module: ' + error.message, 'error');
        }

        // DOM Elements
        const testStatusEl = document.getElementById('testStatus');
        const passedTestsEl = document.getElementById('passedTests');
        const failedTestsEl = document.getElementById('failedTests');
        const avgTimeEl = document.getElementById('avgTime');
        const testResultsEl = document.getElementById('testResults');

        // Update UI functions
        function updateStatus(status) {
            testStatusEl.textContent = status;
        }

        function updateStats() {
            passedTestsEl.textContent = passedCount;
            failedTestsEl.textContent = failedCount;
            
            if (totalTimes.length > 0) {
                const avg = totalTimes.reduce((a, b) => a + b, 0) / totalTimes.length;
                avgTimeEl.textContent = Math.round(avg) + 'ms';
            }
        }

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = `text-sm font-mono p-2 rounded mb-1 ${getResultColor(type)}`;
            div.textContent = `[${timestamp}] ${message}`;
            
            // Clear placeholder if this is first result
            if (testResultsEl.querySelector('.text-gray-500')) {
                testResultsEl.innerHTML = '';
            }
            
            testResultsEl.appendChild(div);
            testResultsEl.scrollTop = testResultsEl.scrollHeight;
        }

        function getResultColor(type) {
            switch(type) {
                case 'success': return 'bg-green-100 text-green-800';
                case 'error': return 'bg-red-100 text-red-800';
                case 'warning': return 'bg-yellow-100 text-yellow-800';
                case 'info': return 'bg-blue-100 text-blue-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function updateAuthLoadTime(time) {
            document.getElementById('authLoadTime').textContent = time + 'ms';
        }

        function updateSessionCheckTime(time) {
            document.getElementById('sessionCheckTime').textContent = time + 'ms';
        }

        function updateTotalAuthTime(time) {
            document.getElementById('totalAuthTime').textContent = time + 'ms';
        }

        function updateTimeoutEffect(effective) {
            document.getElementById('timeoutEffect').textContent = effective ? '✅ Working' : '❌ Failed';
        }

        // Core Test Functions
        async function testAuthenticationSpeed() {
            const startTime = Date.now();
            
            try {
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Test timeout')), 3000);
                });
                
                const authPromise = authManager.init();
                const result = await Promise.race([authPromise, timeoutPromise]);
                
                const elapsed = Date.now() - startTime;
                totalTimes.push(elapsed);
                
                if (elapsed < 3000) {
                    addResult(`✅ Speed test passed: ${elapsed}ms (< 3000ms)`, 'success');
                    passedCount++;
                    return { success: true, time: elapsed, result };
                } else {
                    addResult(`⚠️ Speed test slow: ${elapsed}ms (>= 3000ms)`, 'warning');
                    failedCount++;
                    return { success: false, time: elapsed, reason: 'Too slow' };
                }
                
            } catch (error) {
                const elapsed = Date.now() - startTime;
                totalTimes.push(elapsed);
                
                if (error.message === 'Test timeout') {
                    addResult(`❌ Speed test failed: Timeout after ${elapsed}ms`, 'error');
                    failedCount++;
                    return { success: false, time: elapsed, reason: 'Timeout' };
                } else {
                    addResult(`❌ Speed test error: ${error.message}`, 'error');
                    failedCount++;
                    return { success: false, time: elapsed, reason: error.message };
                }
            }
        }

        async function testTimeoutProtection() {
            const startTime = Date.now();
            
            try {
                // Test with very short timeout to force timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Forced timeout')), 100);
                });
                
                const authPromise = authManager.init();
                await Promise.race([authPromise, timeoutPromise]);
                
                // If we get here, timeout didn't work
                addResult('❌ Timeout protection failed: Auth completed too quickly', 'error');
                failedCount++;
                return { success: false, reason: 'Timeout protection ineffective' };
                
            } catch (error) {
                const elapsed = Date.now() - startTime;
                
                if (error.message === 'Forced timeout' && elapsed < 200) {
                    addResult(`✅ Timeout protection working: Caught timeout in ${elapsed}ms`, 'success');
                    passedCount++;
                    updateTimeoutEffect(true);
                    return { success: true, time: elapsed };
                } else {
                    addResult(`❌ Timeout test failed: ${error.message}`, 'error');
                    failedCount++;
                    updateTimeoutEffect(false);
                    return { success: false, reason: error.message };
                }
            }
        }

        // Individual Test Functions
        window.runSpeedTest = async function() {
            updateStatus('Running Speed Test...');
            addResult('🚀 Starting speed test...', 'info');
            await testAuthenticationSpeed();
            updateStats();
            updateStatus('Ready');
        };

        window.runTimeoutTest = async function() {
            updateStatus('Running Timeout Test...');
            addResult('⏱️ Starting timeout protection test...', 'info');
            await testTimeoutProtection();
            updateStats();
            updateStatus('Ready');
        };

        window.runRetryTest = async function() {
            updateStatus('Running Retry Test...');
            addResult('🔁 Starting retry test...', 'info');
            
            for (let i = 1; i <= 3; i++) {
                addResult(`🔄 Retry attempt ${i}/3`, 'info');
                await testAuthenticationSpeed();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            updateStats();
            updateStatus('Ready');
        };

        window.runMobileTest = async function() {
            updateStatus('Running Mobile Test...');
            addResult('📱 Simulating mobile connection...', 'info');
            
            // Add artificial delay to simulate slower mobile
            const originalInit = authManager.init;
            authManager.init = async function() {
                await new Promise(resolve => setTimeout(resolve, 1000));
                return originalInit.call(this);
            };
            
            await testAuthenticationSpeed();
            
            // Restore original function
            authManager.init = originalInit;
            
            updateStats();
            updateStatus('Ready');
        };

        window.runNetworkTest = async function() {
            updateStatus('Running Network Test...');
            addResult('🌐 Testing various network conditions...', 'info');
            
            const delays = [0, 500, 1000, 2000];
            
            for (const delay of delays) {
                addResult(`📡 Testing with ${delay}ms network delay`, 'info');
                
                const originalInit = authManager.init;
                authManager.init = async function() {
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return originalInit.call(this);
                };
                
                await testAuthenticationSpeed();
                authManager.init = originalInit;
                
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            updateStats();
            updateStatus('Ready');
        };

        window.runSecurityTest = async function() {
            updateStatus('Running Security Test...');
            addResult('🔒 Running security validation...', 'info');
            
            try {
                // Test that auth manager exists and has required methods
                if (!authManager || typeof authManager.init !== 'function') {
                    throw new Error('Auth manager not properly initialized');
                }
                
                addResult('✅ Auth manager security check passed', 'success');
                passedCount++;
                
                // Test for proper error handling
                try {
                    await authManager.signIn('<EMAIL>', 'wrongpassword');
                    addResult('✅ Error handling test passed', 'success');
                    passedCount++;
                } catch (error) {
                    addResult('✅ Security error handling working correctly', 'success');
                    passedCount++;
                }
                
            } catch (error) {
                addResult(`❌ Security test failed: ${error.message}`, 'error');
                failedCount++;
            }
            
            updateStats();
            updateStatus('Ready');
        };

        // Main Test Functions
        document.getElementById('runAllTests').addEventListener('click', async () => {
            updateStatus('Running All Tests...');
            addResult('🧪 Starting comprehensive test suite...', 'info');
            
            await runSpeedTest();
            await new Promise(resolve => setTimeout(resolve, 500));
            await runTimeoutTest();
            await new Promise(resolve => setTimeout(resolve, 500));
            await runRetryTest();
            await new Promise(resolve => setTimeout(resolve, 500));
            await runSecurityTest();
            
            addResult('🎉 All tests completed!', 'success');
            updateStatus('Tests Complete');
        });

        document.getElementById('runQuickTest').addEventListener('click', async () => {
            updateStatus('Running Quick Test...');
            addResult('⚡ Starting quick validation...', 'info');
            
            await testAuthenticationSpeed();
            await testTimeoutProtection();
            
            addResult('⚡ Quick test completed!', 'success');
            updateStats();
            updateStatus('Ready');
        });

        document.getElementById('runStressTest').addEventListener('click', async () => {
            updateStatus('Running Stress Test...');
            addResult('💪 Starting stress test - 10 rapid auth checks...', 'info');
            
            for (let i = 1; i <= 10; i++) {
                addResult(`🔥 Stress test ${i}/10`, 'info');
                await testAuthenticationSpeed();
                // Very short delay between tests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            addResult('💪 Stress test completed!', 'success');
            updateStats();
            updateStatus('Ready');
        });

        document.getElementById('clearResults').addEventListener('click', () => {
            testResults = [];
            passedCount = 0;
            failedCount = 0;
            totalTimes = [];
            
            testResultsEl.innerHTML = '<p class="text-gray-500 italic">No tests run yet. Click a test button to begin.</p>';
            updateStats();
            updateStatus('Ready');
            
            // Reset performance metrics
            updateAuthLoadTime('--');
            updateSessionCheckTime('--');
            updateTotalAuthTime('--');
            updateTimeoutEffect('--');
        });

        // Initial status
        addResult('🔧 Authentication test suite initialized', 'info');
        updateStatus('Ready');
    </script>
</body>
</html>
