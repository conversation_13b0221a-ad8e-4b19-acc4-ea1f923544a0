// Authentication module for Gas Analysis Application
import { supabase } from './supabase-client.js';
import { performanceOptimizer } from './performance-optimizer.js';

export class AuthManager {
    constructor() {
        this.currentUser = null;
        this.userProfile = null;
        this.onAuthStateChange = null;
        this.initPromise = null;
        this.isInitialized = false;
    }

    // Initialize authentication with proper timing
    async init() {
        // Prevent multiple initializations
        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._performInit();
        return this.initPromise;
    }

    async _performInit() {
        if (!supabase) {
            console.warn('Supabase not available, skipping auth');
            this.isInitialized = true;
            return null;
        }

        try {
            // Check for existing session
            const { data: { session } } = await supabase.auth.getSession();
            if (session) {
                this.currentUser = session.user;
                console.log('Auth initialized with existing session:', session.user.email);
            }

            // Listen for auth state changes
            supabase.auth.onAuthStateChange((event, session) => {
                console.log('Auth state changed:', event, session?.user?.email);
                this.currentUser = session?.user || null;
                
                if (this.onAuthStateChange) {
                    this.onAuthStateChange(event, session);
                }
            });

            this.isInitialized = true;
            return this.currentUser;
        } catch (error) {
            console.error('Auth initialization error:', error);
            this.isInitialized = true;
            return null;
        }
    }

    // Ensure authentication is ready before operations
    async ensureReady() {
        if (!this.isInitialized) {
            await this.init();
        }
        
        // Additional check to ensure user data is available
        if (this.currentUser && !this.getUserId()) {
            console.warn('Authentication state inconsistent, refreshing...');
            await new Promise(resolve => setTimeout(resolve, 100)); // Brief wait
            
            if (!this.getUserId()) {
                throw new Error('Authentication not properly initialized');
            }
        }
        
        return this.isAuthenticated();
    }

    // Sign up with email and password
    async signUp(email, password, metadata = {}) {
        if (!supabase) {
            throw new Error('Authentication service not available');
        }

        try {
            const { data, error } = await supabase.auth.signUp({
                email,
                password,
                options: {
                    data: metadata
                }
            });

            if (error) throw error;
            return { user: data.user, error: null };
        } catch (error) {
            console.error('Signup error:', error);
            return { user: null, error: error.message };
        }
    }

    // Sign in with email and password
    async signIn(email, password) {
        if (!supabase) {
            throw new Error('Authentication service not available');
        }

        try {
            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;
            return { user: data.user, error: null };
        } catch (error) {
            console.error('Signin error:', error);
            return { user: null, error: error.message };
        }
    }

    // Sign in with Google
    async signInWithGoogle() {
        try {
            const { data, error } = await supabase.auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: window.location.origin
                }
            });

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Google signin error:', error);
            return { error: error.message };
        }
    }

    // Sign out
    async signOut() {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
            
            this.currentUser = null;
            return { error: null };
        } catch (error) {
            console.error('Signout error:', error);
            return { error: error.message };
        }
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Get user ID for database operations
    getUserId() {
        return this.currentUser?.id || null;
    }

    // Send password reset email
    async resetPassword(email) {
        try {
            const { error } = await supabase.auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/reset-password`
            });

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Password reset error:', error);
            return { error: error.message };
        }
    }

    // Update user profile
    async updateProfile(updates) {
        try {
            const { data, error } = await supabase.auth.updateUser({
                data: updates
            });

            if (error) throw error;
            return { user: data.user, error: null };
        } catch (error) {
            console.error('Profile update error:', error);
            return { user: null, error: error.message };
        }
    }

    // Enhanced getUserProfile with caching
    async getUserProfile() {
        const user = supabase.auth.getUser ? await supabase.auth.getUser() : null;
        if (!user?.data?.user?.id) return null;

        const userId = user.data.user.id;
        
        // Use performance optimizer for caching
        try {
            return await performanceOptimizer.getCachedProfile(userId);
        } catch (error) {
            // Fallback to direct database query
            console.warn('Cache failed, falling back to direct query:', error);
            return await this._fetchProfileDirect(userId);
        }
    }

    // Direct profile fetch without caching (fallback)
    async _fetchProfileDirect(userId) {
        const { data: profile, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('user_id', userId)
            .single();

        if (error && error.code !== 'PGRST116') {
            console.error('Error fetching user profile:', error);
            return null;
        }

        return profile;
    }

    // Enhanced role checking with caching
    async getUserRole() {
        const user = supabase.auth.getUser ? await supabase.auth.getUser() : null;
        if (!user?.data?.user?.id) return 'guest';

        const userId = user.data.user.id;
        
        try {
            return await performanceOptimizer.getCachedRole(userId);
        } catch (error) {
            console.warn('Cached role check failed, falling back:', error);
            const profile = await this._fetchProfileDirect(userId);
            return profile ? profile.role : 'guest';
        }
    }

    // Clear user cache when status changes
    async clearUserCache(userId = null) {
        if (!userId) {
            const user = supabase.auth.getUser ? await supabase.auth.getUser() : null;
            userId = user?.data?.user?.id;
        }
        
        if (userId) {
            performanceOptimizer.clearUserCache(userId);
        }
    }

    // Check if user has specific role
    async hasRole(role) {
        const profile = await this.getUserProfile();
        return profile && profile.role === role && profile.status === 'approved';
    }

    // Check if user is admin
    async isAdmin() {
        return await this.hasRole('admin');
    }

    // Check if user is approved
    async isApproved() {
        const profile = await this.getUserProfile();
        return profile && profile.status === 'approved';
    }

    // Check if user is pending approval
    async isPending() {
        const profile = await this.getUserProfile();
        return profile && profile.status === 'pending';
    }

    // Get user status
    async getUserStatus() {
        const profile = await this.getUserProfile();
        return profile ? profile.status : 'guest';
    }

    // Refresh user profile data
    async refreshProfile() {
        this.userProfile = null;
        return await this.getUserProfile();
    }
}

// Create global auth manager instance
export const authManager = new AuthManager();
