<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test - Alpha Gas Solution</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
</head>
<body>
    <div class="md-flex md-items-center md-justify-center" style="min-height: 100vh; padding: 24px;">
        <div class="md-card md-max-w-lg md-w-full">
            <h1 class="md-headline-medium md-text-center md-mb-lg">
                <span class="material-icons md-text-google-blue">verified</span>
                Authentication Test
            </h1>
            
            <div id="test-results" class="md-mb-lg">
                <p class="md-body-large md-text-center md-text-secondary">
                    Testing authentication system...
                </p>
            </div>
            
            <div class="md-flex md-gap-md">
                <button onclick="testLogin()" class="md-btn md-btn-filled md-flex-1">
                    Test Login
                </button>
                <button onclick="testLogout()" class="md-btn md-btn-outlined md-flex-1">
                    Test Logout
                </button>
                <button onclick="checkSession()" class="md-btn md-btn-text md-flex-1">
                    Check Session
                </button>
            </div>
            
            <div class="md-mt-lg">
                <button onclick="goToLoginPage()" class="md-btn md-btn-secondary md-w-full">
                    Go to Login Page
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function updateResults(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            let statusClass = 'md-status-info';
            let icon = 'info';
            
            if (type === 'success') {
                statusClass = 'md-status-success';
                icon = 'check_circle';
            } else if (type === 'error') {
                statusClass = 'md-status-error';
                icon = 'error';
            }
            
            resultsDiv.innerHTML = `
                <div class="md-card md-card-compact ${statusClass}">
                    <div class="md-flex md-items-center md-gap-sm">
                        <span class="material-icons">${icon}</span>
                        <div>${message}</div>
                    </div>
                </div>
            `;
        }

        async function testLogin() {
            updateResults('Testing login with sample credentials...', 'info');
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'testpassword123'
                });

                if (error) {
                    updateResults(`❌ Login Failed: ${error.message}`, 'error');
                } else {
                    updateResults(`✅ Login Successful! Welcome ${data.user.email}`, 'success');
                }
            } catch (error) {
                updateResults(`❌ Login Error: ${error.message}`, 'error');
            }
        }

        async function testLogout() {
            updateResults('Testing logout...', 'info');
            
            try {
                const { error } = await supabase.auth.signOut();
                
                if (error) {
                    updateResults(`❌ Logout Failed: ${error.message}`, 'error');
                } else {
                    updateResults('✅ Logout Successful!', 'success');
                }
            } catch (error) {
                updateResults(`❌ Logout Error: ${error.message}`, 'error');
            }
        }

        async function checkSession() {
            updateResults('Checking current session...', 'info');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    updateResults(`✅ Active Session: ${session.user.email}`, 'success');
                } else {
                    updateResults('ℹ️ No active session found', 'info');
                }
            } catch (error) {
                updateResults(`❌ Session Check Error: ${error.message}`, 'error');
            }
        }

        function goToLoginPage() {
            window.location.href = 'simple-auth-login.html';
        }

        // Check session on load
        window.addEventListener('load', checkSession);
    </script>
</body>
</html>
