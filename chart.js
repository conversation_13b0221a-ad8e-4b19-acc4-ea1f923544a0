// Chart rendering functions

// Helper Functions
function logPosition(valuePPM) {
    if (valuePPM === null || valuePPM <= 0) valuePPM = CONFIG.minScalePPM;
    const minLog = Math.log10(CONFIG.minScalePPM);
    const maxLog = Math.log10(CONFIG.maxScalePPM);
    const valLog = Math.log10(valuePPM);
    return ((valLog - minLog) / (maxLog - minLog)) * 100;
}

function formatLabel(ppm) {
    if (ppm === null) return "";
    if (ppm < 0.000001) return (ppm * 1000000000).toFixed(3) + " ppt";
    if (ppm < 1) return (ppm * 1000).toFixed(3) + " ppb";
    if (ppm >= 1000000) return (ppm / 10000).toFixed(1) + "%"; 
    if (ppm >= 1000) return (ppm / 10000).toFixed(2) + "%"; 
    return ppm.toFixed(1) + " ppm";
}

// Chart Rendering
const chartElement = document.getElementById('chart');
const scaleAxisElement = document.querySelector('.md-chart-scale');

function drawScaleLabels() {
    if (!scaleAxisElement) {
        console.warn('Scale axis element not found');
        return;
    }
    scaleAxisElement.innerHTML = ''; 
    scaleAxisElement.classList.add('md-chart-scale');
    
    const scalePoints = [
        { value: 0.001, label: "1 ppb" }, 
        { value: 0.01, label: "10 ppb" }, 
        { value: 0.1, label: "0.1 ppm" },
        { value: 1, label: "1 ppm" }, 
        { value: 10, label: "10 ppm" }, 
        { value: 100, label: "100 ppm" },
        { value: 1000, label: "0.1%" }, 
        { value: 10000, label: "1%" }, 
        { value: 100000, label: "10%" },
        { value: 1000000, label: "100%" }
    ];
    scalePoints.forEach(point => {
        const posPercent = logPosition(point.value);
        if (posPercent >=0 && posPercent <=100) {
            const labelDiv = document.createElement('div');
            labelDiv.classList.add('md-scale-label');
            labelDiv.style.left = `${posPercent}%`;
            labelDiv.textContent = point.label;
            scaleAxisElement.appendChild(labelDiv);

            const tickDiv = document.createElement('div');
            tickDiv.classList.add('md-scale-tick');
            tickDiv.style.left = `${posPercent}%`;
            scaleAxisElement.appendChild(tickDiv);
        }
    });
}

function renderChart() {
    if (!chartElement) {
        console.warn('Chart element not found');
        return;
    }
    
    chartElement.innerHTML = ''; 
    
    if (!gasData || gasData.length === 0) {
        chartElement.innerHTML = '<div class="md-text-center md-text-secondary md-p-lg">No gas data available</div>';
        return;
    }
    
    gasData.forEach((gas, index) => {
        const row = document.createElement('div');
        row.classList.add('md-gas-row');

        const nameDiv = document.createElement('div');
        nameDiv.classList.add('md-gas-name');
        nameDiv.textContent = gas.name;
        
        // Add delete button for custom analytes (not for original ones)
        if (index >= getOriginalAnalytesCount()) {
            const deleteBtn = document.createElement('button');
            deleteBtn.classList.add('delete-analyte-btn');
            deleteBtn.innerHTML = '🗑️';
            deleteBtn.title = 'Delete this analyte';
            deleteBtn.onclick = () => deleteAnalyte(index);
            nameDiv.appendChild(deleteBtn);
        }
        
        row.appendChild(nameDiv);

        const barArea = document.createElement('div');
        barArea.classList.add('md-bar-area');

        if (gas.target && gas.target.length > 0) {
            gas.target.forEach(range => {
                const targetMinPos = logPosition(range.min === null ? CONFIG.minScalePPM : parseFloat(range.min));
                const targetMaxPos = logPosition(range.max === null ? CONFIG.maxScalePPM : parseFloat(range.max));
                if (targetMinPos < targetMaxPos && targetMaxPos <= 100 && targetMinPos >= 0) {
                    const targetBar = document.createElement('div');
                    targetBar.classList.add('md-target-bar');
                    targetBar.style.left = `${targetMinPos}%`;
                    targetBar.style.width = `${targetMaxPos - targetMinPos}%`;
                    const tooltip = document.createElement('span');
                    tooltip.classList.add('md-tooltip');
                    tooltip.textContent = `Target: ${range.label || (formatLabel(parseFloat(range.min)) + " - " + formatLabel(parseFloat(range.max)))}`;
                    targetBar.appendChild(tooltip);
                    barArea.appendChild(targetBar);
                }
            });
        }

        if (gas.current && gas.current.length > 0) {
            gas.current.forEach(range => {
                const currentMinPos = logPosition(range.min === null ? CONFIG.minScalePPM : parseFloat(range.min));
                const currentMaxPos = logPosition(range.max === null ? CONFIG.maxScalePPM : parseFloat(range.max));
                if (currentMinPos < currentMaxPos && currentMaxPos <= 100 && currentMinPos >= 0) {
                    const currentBar = document.createElement('div');
                    currentBar.classList.add('md-current-bar');
                    currentBar.style.left = `${currentMinPos}%`;
                    currentBar.style.width = `${currentMaxPos - currentMinPos}%`;
                    const tooltip = document.createElement('span');
                    tooltip.classList.add('md-tooltip');
                    tooltip.textContent = `Current: ${range.label || (formatLabel(parseFloat(range.min)) + " - " + formatLabel(parseFloat(range.max)))}`;
                    currentBar.appendChild(tooltip);
                    barArea.appendChild(currentBar);
                }
            });
        }
        row.appendChild(barArea);

        const notesSection = document.createElement('div'); 
        notesSection.classList.add('md-gap-notes'); 

        const notesText = document.createElement('div'); 
        notesText.classList.add('md-gap-notes-text');
        notesText.textContent = gas.gapNotes || "No notes provided.";
        notesSection.appendChild(notesText); 

        row.appendChild(notesSection); 

        chartElement.appendChild(row);
    });
}

// Helper function to get the count of original analytes
function getOriginalAnalytesCount() {
    // This matches the number of analytes in the original data.js file
    return 14;
}

// Delete an analyte by index
async function deleteAnalyte(index) {
    const analyte = gasData[index];
    if (confirm(`Are you sure you want to delete "${analyte.name}"?`)) {
        try {
            // Import supabase functions if available
            if (typeof supabaseStorage !== 'undefined') {
                await supabaseStorage.deleteAnalyte(analyte.name);
            }
            
            gasData.splice(index, 1);
            await saveDataToLocalStorage();
            renderChart();
        } catch (error) {
            console.error('Error deleting analyte:', error);
            // Still allow local deletion even if Supabase fails
            gasData.splice(index, 1);
            await saveDataToLocalStorage();
            renderChart();
        }
    }
}

// Add data management controls
function addDataManagementControls() {
    const chartContainer = document.querySelector('.chart-container');
    if (!chartContainer || document.getElementById('dataManagementControls')) {
        return; // Already added or container doesn't exist
    }
    
    const controlsDiv = document.createElement('div');
    controlsDiv.id = 'dataManagementControls';
    controlsDiv.classList.add('data-management-controls');
    controlsDiv.innerHTML = `
        <div class="data-controls-section">
            <h3 class="md-headline-small md-text-secondary">Data Management</h3>
            <div class="data-controls-buttons">
                <button onclick="exportData()" class="md-btn md-btn-outlined data-control-btn export-btn">📥 Export Data</button>
                <label for="importFile" class="md-btn md-btn-outlined data-control-btn import-btn">📤 Import Data</label>
                <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                <button onclick="resetToDefaultData()" class="md-btn md-btn-tonal data-control-btn reset-btn">🔄 Reset to Defaults</button>
            </div>
        </div>
    `;
    
    chartContainer.appendChild(controlsDiv);
}

// Enhanced Chart Rendering with Visual Effects
function renderEnhancedChart() {
    if (!chartElement) {
        console.warn('Chart element not found');
        return;
    }
    
    // Add loading animation
    chartElement.innerHTML = '<div class="md-loading"><div class="md-loading-spinner"></div>Rendering chart...</div>';
    
    setTimeout(() => {
        chartElement.innerHTML = ''; 
        
        if (!gasData || gasData.length === 0) {
            chartElement.innerHTML = '<div class="md-text-center md-text-secondary md-p-lg">No gas data available</div>';
            return;
        }
        
        gasData.forEach((gas, index) => {
            const row = document.createElement('div');
            row.classList.add('md-gas-row', 'md-fade-in');
            
            // Add staggered animation delay
            row.style.animationDelay = `${index * 50}ms`;

            const nameDiv = document.createElement('div');
            nameDiv.classList.add('md-gas-name');
            nameDiv.textContent = gas.name;
            
            // Add delete button for custom analytes with enhanced styling
            if (index >= getOriginalAnalytesCount()) {
                const deleteBtn = document.createElement('button');
                deleteBtn.classList.add('delete-analyte-btn', 'md-btn', 'md-btn-text');
                deleteBtn.innerHTML = '🗑️';
                deleteBtn.title = 'Delete this analyte';
                deleteBtn.onclick = () => deleteAnalyte(index);
                nameDiv.appendChild(deleteBtn);
            }
            
            row.appendChild(nameDiv);

            const barArea = document.createElement('div');
            barArea.classList.add('md-bar-area');

            // Enhanced target ranges with gradient backgrounds
            if (gas.target && gas.target.length > 0) {
                gas.target.forEach((range, rangeIndex) => {
                    const targetMinPos = logPosition(range.min === null ? CONFIG.minScalePPM : parseFloat(range.min));
                    const targetMaxPos = logPosition(range.max === null ? CONFIG.maxScalePPM : parseFloat(range.max));
                    
                    const targetBar = document.createElement('div');
                    targetBar.classList.add('md-target-bar', 'enhanced-target-bar');
                    targetBar.style.left = `${Math.min(targetMinPos, targetMaxPos)}%`;
                    targetBar.style.width = `${Math.abs(targetMaxPos - targetMinPos)}%`;
                    
                    // Enhanced gradient based on range index
                    const gradients = [
                        'linear-gradient(135deg, rgba(5, 150, 105, 0.8) 0%, rgba(37, 99, 235, 0.8) 100%)',
                        'linear-gradient(135deg, rgba(37, 99, 235, 0.8) 0%, rgba(168, 85, 247, 0.8) 100%)',
                        'linear-gradient(135deg, rgba(168, 85, 247, 0.8) 0%, rgba(5, 150, 105, 0.8) 100%)'
                    ];
                    targetBar.style.background = gradients[rangeIndex % gradients.length];
                    targetBar.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)';
                    targetBar.style.border = '1px solid rgba(255, 255, 255, 0.1)';
                    
                    // Add hover effects
                    targetBar.addEventListener('mouseenter', () => {
                        targetBar.style.transform = 'translateY(-2px) scale(1.02)';
                        targetBar.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)';
                    });
                    
                    targetBar.addEventListener('mouseleave', () => {
                        targetBar.style.transform = 'translateY(0) scale(1)';
                        targetBar.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)';
                    });

                    // Enhanced tooltip with better formatting
                    const tooltipText = `Target Range ${rangeIndex + 1}: ${formatLabel(range.min)} - ${formatLabel(range.max)}`;
                    targetBar.innerHTML = `<span class="saas-tooltip enhanced-tooltip">${tooltipText}</span>`;
                    
                    barArea.appendChild(targetBar);
                });
            }

            // Enhanced current value bar with pulsing animation
            if (gas.current !== null && gas.current !== undefined) {
                const currentPos = logPosition(parseFloat(gas.current));
                
                const currentBar = document.createElement('div');
                currentBar.classList.add('md-current-bar', 'enhanced-current-bar');
                currentBar.style.left = `${currentPos}%`;
                
                // Enhanced gradient with animated glow
                currentBar.style.background = 'linear-gradient(135deg, #E54D2E 0%, #DC2626 50%, #B91C1C 100%)';
                currentBar.style.boxShadow = '0 0 20px rgba(229, 77, 46, 0.6), 0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3)';
                currentBar.style.animation = 'pulseGlow 2s ease-in-out infinite alternate';
                
                // Add enhanced hover effects
                currentBar.addEventListener('mouseenter', () => {
                    currentBar.style.transform = 'translateY(-3px) scale(1.1)';
                    currentBar.style.boxShadow = '0 0 30px rgba(229, 77, 46, 0.8), 0 4px 16px rgba(0, 0, 0, 0.4)';
                    currentBar.style.animation = 'none';
                });
                
                currentBar.addEventListener('mouseleave', () => {
                    currentBar.style.transform = 'translateY(0) scale(1)';
                    currentBar.style.boxShadow = '0 0 20px rgba(229, 77, 46, 0.6), 0 2px 8px rgba(0, 0, 0, 0.3)';
                    currentBar.style.animation = 'pulseGlow 2s ease-in-out infinite alternate';
                });

                // Enhanced tooltip
                const tooltipText = `Current: ${formatLabel(gas.current)}`;
                currentBar.innerHTML = `<span class="saas-tooltip enhanced-tooltip">${tooltipText}</span>`;
                
                barArea.appendChild(currentBar);
            }

            row.appendChild(barArea);

            // Enhanced gap notes with better styling
            const gapNotesDiv = document.createElement('div');
            gapNotesDiv.classList.add('saas-gap-notes', 'enhanced-gap-notes');
            
            if (gas.notes && gas.notes.trim() !== '') {
                gapNotesDiv.innerHTML = `<div class="saas-gap-notes-text enhanced-notes-text">${gas.notes}</div>`;
            } else {
                gapNotesDiv.innerHTML = `<div class="saas-gap-notes-text enhanced-notes-text saas-text-muted">No notes</div>`;
            }
            
            row.appendChild(gapNotesDiv);
            chartElement.appendChild(row);
        });
        
        // Add enhanced scale labels
        drawEnhancedScaleLabels();
    }, 150); // Small delay for loading effect
}

// Enhanced scale labels with better visual effects
function drawEnhancedScaleLabels() {
    if (!scaleAxisElement) {
        console.warn('Scale axis element not found');
        return;
    }
    scaleAxisElement.innerHTML = ''; 
    scaleAxisElement.classList.add('md-chart-scale', 'enhanced-chart-scale');
    
    const scalePoints = [
        { value: 0.001, label: "1 ppb" }, 
        { value: 0.01, label: "10 ppb" }, 
        { value: 0.1, label: "0.1 ppm" },
        { value: 1, label: "1 ppm" }, 
        { value: 10, label: "10 ppm" }, 
        { value: 100, label: "100 ppm" },
        { value: 1000, label: "0.1%" }, 
        { value: 10000, label: "1%" }, 
        { value: 100000, label: "10%" },
        { value: 1000000, label: "100%" }
    ];
    
    scalePoints.forEach((point, index) => {
        const posPercent = logPosition(point.value);
        if (posPercent >= 0 && posPercent <= 100) {
            const labelDiv = document.createElement('div');
            labelDiv.classList.add('md-scale-label', 'enhanced-scale-label');
            labelDiv.style.left = `${posPercent}%`;
            labelDiv.textContent = point.label;
            labelDiv.style.animationDelay = `${index * 100}ms`;
            scaleAxisElement.appendChild(labelDiv);

            const tickDiv = document.createElement('div');
            tickDiv.classList.add('md-scale-tick', 'enhanced-scale-tick');
            tickDiv.style.left = `${posPercent}%`;
            tickDiv.style.animationDelay = `${index * 100}ms`;
            scaleAxisElement.appendChild(tickDiv);
        }
    });
}

// Replace the original renderChart function
function renderChart() {
    renderEnhancedChart();
}

// ...existing code...
