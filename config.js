// Configuration settings
const CONFIG = {
    // Chart configuration
    minScalePPM: 0.001,
    maxScalePPM: 1000000,
    
    // API configuration - DO NOT PUT API KEY HERE IN PRODUCTION
    // Use environment variables or server-side proxy instead
    apiConfig: {
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        // The API key should be handled server-side or through environment variables
        // For development only - replace with proper authentication
        getApiKey: function() {
            // In production, this should call your backend API
            // that handles the authentication with Google's API
            return process.env.GEMINI_API_KEY || 'your-api-key-here';
        }
    }
};
