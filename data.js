// Initial Gas Data (Your Current Capabilities)
let gasData = [
     {
        name: "Hydrocarbons (Total/Generic)",
        current: [{ min: 1, max: 100, label: "1-100 ppm (GC1-FID, unvalidated)" }],
        target: [{ min: 0.01, max: 1000000, label: "0.01 ppm - 100%" }],
        gapNotes: "Speciation needed (VOCs). Current FID unvalidated. Broader range for pure components & trace analysis."
    },
    {
        name: "SO2",
        current: [
            { min: 1, max: 50, label: "<50 ppm (GC2-FPD, saturated at 50ppm)" },
            { min: 1000, max: 50000, label: "0.1% - 5% (GC1-TCD)" }
        ],
        target: [{ min: 1, max: 500000, label: "1 ppm - 50%" }],
        gapNotes: "Gap between FPD saturation (~50ppm) and TCD lower limit (1000ppm). Lower detection for CEMS."
    },
    {
        name: "H2S",
        current: [
            { min: 1, max: 30, label: "<30 ppm (GC2-FPD, saturated at 30ppm)" },
            { min: 500, max: 60000, label: "0.05% - >6% (GC1-TCD)" }
        ],
        target: [{ min: 1, max: 200000, label: "1 ppm - 20%" }],
        gapNotes: "Gap between FPD saturation (~30ppm) and TCD lower limit (500ppm)."
    },
    { 
        name: "O2", 
        current: [{ min: 1000, max: 980000, label: "0.1% - 98% (GC1-TCD)"}], 
        target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], 
        gapNotes: "Need ppb/low ppm for UHP." 
    },
    { 
        name: "CH4", 
        current: [
            { min: 8, max: 30000, label: "8ppm - 3% (GC1-FID)" }, 
            { min: 30000, max: 834000, label: "3% - 83.4% (GC1-TCD)" }
        ], 
        target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], 
        gapNotes: "Lower detection for trace." 
    },
    { 
        name: "CO2", 
        current: [{ min: 5000, max: 150000, label: "≥0.5% (Teledyne, max std 15%)" }], 
        target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], 
        gapNotes: "GC1-TCD not operational." 
    },
    { 
        name: "CO", 
        current: [{ min: 5000, max: 100000, label: "≥0.5% (Teledyne, max std 10%)" }], 
        target: [{ min: 1, max: 100000, label: "1 ppm - 10%" }], 
        gapNotes: "GC1-TCD not operational." 
    },
    { 
        name: "N2O", 
        current: [{ min: 5000, max: 500000, label: "≥0.5% (Teledyne, Entonox)" }], 
        target: [{ min: 0.1, max: 500000, label: "0.1 ppm - 50%" }], 
        gapNotes: "Need lower for CEMS." 
    },
    { 
        name: "N2", 
        current: [], 
        target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], 
        gapNotes: "Teledyne N2 overlap issues." 
    },
    { 
        name: "NO", 
        current: [{ min: 1, max: 5000, label: "0-5000 ppm (Teledyne)" }], 
        target: [{ min: 1, max: 10000, label: "1 ppm - 1%" }], 
        gapNotes: "Good for CEMS." 
    },
    { 
        name: "NO2", 
        current: [{ min: 1, max: 5000, label: "0-5000 ppm (Teledyne)" }], 
        target: [{ min: 1, max: 500, label: "1 ppm - 500 ppm" }], 
        gapNotes: "Good for CEMS." 
    },
    { 
        name: "H2", 
        current: [], 
        target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], 
        gapNotes: "Major gap." 
    },
    { 
        name: "Argon (Ar)", 
        current: [], 
        target: [{ min: 1, max: 1000000, label: "1 ppm - 100%" }], 
        gapNotes: "Critical, N2 overlap." 
    },
    { 
        name: "VOCs (Speciated)", 
        current: [], 
        target: [{ min: 0.001, max: 100000, label: "1 ppb - Various %" }], 
        gapNotes: "Requires GC-MS." 
    },
];
