<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Analyzer Debug - Alpha Gas Solution</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .debug-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
            background: #18181b;
            border: 1px solid #3f3f46;
            border-radius: 12px;
        }
        .debug-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #27272a;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .status-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            background: #1f1f23;
            border-radius: 6px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
        }
        .status-pass { background-color: #10b981; }
        .status-fail { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .error-text { color: #ff4444; }
        .success-text { color: #44ff44; }
        .info-text { color: #4444ff; }
        .warning-text { color: #ffaa00; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="header-title">AI Analyzer Debug Console</h1>
        <p class="header-subtitle">Comprehensive debugging for Alpha Gas Solution AI Analysis</p>

        <!-- Step 1: Script Loading Check -->
        <div class="debug-section">
            <h2 class="text-xl font-bold text-white mb-4">📋 Step 1: Script Loading Status</h2>
            <div class="status-grid" id="scriptStatus">
                <!-- Script status will be populated here -->
            </div>
        </div>

        <!-- Step 2: DOM Elements Check -->
        <div class="debug-section">
            <h2 class="text-xl font-bold text-white mb-4">🔍 Step 2: DOM Elements Availability</h2>
            <div class="status-grid" id="domStatus">
                <!-- DOM status will be populated here -->
            </div>
        </div>

        <!-- Step 3: Function Availability -->
        <div class="debug-section">
            <h2 class="text-xl font-bold text-white mb-4">⚙️ Step 3: Function Availability</h2>
            <div class="status-grid" id="functionStatus">
                <!-- Function status will be populated here -->
            </div>
        </div>

        <!-- Step 4: Event Listener Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold text-white mb-4">🎯 Step 4: Event Listener Test</h2>
            <div class="status-grid" id="eventStatus">
                <!-- Event status will be populated here -->
            </div>
        </div>

        <!-- Step 5: Interactive Test Area -->
        <div class="debug-section">
            <h2 class="text-xl font-bold text-white mb-4">🧪 Step 5: Interactive Test Area</h2>
            <button class="analyzer-btn mb-4" onclick="runInteractiveTest()">🔬 Run Interactive Test</button>
            
            <!-- AI Analyzer Interface -->
            <div class="calibration-analyzer-container">
                <div class="analyzer-section">
                    <div>
                        <label for="calibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                        <input type="text" id="calibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix" value="Debug Test Standard">
                    </div>
                    
                    <h3 class="text-lg font-medium text-zinc-300 mt-4 mb-2">Components:</h3>
                    <div id="calibrationCompoundsContainer">
                        <!-- Compound input rows will be added here -->
                    </div>
                    <button type="button" id="addCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
                    <div id="calibrationInputError" class="error-message" style="display: none;"></div>
                    <button type="button" id="analyzeCalibrationBtn" class="analyzer-btn">🔬 Debug Analyze Button</button>
                </div>
                <div id="calibrationLoading" class="text-sm text-zinc-400 my-2" style="display: none;">Debug testing...</div>
                <div id="calibrationResults">
                    <!-- Results will be displayed here -->
                </div>
            </div>
        </div>

        <!-- Step 6: Console Output -->
        <div class="debug-section">
            <h2 class="text-xl font-bold text-white mb-4">📊 Step 6: Console Output</h2>
            <div class="console-output" id="consoleOutput">
                Starting debug session...\n
            </div>
            <button class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onclick="clearConsole()">Clear Console</button>
        </div>
    </div>

    <!-- Load Scripts in Order -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="ai-analyzer.js"></script>

    <script>
        // Console capture system
        const consoleOutput = document.getElementById('consoleOutput');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'info-text',
                error: 'error-text',
                success: 'success-text',
                warning: 'warning-text'
            };
            
            const span = document.createElement('span');
            span.className = colors[type] || 'info-text';
            span.textContent = `[${timestamp}] ${message}\n`;
            consoleOutput.appendChild(span);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            logToConsole(args.join(' '), 'info');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logToConsole(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            logToConsole(args.join(' '), 'warning');
            originalWarn.apply(console, args);
        };

        function clearConsole() {
            consoleOutput.innerHTML = 'Console cleared...\n';
        }

        // Debug test functions
        function createStatusItem(label, status, details = '') {
            const statusClass = status ? 'status-pass' : 'status-fail';
            const statusText = status ? 'PASS' : 'FAIL';
            
            return `
                <div class="status-item">
                    <div class="status-indicator ${statusClass}"></div>
                    <div>
                        <div class="text-white font-medium">${label}</div>
                        <div class="text-zinc-400 text-sm">${statusText}${details ? ' - ' + details : ''}</div>
                    </div>
                </div>
            `;
        }

        function checkScriptLoading() {
            console.log('🔍 Checking script loading status...');
            const scriptStatus = document.getElementById('scriptStatus');
            
            const checks = [
                { name: 'config.js', test: () => typeof gasData !== 'undefined' },
                { name: 'data.js', test: () => Array.isArray(window.gasData || gasData) },
                { name: 'ai-analyzer.js', test: () => typeof addCalibrationCompoundRow === 'function' }
            ];

            let html = '';
            checks.forEach(check => {
                try {
                    const result = check.test();
                    html += createStatusItem(check.name, result);
                    console.log(`✅ ${check.name}: ${result ? 'Loaded' : 'Not loaded'}`);
                } catch (error) {
                    html += createStatusItem(check.name, false, error.message);
                    console.error(`❌ ${check.name}: ${error.message}`);
                }
            });

            scriptStatus.innerHTML = html;
        }

        function checkDOMElements() {
            console.log('🔍 Checking DOM elements...');
            const domStatus = document.getElementById('domStatus');
            
            const elements = [
                'calibrationGasStandardName',
                'addCalibrationCompoundBtn',
                'calibrationCompoundsContainer',
                'analyzeCalibrationBtn',
                'calibrationResults',
                'calibrationLoading',
                'calibrationInputError'
            ];

            let html = '';
            elements.forEach(elementId => {
                const element = document.getElementById(elementId);
                const exists = element !== null;
                html += createStatusItem(elementId, exists, exists ? element.tagName : 'Not found');
                console.log(`${exists ? '✅' : '❌'} ${elementId}: ${exists ? 'Found' : 'Not found'}`);
            });

            domStatus.innerHTML = html;
        }

        function checkFunctions() {
            console.log('🔍 Checking function availability...');
            const functionStatus = document.getElementById('functionStatus');
            
            const functions = [
                { name: 'initializeAIAnalyzer', ref: window.initializeAIAnalyzer },
                { name: 'addCalibrationCompoundRow', ref: window.addCalibrationCompoundRow },
                { name: 'gasData variable', ref: window.gasData || gasData }
            ];

            let html = '';
            functions.forEach(func => {
                const exists = typeof func.ref !== 'undefined';
                const type = typeof func.ref;
                html += createStatusItem(func.name, exists, `Type: ${type}`);
                console.log(`${exists ? '✅' : '❌'} ${func.name}: ${exists ? `Available (${type})` : 'Not available'}`);
            });

            functionStatus.innerHTML = html;
        }

        function checkEventListeners() {
            console.log('🔍 Testing event listeners...');
            const eventStatus = document.getElementById('eventStatus');
            let html = '';

            // Test add button
            const addBtn = document.getElementById('addCalibrationCompoundBtn');
            if (addBtn) {
                try {
                    // Test click simulation
                    const clickTest = () => {
                        const initialRows = document.querySelectorAll('.compound-row').length;
                        addBtn.click();
                        const finalRows = document.querySelectorAll('.compound-row').length;
                        return finalRows > initialRows;
                    };
                    
                    const addBtnWorks = clickTest();
                    html += createStatusItem('Add Compound Button', addBtnWorks);
                    console.log(`${addBtnWorks ? '✅' : '❌'} Add Compound Button: ${addBtnWorks ? 'Working' : 'Not working'}`);
                } catch (error) {
                    html += createStatusItem('Add Compound Button', false, error.message);
                    console.error(`❌ Add Compound Button: ${error.message}`);
                }
            } else {
                html += createStatusItem('Add Compound Button', false, 'Button not found');
                console.error('❌ Add Compound Button: Button not found');
            }

            // Test analyze button
            const analyzeBtn = document.getElementById('analyzeCalibrationBtn');
            if (analyzeBtn) {
                html += createStatusItem('Analyze Button Element', true, 'Found and clickable');
                console.log('✅ Analyze Button Element: Found and clickable');
            } else {
                html += createStatusItem('Analyze Button Element', false, 'Not found');
                console.error('❌ Analyze Button Element: Not found');
            }

            eventStatus.innerHTML = html;
        }

        function runInteractiveTest() {
            console.log('🧪 Running interactive test...');
            
            // Add a test compound row
            const addBtn = document.getElementById('addCalibrationCompoundBtn');
            if (addBtn) {
                addBtn.click();
                console.log('✅ Clicked add compound button');
                
                // Fill in test data
                setTimeout(() => {
                    const rows = document.querySelectorAll('.compound-row');
                    if (rows.length > 0) {
                        const lastRow = rows[rows.length - 1];
                        const inputs = lastRow.querySelectorAll('input');
                        if (inputs.length >= 2) {
                            inputs[0].value = 'CH4'; // Compound name
                            inputs[1].value = '1000'; // Concentration
                            console.log('✅ Filled test data: CH4, 1000 ppm');
                        }
                    }
                }, 100);
            }

            // Test the analyze button
            const analyzeBtn = document.getElementById('analyzeCalibrationBtn');
            if (analyzeBtn) {
                analyzeBtn.addEventListener('click', function testListener() {
                    console.log('🔬 Analyze button clicked - event listener working!');
                    const resultsDiv = document.getElementById('calibrationResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML = `
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <p class="text-green-600 font-medium">✅ Interactive Test Successful!</p>
                                <p class="text-sm text-green-500 mt-2">The AI analyzer button is working correctly. Event listeners are properly attached.</p>
                            </div>
                        `;
                    }
                    // Remove this test listener
                    analyzeBtn.removeEventListener('click', testListener);
                }, { once: true });
            }
        }

        // Initialize debug session
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Starting comprehensive AI analyzer debug session...');
            
            // Run checks with delays to ensure everything loads
            setTimeout(() => {
                checkScriptLoading();
                checkDOMElements();
                checkFunctions();
                
                // Try to initialize AI analyzer
                if (typeof initializeAIAnalyzer === 'function') {
                    console.log('🔧 Attempting to initialize AI analyzer...');
                    try {
                        initializeAIAnalyzer();
                        console.log('✅ AI analyzer initialized successfully');
                    } catch (error) {
                        console.error('❌ AI analyzer initialization failed:', error);
                    }
                } else {
                    console.error('❌ initializeAIAnalyzer function not available');
                }
                
                setTimeout(() => {
                    checkEventListeners();
                    console.log('🎯 Debug session complete');
                }, 500);
            }, 1000);
        });
    </script>
</body>
</html>
