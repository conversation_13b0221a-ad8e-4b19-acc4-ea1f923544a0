<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Authentication Flow Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; }
        .debug-card { background: white; border-radius: 8px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .debug-header { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }
        .test-result { padding: 10px; margin: 8px 0; border-radius: 4px; font-family: monospace; }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; margin: 10px 0; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 Authentication Flow Diagnostic</h1>
        <p>Diagnosing the exact issue with analyte data not persisting to Supabase database.</p>

        <div class="debug-card">
            <div class="debug-header">🔐 Authentication State Analysis</div>
            <button class="test-button" onclick="runFullDiagnostic()">Run Complete Diagnostic</button>
            <button class="test-button" onclick="testSaveFlow()">Test Save Flow</button>
            <button class="test-button" onclick="simulateFormSubmission()">Simulate Form Submission</button>
            <div id="diagnosticResults"></div>
        </div>

        <div class="debug-card">
            <div class="debug-header">💾 Save Operation Testing</div>
            <input type="text" id="testAnalyteName" placeholder="Test Analyte Name" style="padding: 8px; margin-right: 10px;">
            <button class="test-button" onclick="testAnalyteCreation()">Test Analyte Creation</button>
            <div id="saveResults"></div>
        </div>

        <div class="debug-card">
            <div class="debug-header">🗄️ Database Inspection</div>
            <button class="test-button" onclick="inspectDatabase()">Inspect Database</button>
            <button class="test-button" onclick="testDirectInsert()">Test Direct Insert</button>
            <div id="databaseResults"></div>
        </div>
    </div>

    <script type="module">
        // Import modules
        import { supabase } from './supabase-client.js';
        import { supabaseStorage } from './supabase-client.js';
        import { authManager } from './auth.js';

        let gasData = []; // Mock gasData for testing
        
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            container.appendChild(div);
        }

        function addCodeResult(containerId, title, code, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${title}:</strong><div class="code-block">${JSON.stringify(code, null, 2)}</div>`;
            container.appendChild(div);
        }

        window.runFullDiagnostic = async function() {
            const container = document.getElementById('diagnosticResults');
            container.innerHTML = '';
            
            addResult('diagnosticResults', '🚀 Starting comprehensive authentication diagnostic...', 'info');

            try {
                // Test 1: Check AuthManager state
                addResult('diagnosticResults', '1️⃣ Testing AuthManager...', 'info');
                const isAuth = authManager.isAuthenticated();
                const currentUser = authManager.getCurrentUser();
                const authUserId = authManager.getUserId();
                
                addResult('diagnosticResults', `AuthManager.isAuthenticated(): ${isAuth}`, isAuth ? 'success' : 'error');
                addResult('diagnosticResults', `AuthManager.getCurrentUser(): ${currentUser ? 'User object exists' : 'null'}`, currentUser ? 'success' : 'error');
                addResult('diagnosticResults', `AuthManager.getUserId(): ${authUserId || 'null'}`, authUserId ? 'success' : 'error');

                if (currentUser) {
                    addCodeResult('diagnosticResults', 'Current User Object', {
                        id: currentUser.id,
                        email: currentUser.email,
                        created_at: currentUser.created_at
                    }, 'info');
                }

                // Test 2: Check Supabase direct auth
                addResult('diagnosticResults', '2️⃣ Testing Supabase auth directly...', 'info');
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error) {
                    addResult('diagnosticResults', `Supabase auth error: ${error.message}`, 'error');
                } else if (user) {
                    addResult('diagnosticResults', `Supabase user found: ${user.email}`, 'success');
                    addResult('diagnosticResults', `Supabase user ID: ${user.id}`, 'success');
                } else {
                    addResult('diagnosticResults', 'No Supabase user found', 'error');
                }

                // Test 3: Check SupabaseStorage getUserId
                addResult('diagnosticResults', '3️⃣ Testing SupabaseStorage.getUserId()...', 'info');
                const storageUserId = await supabaseStorage.getUserId();
                addResult('diagnosticResults', `SupabaseStorage.getUserId(): ${storageUserId || 'null'}`, storageUserId ? 'success' : 'error');

                // Test 4: Compare all user IDs
                addResult('diagnosticResults', '4️⃣ Comparing user ID consistency...', 'info');
                const comparison = {
                    authManager: authUserId,
                    supabaseDirect: user?.id,
                    supabaseStorage: storageUserId
                };
                addCodeResult('diagnosticResults', 'User ID Comparison', comparison, 'info');

                const allMatch = authUserId === user?.id && user?.id === storageUserId && authUserId !== null;
                addResult('diagnosticResults', `All user IDs match: ${allMatch}`, allMatch ? 'success' : 'error');

                // Test 5: Test session validity
                addResult('diagnosticResults', '5️⃣ Testing session validity...', 'info');
                const { data: { session } } = await supabase.auth.getSession();
                if (session) {
                    addResult('diagnosticResults', `Session valid until: ${new Date(session.expires_at * 1000).toLocaleString()}`, 'success');
                    addResult('diagnosticResults', `Session user ID: ${session.user.id}`, 'info');
                } else {
                    addResult('diagnosticResults', 'No valid session found', 'error');
                }

            } catch (error) {
                addResult('diagnosticResults', `Diagnostic error: ${error.message}`, 'error');
                console.error('Full diagnostic error:', error);
            }
        };

        window.testSaveFlow = async function() {
            const container = document.getElementById('diagnosticResults');
            
            addResult('diagnosticResults', '💾 Testing complete save flow...', 'info');

            try {
                // Step 1: Check authentication requirement
                if (!authManager.isAuthenticated()) {
                    addResult('diagnosticResults', '❌ ISSUE FOUND: AuthManager reports not authenticated', 'error');
                    return;
                }

                // Step 2: Create test analyte
                const testAnalyte = {
                    name: 'Debug Test Analyte ' + Date.now(),
                    current: [{ min: 1, max: 100, label: 'Test current' }],
                    target: [{ min: 0.1, max: 1000, label: 'Test target' }],
                    gapNotes: 'Debug test notes',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                gasData.push(testAnalyte);
                addResult('diagnosticResults', '✅ Test analyte created and added to gasData', 'success');

                // Step 3: Test the exact filter used in storage.js
                const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                addResult('diagnosticResults', `Filter result: ${userTargets.length} of ${gasData.length} analytes passed filter`, userTargets.length > 0 ? 'success' : 'error');

                if (userTargets.length === 0) {
                    addResult('diagnosticResults', '❌ ISSUE FOUND: Filter excludes all analytes', 'error');
                    addCodeResult('diagnosticResults', 'Failed Filter Test - Analyte Properties', testAnalyte, 'error');
                    return;
                }

                // Step 4: Test saveUserTargets directly
                addResult('diagnosticResults', '🚀 Testing supabaseStorage.saveUserTargets()...', 'info');
                await supabaseStorage.saveUserTargets(userTargets);
                addResult('diagnosticResults', '✅ saveUserTargets completed without error', 'success');

                // Step 5: Verify data was actually saved
                addResult('diagnosticResults', '🔍 Verifying data was saved to database...', 'info');
                const userId = await supabaseStorage.getUserId();
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', userId)
                    .eq('name', testAnalyte.name);

                if (error) {
                    addResult('diagnosticResults', `❌ Database verification error: ${error.message}`, 'error');
                } else if (data && data.length > 0) {
                    addResult('diagnosticResults', '✅ Data successfully saved and verified in database!', 'success');
                } else {
                    addResult('diagnosticResults', '❌ ISSUE FOUND: Data not found in database after save', 'error');
                }

            } catch (error) {
                addResult('diagnosticResults', `❌ Save flow error: ${error.message}`, 'error');
                console.error('Save flow error:', error);
            }
        };

        window.testAnalyteCreation = async function() {
            const analyteName = document.getElementById('testAnalyteName').value.trim();
            if (!analyteName) {
                addResult('saveResults', '⚠️ Please enter an analyte name', 'warning');
                return;
            }

            const container = document.getElementById('saveResults');
            container.innerHTML = '';

            addResult('saveResults', `🧪 Testing analyte creation: ${analyteName}`, 'info');

            try {
                // Simulate the exact form handler flow
                const newAnalyte = {
                    name: analyteName,
                    current: [{ min: 10, max: 100, label: 'Test Current Range' }],
                    target: [{ min: 1, max: 1000, label: 'Test Target Range' }],
                    gapNotes: 'Created via debug test',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                // Add to gasData (simulating chart update)
                gasData.push(newAnalyte);
                addResult('saveResults', '✅ Analyte added to chart (gasData array)', 'success');

                // Test save operation
                if (!authManager.isAuthenticated()) {
                    addResult('saveResults', '❌ Cannot save: User not authenticated', 'error');
                    return;
                }

                const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                addResult('saveResults', `📊 Filtered targets for save: ${userTargets.length}`, 'info');

                await supabaseStorage.saveUserTargets(userTargets);
                addResult('saveResults', '✅ Save operation completed successfully', 'success');

                // Clear the input
                document.getElementById('testAnalyteName').value = '';

            } catch (error) {
                addResult('saveResults', `❌ Creation/save error: ${error.message}`, 'error');
                console.error('Test analyte creation error:', error);
            }
        };

        window.inspectDatabase = async function() {
            const container = document.getElementById('databaseResults');
            container.innerHTML = '';

            addResult('databaseResults', '🗄️ Inspecting database contents...', 'info');

            try {
                const userId = await supabaseStorage.getUserId();
                if (!userId) {
                    addResult('databaseResults', '❌ No user ID available for database query', 'error');
                    return;
                }

                addResult('databaseResults', `Using user ID: ${userId}`, 'info');

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', userId)
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) {
                    addResult('databaseResults', `❌ Database query error: ${error.message}`, 'error');
                } else {
                    addResult('databaseResults', `📊 Found ${data.length} user records in database`, data.length > 0 ? 'success' : 'warning');
                    
                    data.forEach((record, index) => {
                        addResult('databaseResults', 
                            `${index + 1}. ${record.name} (${record.data_type}) - ${new Date(record.created_at).toLocaleString()}`, 
                            'info'
                        );
                    });
                }

            } catch (error) {
                addResult('databaseResults', `❌ Database inspection error: ${error.message}`, 'error');
            }
        };

        window.testDirectInsert = async function() {
            const container = document.getElementById('databaseResults');
            
            addResult('databaseResults', '🔨 Testing direct database insert...', 'info');

            try {
                const userId = await supabaseStorage.getUserId();
                if (!userId) {
                    addResult('databaseResults', '❌ No user ID for direct insert test', 'error');
                    return;
                }

                const testData = {
                    user_id: userId,
                    name: 'Direct Insert Test ' + Date.now(),
                    current_ranges: [{ min: 1, max: 100, label: 'Direct test current' }],
                    target_ranges: [{ min: 0.1, max: 1000, label: 'Direct test target' }],
                    gap_notes: 'Direct insert test notes',
                    is_custom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert(testData)
                    .select();

                if (error) {
                    addResult('databaseResults', `❌ Direct insert failed: ${error.message}`, 'error');
                    addCodeResult('databaseResults', 'Insert Error Details', error, 'error');
                } else {
                    addResult('databaseResults', '✅ Direct insert successful!', 'success');
                    addResult('databaseResults', `Inserted record ID: ${data[0].id}`, 'info');
                }

            } catch (error) {
                addResult('databaseResults', `❌ Direct insert error: ${error.message}`, 'error');
            }
        };

        window.simulateFormSubmission = async function() {
            const container = document.getElementById('diagnosticResults');
            
            addResult('diagnosticResults', '📝 Simulating complete form submission flow...', 'info');

            try {
                // Step 1: Form validation (normally done in form handler)
                const analyteName = 'Simulated Form Analyte ' + Date.now();
                addResult('diagnosticResults', `Creating analyte: ${analyteName}`, 'info');

                // Step 2: Create analyte object (from form-handler.js logic)
                const newAnalyte = {
                    name: analyteName,
                    current: [{ min: 10, max: 100, label: 'Simulated Current' }],
                    target: [{ min: 1, max: 1000, label: 'Simulated Target' }],
                    gapNotes: 'Simulated gap notes',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                // Step 3: Add to gasData (chart update)
                gasData.push(newAnalyte);
                addResult('diagnosticResults', '✅ Analyte added to chart (gasData)', 'success');

                // Step 4: Auto-save attempt (from form-handler.js)
                addResult('diagnosticResults', '💾 Starting auto-save operation...', 'info');
                
                if (!authManager.isAuthenticated()) {
                    addResult('diagnosticResults', '❌ AUTO-SAVE FAILED: User not authenticated', 'error');
                    addResult('diagnosticResults', '🔍 This explains why chart updates but database doesn\'t', 'warning');
                    return;
                }

                // Simulate saveDataToLocalStorage() from storage.js
                const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                addResult('diagnosticResults', `Filtered user targets: ${userTargets.length}`, 'info');

                if (userTargets.length === 0) {
                    addResult('diagnosticResults', '❌ AUTO-SAVE FAILED: No user targets after filter', 'error');
                    return;
                }

                await supabaseStorage.saveUserTargets(userTargets);
                addResult('diagnosticResults', '✅ Auto-save completed successfully', 'success');
                addResult('diagnosticResults', '🎉 Form submission simulation: SUCCESS', 'success');

            } catch (error) {
                addResult('diagnosticResults', `❌ Form simulation error: ${error.message}`, 'error');
                addResult('diagnosticResults', '🔍 This error would cause chart update without database save', 'warning');
            }
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                await authManager.init();
                addResult('diagnosticResults', 'Debug tool initialized. Click "Run Complete Diagnostic" to start.', 'info');
            } catch (error) {
                addResult('diagnosticResults', `Initialization error: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
