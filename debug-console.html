<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Console - RBAC System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-4">🔧 Debug Console - RBAC System</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Panel: Controls -->
            <div class="space-y-4">
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">System Status</h3>
                    <div id="systemStatus" class="space-y-2 text-sm"></div>
                    <button id="checkStatus" class="mt-2 bg-blue-600 text-white px-3 py-1 rounded text-sm">
                        Check Status
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Authentication Test</h3>
                    <div id="authTest" class="space-y-2 text-sm"></div>
                    <button id="testAuth" class="mt-2 bg-green-600 text-white px-3 py-1 rounded text-sm">
                        Test Auth
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Role Test</h3>
                    <div id="roleTest" class="space-y-2 text-sm"></div>
                    <button id="testRole" class="mt-2 bg-purple-600 text-white px-3 py-1 rounded text-sm">
                        Test Roles
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Database Test</h3>
                    <div id="dbTest" class="space-y-2 text-sm"></div>
                    <button id="testDb" class="mt-2 bg-orange-600 text-white px-3 py-1 rounded text-sm">
                        Test Database
                    </button>
                </div>
            </div>
            
            <!-- Right Panel: Console Output -->
            <div class="p-4 border rounded">
                <h3 class="font-semibold mb-2">Console Output</h3>
                <div id="console" class="bg-black text-green-400 p-4 rounded text-sm font-mono h-96 overflow-y-auto"></div>
                <button id="clearConsole" class="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm">
                    Clear Console
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        let supabase;
        let authManager;
        let roleManager;

        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const color = {
                info: 'text-green-400',
                error: 'text-red-400',
                warn: 'text-yellow-400',
                success: 'text-blue-400'
            }[type] || 'text-green-400';
            
            const div = document.createElement('div');
            div.className = color;
            div.textContent = `[${timestamp}] ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }

        function logToSection(sectionId, message, isError = false) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = isError ? 'text-red-600' : 'text-green-600';
            div.textContent = message;
            section.appendChild(div);
        }

        async function initializeSystem() {
            try {
                log('Initializing system...', 'info');
                
                // Initialize Supabase
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
                log('Supabase client initialized', 'success');

                // Import modules
                const authModule = await import('./auth.js');
                authManager = authModule.authManager;
                log('Auth manager imported', 'success');

                const roleModule = await import('./role-manager.js');
                roleManager = roleModule.roleManager;
                log('Role manager imported', 'success');

                log('System initialization complete', 'success');
            } catch (error) {
                log(`System initialization failed: ${error.message}`, 'error');
                console.error('Init error:', error);
            }
        }

        async function checkSystemStatus() {
            const section = document.getElementById('systemStatus');
            section.innerHTML = '';
            
            try {
                logToSection('systemStatus', '🔍 Checking system status...');
                
                // Check Supabase connection
                const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
                if (error) {
                    logToSection('systemStatus', `❌ Database: ${error.message}`, true);
                    log(`Database error: ${error.message}`, 'error');
                } else {
                    logToSection('systemStatus', '✅ Database: Connected');
                    log('Database connection successful', 'success');
                }

                // Check auth manager
                if (authManager) {
                    logToSection('systemStatus', '✅ Auth Manager: Ready');
                    log('Auth manager ready', 'success');
                } else {
                    logToSection('systemStatus', '❌ Auth Manager: Not loaded', true);
                    log('Auth manager not loaded', 'error');
                }

                // Check role manager
                if (roleManager) {
                    logToSection('systemStatus', '✅ Role Manager: Ready');
                    log('Role manager ready', 'success');
                } else {
                    logToSection('systemStatus', '❌ Role Manager: Not loaded', true);
                    log('Role manager not loaded', 'error');
                }

            } catch (error) {
                logToSection('systemStatus', `❌ Error: ${error.message}`, true);
                log(`Status check error: ${error.message}`, 'error');
            }
        }

        async function testAuthentication() {
            const section = document.getElementById('authTest');
            section.innerHTML = '';
            
            try {
                logToSection('authTest', '🔐 Testing authentication...');
                log('Testing authentication system', 'info');
                
                if (!authManager) {
                    throw new Error('Auth manager not initialized');
                }

                // Get current user
                const currentUser = authManager.getCurrentUser();
                if (currentUser) {
                    logToSection('authTest', `✅ Current user: ${currentUser.email}`);
                    log(`User authenticated: ${currentUser.email}`, 'success');
                    
                    // Get user profile
                    const profile = await authManager.getUserProfile();
                    if (profile) {
                        logToSection('authTest', `✅ Profile: ${profile.role} (${profile.status})`);
                        log(`User profile: ${profile.role} (${profile.status})`, 'success');
                    } else {
                        logToSection('authTest', '⚠️ Profile: Not found');
                        log('User profile not found', 'warn');
                    }
                } else {
                    logToSection('authTest', '⚠️ No user authenticated');
                    log('No user authenticated', 'warn');
                }

            } catch (error) {
                logToSection('authTest', `❌ Error: ${error.message}`, true);
                log(`Auth test error: ${error.message}`, 'error');
            }
        }

        async function testRoleSystem() {
            const section = document.getElementById('roleTest');
            section.innerHTML = '';
            
            try {
                logToSection('roleTest', '👥 Testing role system...');
                log('Testing role management system', 'info');
                
                if (!roleManager) {
                    throw new Error('Role manager not initialized');
                }

                // Initialize role manager
                await roleManager.init();
                logToSection('roleTest', '✅ Role manager initialized');
                log('Role manager initialized', 'success');

                // Get current role info
                const role = roleManager.getRole();
                const status = roleManager.getStatus();
                logToSection('roleTest', `✅ Current role: ${role}`);
                logToSection('roleTest', `✅ Current status: ${status}`);
                log(`Current role: ${role}, status: ${status}`, 'success');

                // Test role checks
                const isAdmin = roleManager.hasRole('admin');
                const isUser = roleManager.hasRole('user');
                const isGuest = roleManager.hasRole('guest');
                
                logToSection('roleTest', `✅ Admin: ${isAdmin}, User: ${isUser}, Guest: ${isGuest}`);
                log(`Role checks - Admin: ${isAdmin}, User: ${isUser}, Guest: ${isGuest}`, 'info');

                // Get UI restrictions
                const restrictions = roleManager.getUIRestrictions();
                logToSection('roleTest', `✅ UI restrictions: ${JSON.stringify(restrictions)}`);
                log(`UI restrictions: ${JSON.stringify(restrictions)}`, 'info');

            } catch (error) {
                logToSection('roleTest', `❌ Error: ${error.message}`, true);
                log(`Role test error: ${error.message}`, 'error');
            }
        }

        async function testDatabase() {
            const section = document.getElementById('dbTest');
            section.innerHTML = '';
            
            try {
                logToSection('dbTest', '💾 Testing database operations...');
                log('Testing database operations', 'info');

                // Test user_profiles table
                const { data: profiles, error: profileError } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .limit(5);

                if (profileError) {
                    logToSection('dbTest', `❌ Profiles: ${profileError.message}`, true);
                    log(`Profile query error: ${profileError.message}`, 'error');
                } else {
                    logToSection('dbTest', `✅ Profiles: ${profiles.length} records found`);
                    log(`Profile query successful: ${profiles.length} records`, 'success');
                }

                // Test gas_analytes table
                const { data: analytes, error: analyteError } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .limit(5);

                if (analyteError) {
                    logToSection('dbTest', `❌ Analytes: ${analyteError.message}`, true);
                    log(`Analyte query error: ${analyteError.message}`, 'error');
                } else {
                    logToSection('dbTest', `✅ Analytes: ${analytes.length} records found`);
                    log(`Analyte query successful: ${analytes.length} records`, 'success');
                }

                // Test admin_audit_log table
                const { data: logs, error: logError } = await supabase
                    .from('admin_audit_log')
                    .select('*')
                    .limit(5);

                if (logError) {
                    logToSection('dbTest', `❌ Logs: ${logError.message}`, true);
                    log(`Log query error: ${logError.message}`, 'error');
                } else {
                    logToSection('dbTest', `✅ Logs: ${logs.length} records found`);
                    log(`Log query successful: ${logs.length} records`, 'success');
                }

            } catch (error) {
                logToSection('dbTest', `❌ Error: ${error.message}`, true);
                log(`Database test error: ${error.message}`, 'error');
            }
        }

        // Event listeners
        document.getElementById('checkStatus').addEventListener('click', checkSystemStatus);
        document.getElementById('testAuth').addEventListener('click', testAuthentication);
        document.getElementById('testRole').addEventListener('click', testRoleSystem);
        document.getElementById('testDb').addEventListener('click', testDatabase);
        document.getElementById('clearConsole').addEventListener('click', () => {
            document.getElementById('console').innerHTML = '';
        });

        // Initialize on load
        window.addEventListener('load', async () => {
            log('Debug console loaded', 'info');
            await initializeSystem();
            await checkSystemStatus();
        });

        // Capture console errors
        window.addEventListener('error', (event) => {
            log(`Error: ${event.error.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`Unhandled rejection: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
