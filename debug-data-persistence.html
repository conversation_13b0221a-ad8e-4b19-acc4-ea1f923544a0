<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Persistence Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Data Persistence Debug Tool</h1>
        
        <div class="section">
            <h2>📡 Connection Status</h2>
            <div id="connectionResults"></div>
            <button onclick="checkConnection()">Check Connection</button>
        </div>

        <div class="section">
            <h2>💾 Data Flow Test</h2>
            <div id="dataFlowResults"></div>
            <button onclick="testFullDataFlow()">Test Complete Data Flow</button>
        </div>

        <div class="section">
            <h2>🔧 Manual Operations</h2>
            <input type="text" id="testAnalyteName" placeholder="Test Analyte Name" value="Debug Test Compound">
            <button onclick="addTestAnalyte()">Add Test Analyte</button>
            <button onclick="loadCurrentData()">Load Current Data</button>
            <button onclick="clearAllData()">Clear All Data</button>
            <div id="manualResults"></div>
        </div>

        <div class="section">
            <h2>📊 Current Database State</h2>
            <div id="databaseState"></div>
            <button onclick="inspectDatabase()">Inspect Database</button>
        </div>

        <div class="section">
            <h2>🧪 Session Info</h2>
            <div id="sessionInfo"></div>
            <button onclick="checkSession()">Check Session</button>
        </div>
    </div>

    <script type="module">
        import { supabase, supabaseStorage, userSession } from './supabase-client.js';
        
        window.supabase = supabase;
        window.supabaseStorage = supabaseStorage;
        window.userSession = userSession;

        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        window.checkConnection = async function() {
            const container = document.getElementById('connectionResults');
            container.innerHTML = '';
            
            try {
                addLog('connectionResults', 'Testing Supabase connection...', 'info');
                
                // Test basic connection
                const connectionResult = await supabaseStorage.testConnection();
                addLog('connectionResults', `Connection test result: ${connectionResult}`, connectionResult ? 'success' : 'error');
                
                // Test table access
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .limit(1);
                
                if (error) {
                    addLog('connectionResults', `Table access error: ${error.message}`, 'error');
                } else {
                    addLog('connectionResults', 'Table access successful', 'success');
                }
                
                // Check session
                addLog('connectionResults', `Session ID: ${userSession}`, 'info');
                
            } catch (error) {
                addLog('connectionResults', `Connection check failed: ${error.message}`, 'error');
            }
        };

        window.testFullDataFlow = async function() {
            const container = document.getElementById('dataFlowResults');
            container.innerHTML = '';
            
            try {
                // Step 1: Create test data
                addLog('dataFlowResults', 'Step 1: Creating test data...', 'info');
                const testData = [{
                    name: 'Test Flow Analyte ' + Date.now(),
                    current: [{ min: 1, max: 100, label: 'Test current range' }],
                    target: [{ min: 0.1, max: 1000, label: 'Test target range' }],
                    gapNotes: 'Test gap notes for data flow',
                    isCustom: true
                }];
                
                // Step 2: Save to Supabase directly
                addLog('dataFlowResults', 'Step 2: Saving to Supabase...', 'info');
                await supabaseStorage.saveData(testData);
                addLog('dataFlowResults', 'Data saved to Supabase successfully', 'success');
                
                // Step 3: Load from Supabase
                addLog('dataFlowResults', 'Step 3: Loading from Supabase...', 'info');
                const loadedData = await supabaseStorage.loadData();
                addLog('dataFlowResults', `Loaded ${loadedData.length} records from Supabase`, 'success');
                
                // Step 4: Verify data integrity
                addLog('dataFlowResults', 'Step 4: Verifying data integrity...', 'info');
                const found = loadedData.find(item => item.name === testData[0].name);
                if (found) {
                    addLog('dataFlowResults', 'Data integrity verified - test analyte found!', 'success');
                    addLog('dataFlowResults', `Analyte: ${found.name}`, 'info');
                    addLog('dataFlowResults', `Current ranges: ${JSON.stringify(found.current)}`, 'info');
                    addLog('dataFlowResults', `Target ranges: ${JSON.stringify(found.target)}`, 'info');
                } else {
                    addLog('dataFlowResults', 'Data integrity failed - test analyte not found!', 'error');
                }
                
            } catch (error) {
                addLog('dataFlowResults', `Data flow test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        };

        window.addTestAnalyte = async function() {
            const analyteName = document.getElementById('testAnalyteName').value;
            const container = document.getElementById('manualResults');
            
            if (!analyteName) {
                addLog('manualResults', 'Please enter a test analyte name', 'warning');
                return;
            }
            
            try {
                addLog('manualResults', `Adding test analyte: ${analyteName}...`, 'info');
                
                // Get current data
                const currentData = await supabaseStorage.loadData();
                
                // Add new analyte
                const newAnalyte = {
                    name: analyteName,
                    current: [{ min: Math.random() * 100, max: Math.random() * 1000, label: 'Debug current' }],
                    target: [{ min: 0.1, max: 10000, label: 'Debug target' }],
                    gapNotes: 'Added via debug tool',
                    isCustom: true
                };
                
                currentData.push(newAnalyte);
                
                // Save updated data
                await supabaseStorage.saveData(currentData);
                
                addLog('manualResults', `Successfully added: ${analyteName}`, 'success');
                addLog('manualResults', 'Total analytes now: ' + currentData.length, 'info');
                
                // Clear input
                document.getElementById('testAnalyteName').value = '';
                
            } catch (error) {
                addLog('manualResults', `Failed to add analyte: ${error.message}`, 'error');
            }
        };

        window.loadCurrentData = async function() {
            const container = document.getElementById('manualResults');
            
            try {
                addLog('manualResults', 'Loading current data...', 'info');
                
                const data = await supabaseStorage.loadData();
                addLog('manualResults', `Loaded ${data.length} analytes`, 'success');
                
                if (data.length > 0) {
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(data, null, 2);
                    document.getElementById('manualResults').appendChild(pre);
                }
                
            } catch (error) {
                addLog('manualResults', `Failed to load data: ${error.message}`, 'error');
            }
        };

        window.clearAllData = async function() {
            if (!confirm('Are you sure you want to clear all data?')) return;
            
            const container = document.getElementById('manualResults');
            
            try {
                addLog('manualResults', 'Clearing all data...', 'info');
                await supabaseStorage.clearData();
                addLog('manualResults', 'All data cleared successfully', 'success');
                
            } catch (error) {
                addLog('manualResults', `Failed to clear data: ${error.message}`, 'error');
            }
        };

        window.inspectDatabase = async function() {
            const container = document.getElementById('databaseState');
            container.innerHTML = '';
            
            try {
                addLog('databaseState', 'Inspecting database state...', 'info');
                
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_session', userSession)
                    .order('created_at');
                
                if (error) {
                    addLog('databaseState', `Database inspection failed: ${error.message}`, 'error');
                    return;
                }
                
                addLog('databaseState', `Found ${data.length} records for session: ${userSession}`, 'success');
                
                if (data.length > 0) {
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(data, null, 2);
                    container.appendChild(pre);
                } else {
                    addLog('databaseState', 'No data found in database for this session', 'warning');
                }
                
            } catch (error) {
                addLog('databaseState', `Database inspection error: ${error.message}`, 'error');
            }
        };

        window.checkSession = async function() {
            const container = document.getElementById('sessionInfo');
            container.innerHTML = '';
            
            addLog('sessionInfo', `Current session ID: ${userSession}`, 'info');
            addLog('sessionInfo', `Session stored in localStorage: ${localStorage.getItem('gas_analysis_session_id')}`, 'info');
            addLog('sessionInfo', `Local storage data exists: ${!!localStorage.getItem('alphaGasSolution_gasData')}`, 'info');
            
            // Check if session has any data in Supabase
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .eq('user_session', userSession);
                
                if (error) {
                    addLog('sessionInfo', `Error checking session data: ${error.message}`, 'error');
                } else {
                    addLog('sessionInfo', `Records in Supabase for this session: ${data[0]?.count || 0}`, 'info');
                }
                
            } catch (error) {
                addLog('sessionInfo', `Session check error: ${error.message}`, 'error');
            }
        };

        // Auto-run connection check on load
        setTimeout(checkConnection, 1000);
        setTimeout(checkSession, 1500);
    </script>
</body>
</html>
