<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Save Issue - Analysis Gap</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log-entry {
            margin: 5px 0;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .red { background: #dc3545; }
        .green { background: #28a745; }
        .yellow { background: #ffc107; color: black; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .red-dot { background-color: #dc3545; }
        .green-dot { background-color: #28a745; }
        .yellow-dot { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Save Issue - Analyte Persistence Problem</h1>
        
        <div class="section info">
            <h2>🎯 Problem Description</h2>
            <p><strong>Issue:</strong> When adding analytes via the form, they appear on the chart but don't register in the Supabase database table.</p>
            <p><strong>Investigating:</strong> Authentication status, database permissions, save operation flow, and data validation.</p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🔐 Authentication Status</h3>
                <div id="authCheck">
                    <span class="status-indicator yellow-dot"></span>
                    <span>Checking...</span>
                </div>
                <button onclick="checkAuthentication()">Refresh Auth Status</button>
            </div>

            <div class="status-card">
                <h3>🔗 Supabase Connection</h3>
                <div id="connectionCheck">
                    <span class="status-indicator yellow-dot"></span>
                    <span>Checking...</span>
                </div>
                <button onclick="testConnection()">Test Connection</button>
            </div>

            <div class="status-card">
                <h3>📊 Database Tables</h3>
                <div id="tableCheck">
                    <span class="status-indicator yellow-dot"></span>
                    <span>Checking...</span>
                </div>
                <button onclick="checkTables()">Check Tables</button>
            </div>

            <div class="status-card">
                <h3>🔒 Permissions</h3>
                <div id="permissionsCheck">
                    <span class="status-indicator yellow-dot"></span>
                    <span>Checking...</span>
                </div>
                <button onclick="testPermissions()">Test Permissions</button>
            </div>
        </div>

        <div class="section">
            <h2>🧪 Test Save Operation</h2>
            <div style="margin: 15px 0;">
                <input type="text" id="testAnalyteName" placeholder="Test Analyte Name" style="padding: 8px; margin-right: 10px;">
                <button onclick="testSaveOperation()" class="green">Test Save to Database</button>
                <button onclick="inspectGasData()" class="yellow">Inspect gasData Array</button>
                <button onclick="loadFromDatabase()" class="blue">Load from Database</button>
            </div>
            <div id="saveTestResults"></div>
        </div>

        <div class="section">
            <h2>📝 Step-by-Step Diagnosis</h2>
            <button onclick="runFullDiagnosis()" style="font-size: 16px; padding: 12px 20px;">🔍 Run Complete Diagnosis</button>
            <div id="diagnosisResults"></div>
        </div>

        <div class="section">
            <h2>🗄️ Database Contents</h2>
            <button onclick="inspectDatabase()">Inspect Database</button>
            <button onclick="clearUserData()" class="red">Clear User Data</button>
            <div id="databaseInspection"></div>
        </div>

        <div class="section">
            <h2>📋 Debug Log</h2>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="debugLog"></div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script type="module">
        import { supabase, supabaseStorage } from './supabase-client.js';
        import { authManager } from './auth.js';
        
        // Make available globally for testing
        window.supabase = supabase;
        window.supabaseStorage = supabaseStorage;
        window.authManager = authManager;

        let currentUser = null;

        function addLog(container, message, type = 'info') {
            const logContainer = document.getElementById(container);
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(elementId, message, success = null) {
            const element = document.getElementById(elementId);
            if (element) {
                const indicator = element.querySelector('.status-indicator');
                const textNode = element.childNodes[element.childNodes.length - 1];
                
                if (indicator) {
                    indicator.className = 'status-indicator ' + 
                        (success === true ? 'green-dot' : success === false ? 'red-dot' : 'yellow-dot');
                }
                
                if (textNode) {
                    textNode.textContent = message;
                }
            }
        }

        // Authentication check
        window.checkAuthentication = async function() {
            addLog('debugLog', 'Checking authentication status...', 'info');
            
            try {
                await authManager.init();
                const user = authManager.getCurrentUser();
                
                if (user) {
                    currentUser = user;
                    updateStatus('authCheck', `Authenticated as ${user.email}`, true);
                    addLog('debugLog', `✅ User authenticated: ${user.email} (ID: ${user.id})`, 'success');
                } else {
                    updateStatus('authCheck', 'Not authenticated', false);
                    addLog('debugLog', '❌ User not authenticated', 'error');
                }
            } catch (error) {
                updateStatus('authCheck', `Error: ${error.message}`, false);
                addLog('debugLog', `❌ Auth check failed: ${error.message}`, 'error');
            }
        };

        // Connection test
        window.testConnection = async function() {
            addLog('debugLog', 'Testing Supabase connection...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .limit(1);

                if (error) {
                    updateStatus('connectionCheck', `Error: ${error.message}`, false);
                    addLog('debugLog', `❌ Connection failed: ${error.message}`, 'error');
                } else {
                    updateStatus('connectionCheck', 'Connected successfully', true);
                    addLog('debugLog', '✅ Supabase connection working', 'success');
                }
            } catch (error) {
                updateStatus('connectionCheck', `Exception: ${error.message}`, false);
                addLog('debugLog', `❌ Connection exception: ${error.message}`, 'error');
            }
        };

        // Check tables
        window.checkTables = async function() {
            addLog('debugLog', 'Checking database tables...', 'info');
            
            try {
                // Check gas_analytes table structure
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .limit(1);

                if (error) {
                    updateStatus('tableCheck', `Table error: ${error.message}`, false);
                    addLog('debugLog', `❌ gas_analytes table error: ${error.message}`, 'error');
                } else {
                    updateStatus('tableCheck', 'Tables accessible', true);
                    addLog('debugLog', '✅ gas_analytes table accessible', 'success');
                    
                    if (data && data.length > 0) {
                        addLog('debugLog', `Sample record structure: ${JSON.stringify(Object.keys(data[0]))}`, 'info');
                    }
                }
            } catch (error) {
                updateStatus('tableCheck', `Exception: ${error.message}`, false);
                addLog('debugLog', `❌ Table check exception: ${error.message}`, 'error');
            }
        };

        // Test permissions
        window.testPermissions = async function() {
            addLog('debugLog', 'Testing database permissions...', 'info');
            
            if (!currentUser) {
                updateStatus('permissionsCheck', 'No user to test', false);
                addLog('debugLog', '❌ Cannot test permissions without authenticated user', 'error');
                return;
            }

            try {
                // Test read permission
                const { data: readData, error: readError } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .limit(5);

                if (readError) {
                    addLog('debugLog', `❌ Read permission failed: ${readError.message}`, 'error');
                } else {
                    addLog('debugLog', `✅ Read permission working (found ${readData.length} records)`, 'success');
                }

                // Test write permission with a temporary record
                const testRecord = {
                    user_id: currentUser.id,
                    name: 'PERMISSION_TEST_' + Date.now(),
                    current_ranges: [],
                    target_ranges: [],
                    gap_notes: 'Permission test record',
                    is_custom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                const { data: writeData, error: writeError } = await supabase
                    .from('gas_analytes')
                    .insert(testRecord)
                    .select();

                if (writeError) {
                    updateStatus('permissionsCheck', `Write failed: ${writeError.message}`, false);
                    addLog('debugLog', `❌ Write permission failed: ${writeError.message}`, 'error');
                } else {
                    updateStatus('permissionsCheck', 'Read/Write permissions OK', true);
                    addLog('debugLog', '✅ Write permission working', 'success');
                    
                    // Clean up test record
                    await supabase
                        .from('gas_analytes')
                        .delete()
                        .eq('id', writeData[0].id);
                    
                    addLog('debugLog', '🧹 Test record cleaned up', 'info');
                }

            } catch (error) {
                updateStatus('permissionsCheck', `Exception: ${error.message}`, false);
                addLog('debugLog', `❌ Permission test exception: ${error.message}`, 'error');
            }
        };

        // Test save operation
        window.testSaveOperation = async function() {
            const analyteName = document.getElementById('testAnalyteName').value.trim();
            if (!analyteName) {
                addLog('saveTestResults', '❌ Please enter a test analyte name', 'error');
                return;
            }

            addLog('saveTestResults', `🧪 Testing save operation for: ${analyteName}`, 'info');

            if (!currentUser) {
                addLog('saveTestResults', '❌ User not authenticated - cannot save to database', 'error');
                return;
            }

            try {
                // Create test analyte data
                const testAnalyte = {
                    name: analyteName,
                    current: [{ min: 1, max: 100, label: "Test Current" }],
                    target: [{ min: 0.1, max: 1000, label: "Test Target" }],
                    gapNotes: 'Debug test analyte',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                addLog('saveTestResults', '📝 Test analyte created locally', 'info');

                // Test direct Supabase insert
                const dataToInsert = {
                    user_id: currentUser.id,
                    name: testAnalyte.name,
                    current_ranges: testAnalyte.current,
                    target_ranges: testAnalyte.target,
                    gap_notes: testAnalyte.gapNotes,
                    is_custom: testAnalyte.isCustom,
                    is_shared: testAnalyte.is_shared,
                    data_type: testAnalyte.data_type
                };

                addLog('saveTestResults', '💾 Attempting direct Supabase insert...', 'info');

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert(dataToInsert)
                    .select();

                if (error) {
                    addLog('saveTestResults', `❌ Direct save failed: ${error.message}`, 'error');
                    addLog('saveTestResults', `Error details: ${JSON.stringify(error)}`, 'error');
                } else {
                    addLog('saveTestResults', '✅ Direct save successful!', 'success');
                    addLog('saveTestResults', `Record ID: ${data[0].id}`, 'info');
                }

                // Test using supabaseStorage class
                addLog('saveTestResults', '🔧 Testing via supabaseStorage class...', 'info');
                
                try {
                    await supabaseStorage.saveUserTargets([testAnalyte]);
                    addLog('saveTestResults', '✅ supabaseStorage.saveUserTargets() successful!', 'success');
                } catch (storageError) {
                    addLog('saveTestResults', `❌ supabaseStorage failed: ${storageError.message}`, 'error');
                }

            } catch (error) {
                addLog('saveTestResults', `❌ Test save exception: ${error.message}`, 'error');
            }

            // Clear the input
            document.getElementById('testAnalyteName').value = '';
        };

        // Inspect gasData array
        window.inspectGasData = function() {
            addLog('saveTestResults', '🔍 Inspecting gasData array...', 'info');
            
            if (typeof gasData !== 'undefined') {
                addLog('saveTestResults', `gasData length: ${gasData.length}`, 'info');
                
                if (gasData.length > 0) {
                    const userTargets = gasData.filter(item => !item.is_shared && item.isCustom);
                    addLog('saveTestResults', `User targets in gasData: ${userTargets.length}`, 'info');
                    
                    gasData.forEach((item, index) => {
                        addLog('saveTestResults', 
                            `[${index}] ${item.name} - isCustom: ${item.isCustom}, is_shared: ${item.is_shared || false}`, 
                            'info'
                        );
                    });
                } else {
                    addLog('saveTestResults', 'gasData array is empty', 'warning');
                }
            } else {
                addLog('saveTestResults', '❌ gasData is not defined', 'error');
            }
        };

        // Load from database
        window.loadFromDatabase = async function() {
            if (!currentUser) {
                addLog('saveTestResults', '❌ User not authenticated', 'error');
                return;
            }

            addLog('saveTestResults', '📥 Loading data from database...', 'info');

            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .order('created_at', { ascending: false });

                if (error) {
                    addLog('saveTestResults', `❌ Load failed: ${error.message}`, 'error');
                } else {
                    addLog('saveTestResults', `✅ Found ${data.length} records in database`, 'success');
                    
                    data.forEach((record, index) => {
                        addLog('saveTestResults', 
                            `[${index + 1}] ${record.name} (${record.data_type || 'unknown'}) - ${record.created_at}`, 
                            'info'
                        );
                    });
                }
            } catch (error) {
                addLog('saveTestResults', `❌ Load exception: ${error.message}`, 'error');
            }
        };

        // Full diagnosis
        window.runFullDiagnosis = async function() {
            const container = document.getElementById('diagnosisResults');
            container.innerHTML = '';
            
            addLog('diagnosisResults', '🔍 Starting comprehensive diagnosis...', 'info');
            
            // Step 1: Authentication
            addLog('diagnosisResults', 'Step 1: Checking authentication...', 'info');
            await checkAuthentication();
            
            // Step 2: Connection
            addLog('diagnosisResults', 'Step 2: Testing connection...', 'info');
            await testConnection();
            
            // Step 3: Tables
            addLog('diagnosisResults', 'Step 3: Checking tables...', 'info');
            await checkTables();
            
            // Step 4: Permissions
            addLog('diagnosisResults', 'Step 4: Testing permissions...', 'info');
            await testPermissions();
            
            // Step 5: Application state
            addLog('diagnosisResults', 'Step 5: Checking application state...', 'info');
            
            // Check if form handler is properly initialized
            if (typeof initializeFormHandler === 'function') {
                addLog('diagnosisResults', '✅ Form handler function available', 'success');
            } else {
                addLog('diagnosisResults', '❌ Form handler function missing', 'error');
            }
            
            // Check if save functions are available
            if (typeof saveDataToLocalStorage === 'function') {
                addLog('diagnosisResults', '✅ saveDataToLocalStorage function available', 'success');
            } else {
                addLog('diagnosisResults', '❌ saveDataToLocalStorage function missing', 'error');
            }
            
            addLog('diagnosisResults', '🏁 Diagnosis complete! Check individual sections for details.', 'success');
        };

        // Inspect database
        window.inspectDatabase = async function() {
            if (!currentUser) {
                addLog('databaseInspection', '❌ User not authenticated', 'error');
                return;
            }

            addLog('databaseInspection', '🗄️ Inspecting database contents...', 'info');

            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .order('created_at', { ascending: false });

                if (error) {
                    addLog('databaseInspection', `❌ Database query failed: ${error.message}`, 'error');
                } else {
                    addLog('databaseInspection', `✅ Found ${data.length} total records for user`, 'success');
                    
                    if (data.length > 0) {
                        const pre = document.createElement('pre');
                        pre.textContent = JSON.stringify(data, null, 2);
                        document.getElementById('databaseInspection').appendChild(pre);
                    } else {
                        addLog('databaseInspection', '📭 No records found in database for this user', 'warning');
                    }
                }
            } catch (error) {
                addLog('databaseInspection', `❌ Database inspection failed: ${error.message}`, 'error');
            }
        };

        // Clear user data
        window.clearUserData = async function() {
            if (!currentUser) {
                addLog('databaseInspection', '❌ User not authenticated', 'error');
                return;
            }

            if (!confirm('Are you sure you want to clear all your data from the database?')) {
                return;
            }

            addLog('databaseInspection', '🗑️ Clearing user data...', 'info');

            try {
                const { error } = await supabase
                    .from('gas_analytes')
                    .delete()
                    .eq('user_id', currentUser.id);

                if (error) {
                    addLog('databaseInspection', `❌ Clear failed: ${error.message}`, 'error');
                } else {
                    addLog('databaseInspection', '✅ User data cleared successfully', 'success');
                }
            } catch (error) {
                addLog('databaseInspection', `❌ Clear exception: ${error.message}`, 'error');
            }
        };

        // Clear logs
        window.clearLogs = function() {
            document.getElementById('debugLog').innerHTML = '';
        };

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', async function() {
            addLog('debugLog', '🚀 Debug page loaded, initializing...', 'info');
            
            // Auto-run basic checks
            setTimeout(checkAuthentication, 500);
            setTimeout(testConnection, 1000);
            setTimeout(checkTables, 1500);
        });
    </script>
</body>
</html>
