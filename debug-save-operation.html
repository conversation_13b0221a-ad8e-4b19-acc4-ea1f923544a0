<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Save Operation</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            background: #f8f9fa;
        }
        .status-good { border-color: #28a745; background: #d4edda; }
        .status-bad { border-color: #dc3545; background: #f8d7da; }
        .status-warning { border-color: #ffc107; background: #fff3cd; }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover { background: #005a9e; }
        .test-button:disabled { background: #ccc; cursor: not-allowed; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
        .form-section {
            margin: 15px 0;
        }
        .form-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Save Operation</h1>
        <p>This tool debugs the exact save operation flow used by the "Add to Chart" button.</p>

        <div id="authCheck" class="test-section">
            <h3>🔐 Authentication Check</h3>
            <p id="authResult">Checking...</p>
            <button class="test-button" onclick="checkAuth()">Check Auth Status</button>
        </div>

        <div id="testForm" class="test-section">
            <h3>📝 Test Data Entry</h3>
            <div class="form-section">
                <label>Analyte Name:</label>
                <input type="text" id="testAnalyteName" class="saas-input" value="Debug Test Analyte" placeholder="Enter analyte name">
            </div>
            <div class="form-section">
                <label>Gap Notes:</label>
                <input type="text" id="testGapNotes" class="saas-input" value="Testing save operation" placeholder="Enter notes">
            </div>
            <button class="test-button" onclick="simulateAddToChart()" id="addBtn">🧪 Simulate "Add to Chart"</button>
        </div>

        <div id="saveResult" class="test-section">
            <h3>💾 Save Operation Result</h3>
            <p id="saveStatus">Ready to test...</p>
        </div>

        <div id="logs" class="test-section">
            <h3>📋 Detailed Logs</h3>
            <pre id="logOutput" class="log"></pre>
            <button class="test-button" onclick="clearLogs()">Clear Logs</button>
        </div>

        <div id="actions">
            <button class="test-button" onclick="goToMainApp()">Go to Main App</button>
            <button class="test-button" onclick="testDirectSave()">Test Direct Database Save</button>
        </div>
    </div>

    <script type="module">
        // Import modules
        import { supabase } from './supabase-client.js';
        import { authManager } from './auth.js';

        // Make available globally
        window.authManager = authManager;
        window.supabase = supabase;

        let logMessages = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logMessages.push(logEntry);
            console.log(logEntry);
            document.getElementById('logOutput').textContent = logMessages.join('\n');
            document.getElementById('logOutput').scrollTop = document.getElementById('logOutput').scrollHeight;
        }

        window.log = log;

        // Mock gasData array like in the main app
        window.gasData = [];

        // Import storage functions
        let saveDataToLocalStorage;
        
        async function loadStorageFunctions() {
            try {
                const storageModule = await import('./storage.js');
                // Wait a bit for global assignments
                setTimeout(() => {
                    saveDataToLocalStorage = window.saveDataToLocalStorage;
                    if (saveDataToLocalStorage) {
                        log('✅ Storage functions loaded successfully');
                    } else {
                        log('❌ saveDataToLocalStorage not available globally', 'error');
                    }
                }, 100);
            } catch (error) {
                log(`❌ Error loading storage functions: ${error.message}`, 'error');
            }
        }

        window.checkAuth = async function() {
            const authCheck = document.getElementById('authCheck');
            const authResult = document.getElementById('authResult');
            
            try {
                log('🔍 Checking authentication status...');
                
                // Initialize auth manager
                const user = await authManager.init();
                
                if (user) {
                    authCheck.className = 'test-section status-good';
                    authResult.innerHTML = `✅ Authenticated as: <strong>${user.email}</strong><br>User ID: ${user.id}`;
                    log(`✅ Authentication SUCCESS: ${user.email}`, 'success');
                    
                    // Test auth.uid() equivalent
                    const { data: { user: currentUser } } = await supabase.auth.getUser();
                    if (currentUser) {
                        log(`✅ Supabase auth.getUser() returns: ${currentUser.id}`, 'success');
                    } else {
                        log('❌ Supabase auth.getUser() returns null', 'error');
                    }
                    
                    return true;
                } else {
                    authCheck.className = 'test-section status-bad';
                    authResult.innerHTML = `❌ Not authenticated - Please login first`;
                    log('❌ Authentication FAILED: No user session', 'error');
                    return false;
                }
            } catch (error) {
                authCheck.className = 'test-section status-bad';
                authResult.innerHTML = `❌ Authentication error: ${error.message}`;
                log(`❌ Authentication ERROR: ${error.message}`, 'error');
                return false;
            }
        };

        window.simulateAddToChart = async function() {
            const addBtn = document.getElementById('addBtn');
            const saveResult = document.getElementById('saveResult');
            const saveStatus = document.getElementById('saveStatus');
            
            addBtn.disabled = true;
            addBtn.textContent = '🔄 Testing...';
            
            try {
                log('🧪 === SIMULATING ADD TO CHART OPERATION ===');
                
                // Get form data
                const analyteName = document.getElementById('testAnalyteName').value.trim();
                const gapNotes = document.getElementById('testGapNotes').value.trim();
                
                if (!analyteName) {
                    throw new Error('Analyte name is required');
                }
                
                log(`📝 Form data: Name="${analyteName}", Notes="${gapNotes}"`);
                
                // Check authentication (same as form-handler.js)
                if (typeof authManager !== 'undefined') {
                    if (!authManager.isAuthenticated()) {
                        throw new Error('User not authenticated');
                    }
                    log('✅ Authentication check passed');
                } else {
                    log('⚠️ authManager not available', 'warning');
                }
                
                // Add to gasData array (same as form-handler.js)
                const newAnalyte = {
                    name: analyteName,
                    current: [{ min: 10, max: 100, label: 'Test Current' }],
                    target: [{ min: 5, max: 200, label: 'Test Target' }],
                    gapNotes: gapNotes,
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };
                
                gasData.push(newAnalyte);
                log(`✅ Added to gasData array. Total items: ${gasData.length}`);
                
                // Ensure authentication is ready (same as form-handler.js)
                if (typeof authManager !== 'undefined' && authManager.isAuthenticated()) {
                    log('🔐 Ensuring authentication is ready before save...');
                    await authManager.ensureReady();
                    log('✅ Authentication confirmed ready, proceeding with save');
                }
                
                // Call save function (same as form-handler.js)
                if (typeof saveDataToLocalStorage === 'function') {
                    log('💾 Calling saveDataToLocalStorage...');
                    await saveDataToLocalStorage();
                    log('✅ Save operation completed successfully');
                    
                    saveResult.className = 'test-section status-good';
                    saveStatus.innerHTML = `✅ Save operation SUCCESSFUL!<br>Data added to gasData and saved to database.`;
                } else {
                    throw new Error('saveDataToLocalStorage function not available');
                }
                
            } catch (error) {
                log(`❌ Save operation FAILED: ${error.message}`, 'error');
                saveResult.className = 'test-section status-bad';
                saveStatus.innerHTML = `❌ Save operation FAILED: ${error.message}`;
            } finally {
                addBtn.disabled = false;
                addBtn.textContent = '🧪 Simulate "Add to Chart"';
            }
        };

        window.testDirectSave = async function() {
            try {
                log('🚀 === TESTING DIRECT DATABASE SAVE ===');
                
                const user = await supabase.auth.getUser();
                if (!user.data.user) {
                    throw new Error('User not authenticated');
                }
                
                log(`🔐 User authenticated: ${user.data.user.email}`);
                
                const testData = {
                    user_id: user.data.user.id,
                    name: `Direct Test ${Date.now()}`,
                    current_ranges: [{ min: 1, max: 10, label: 'Direct Test' }],
                    target_ranges: [{ min: 1, max: 20, label: 'Direct Target' }],
                    gap_notes: 'Direct database test',
                    is_custom: true
                };
                
                log('📤 Inserting test data directly to database...');
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert([testData])
                    .select();
                
                if (error) {
                    throw new Error(`Database insert failed: ${error.message}`);
                }
                
                log(`✅ Direct database save SUCCESS! Inserted ID: ${data[0].id}`, 'success');
                
                // Clean up
                const { error: deleteError } = await supabase
                    .from('gas_analytes')
                    .delete()
                    .eq('id', data[0].id);
                
                if (!deleteError) {
                    log('🧹 Test data cleaned up successfully');
                }
                
            } catch (error) {
                log(`❌ Direct database save FAILED: ${error.message}`, 'error');
            }
        };

        window.clearLogs = function() {
            logMessages = [];
            document.getElementById('logOutput').textContent = '';
        };

        window.goToMainApp = function() {
            window.location.href = './app.html';
        };

        // Initialize
        async function init() {
            log('🔧 Initializing debug save operation tool...');
            await loadStorageFunctions();
            await checkAuth();
            log('✅ Debug tool ready');
        }

        init();
    </script>
</body>
</html>
