#!/bin/bash

# Vercel Deployment Script for Alpha Gas Solution RBAC System
# Usage: ./deploy-vercel.sh [preview|production]

set -e

# Configuration
DEPLOYMENT_TYPE=${1:-preview}
PROJECT_NAME="alpha-gas-solution-rbac"
LOG_FILE="./vercel_deployment_$(date +%Y%m%d_%H%M%S).log"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

# Check if Vercel CLI is installed
check_vercel_cli() {
    if ! command -v vercel &> /dev/null; then
        error "Vercel CLI not found. Install with: npm i -g vercel"
    fi
    success "Vercel CLI found"
}

# Verify required environment variables
check_environment_variables() {
    log "Checking required environment variables..."
    
    required_vars=()
    
    # Check if .env exists locally (for reference)
    if [[ -f ".env" ]]; then
        log "Local .env file found"
    else
        warning "No local .env file found. Make sure Vercel environment variables are configured."
    fi
    
    # Check Vercel environment variables
    log "Checking Vercel environment variables..."
    if ! vercel env ls &> /dev/null; then
        warning "Unable to check Vercel environment variables. Make sure you're logged in."
    else
        success "Vercel environment variables accessible"
    fi
}

# Validate project structure
validate_project_structure() {
    log "Validating project structure..."
    
    required_files=(
        "vercel.json"
        "auth.js"
        "role-manager.js"
        "error-handler.js"
        "performance-optimizer.js"
        "email-service.js"
        "app.html"
        "admin-dashboard.html"
        "system-monitor.html"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file missing: $file"
        fi
    done
    
    success "All required files present"
}

# Run pre-deployment tests
run_pre_deployment_tests() {
    log "Running pre-deployment tests..."
    
    # Check JavaScript syntax
    log "Validating JavaScript syntax..."
    for js_file in *.js; do
        if [[ -f "$js_file" ]]; then
            if command -v node &> /dev/null; then
                node -c "$js_file" || error "Syntax error in $js_file"
            fi
        fi
    done
    
    # Check if API functions exist
    if [[ -d "api" ]]; then
        log "API directory found, validating API functions..."
        for api_file in api/*.js; do
            if [[ -f "$api_file" ]]; then
                if command -v node &> /dev/null; then
                    node -c "$api_file" || warning "Syntax issue in $api_file"
                fi
            fi
        done
        success "API functions validated"
    fi
    
    success "Pre-deployment tests completed"
}

# Create deployment summary
create_deployment_summary() {
    log "Creating deployment summary..."
    
    cat > deployment-summary.md << EOF
# Vercel Deployment Summary

**Date**: $(date)
**Type**: $DEPLOYMENT_TYPE
**Project**: $PROJECT_NAME

## Files Deployed
- Authentication System: auth.js
- Role Management: role-manager.js  
- Error Handling: error-handler.js
- Performance Optimization: performance-optimizer.js
- Email Service: email-service.js
- Admin Dashboard: admin-dashboard.html
- System Monitor: system-monitor.html
- Main Application: app.html

## API Functions
$(find api -name "*.js" 2>/dev/null | sed 's/^/- /' || echo "- No API functions found")

## Configuration
- Vercel Config: vercel.json
- Security Headers: Configured
- Routing: SPA routing to app.html
- Functions Runtime: Node.js 18.x

## Post-Deployment Checklist
- [ ] Verify application loads
- [ ] Test authentication flow
- [ ] Check admin dashboard access
- [ ] Validate role-based permissions
- [ ] Test system monitoring
- [ ] Verify email notifications
EOF

    success "Deployment summary created"
}

# Deploy to Vercel
deploy_to_vercel() {
    log "Deploying to Vercel ($DEPLOYMENT_TYPE)..."
    
    if [[ "$DEPLOYMENT_TYPE" == "production" ]]; then
        log "🚀 Starting PRODUCTION deployment..."
        
        # Confirm production deployment
        echo -e "${YELLOW}⚠️  You are about to deploy to PRODUCTION!${NC}"
        echo "This will make the application live for all users."
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Production deployment cancelled by user"
            exit 0
        fi
        
        # Deploy to production
        vercel --prod --yes || error "Production deployment failed"
        
        success "🎉 Production deployment successful!"
        
    else
        log "🔄 Starting PREVIEW deployment..."
        
        # Deploy to preview
        vercel --yes || error "Preview deployment failed"
        
        success "✅ Preview deployment successful!"
    fi
}

# Get deployment URL
get_deployment_url() {
    log "Getting deployment URL..."
    
    # Get the latest deployment URL
    DEPLOYMENT_URL=$(vercel ls --limit=1 | grep https | awk '{print $2}' | head -1)
    
    if [[ -n "$DEPLOYMENT_URL" ]]; then
        success "Deployment URL: $DEPLOYMENT_URL"
        echo ""
        echo "🔗 Access your application:"
        echo "   Main App: $DEPLOYMENT_URL"
        echo "   Admin Dashboard: $DEPLOYMENT_URL/admin-dashboard.html"
        echo "   System Monitor: $DEPLOYMENT_URL/system-monitor.html"
        echo ""
    else
        warning "Could not retrieve deployment URL automatically"
        log "Check 'vercel ls' for deployment details"
    fi
}

# Post-deployment verification
post_deployment_verification() {
    log "Running post-deployment verification..."
    
    if [[ -n "$DEPLOYMENT_URL" ]]; then
        log "Testing deployment URL: $DEPLOYMENT_URL"
        
        # Test if the main page loads
        if command -v curl &> /dev/null; then
            HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOYMENT_URL" || echo "000")
            
            if [[ "$HTTP_STATUS" == "200" ]]; then
                success "Main application responds with HTTP 200"
            else
                warning "Main application returned HTTP $HTTP_STATUS"
            fi
        else
            warning "curl not available, skipping HTTP status check"
        fi
        
    else
        warning "No deployment URL available for verification"
    fi
    
    # Deployment instructions
    echo ""
    echo "📋 POST-DEPLOYMENT STEPS:"
    echo "1. Test the deployment URL in your browser"
    echo "2. Verify authentication system works"
    echo "3. Check admin dashboard access"
    echo "4. Test user registration flow"
    echo "5. Validate role-based permissions"
    echo "6. Monitor system performance"
    echo ""
    
    success "Post-deployment verification completed"
}

# Clean up temporary files
cleanup() {
    log "Cleaning up temporary files..."
    
    # Remove deployment summary if not in production
    if [[ "$DEPLOYMENT_TYPE" != "production" ]] && [[ -f "deployment-summary.md" ]]; then
        rm deployment-summary.md
    fi
    
    success "Cleanup completed"
}

# Main deployment process
main() {
    echo "🚀 Vercel Deployment Script for Alpha Gas Solution RBAC"
    echo "============================================================"
    log "Starting $DEPLOYMENT_TYPE deployment..."
    
    # Pre-deployment checks
    check_vercel_cli
    check_environment_variables  
    validate_project_structure
    run_pre_deployment_tests
    create_deployment_summary
    
    # Deploy
    deploy_to_vercel
    get_deployment_url
    post_deployment_verification
    
    # Cleanup
    cleanup
    
    success "🎉 Vercel deployment completed successfully!"
    log "Deployment log saved to: $LOG_FILE"
    
    if [[ "$DEPLOYMENT_TYPE" == "production" ]]; then
        echo ""
        echo "🎊 PRODUCTION DEPLOYMENT COMPLETE!"
        echo "Your Alpha Gas Solution RBAC system is now live!"
        echo ""
        echo "Next steps:"
        echo "- Create your admin user account"
        echo "- Configure user approval workflow"
        echo "- Monitor system performance"
        echo "- Review deployment logs"
    else
        echo ""
        echo "✅ PREVIEW DEPLOYMENT COMPLETE!"
        echo "Test your application thoroughly before production deployment."
        echo ""
        echo "When ready for production, run:"
        echo "  ./deploy-vercel.sh production"
    fi
}

# Show usage if invalid argument
if [[ "$1" != "preview" ]] && [[ "$1" != "production" ]] && [[ $# -gt 0 ]]; then
    echo "Usage: $0 [preview|production]"
    echo ""
    echo "Examples:"
    echo "  $0 preview     # Deploy to Vercel preview environment"
    echo "  $0 production  # Deploy to Vercel production"
    echo "  $0             # Deploy to preview (default)"
    exit 1
fi

# Run main deployment
main
