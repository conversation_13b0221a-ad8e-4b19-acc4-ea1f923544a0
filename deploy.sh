#!/usr/bin/env zsh

# Production deployment script
echo "🚀 Preparing Alpha Gas Solution for Production Deployment"
echo "========================================================"

# Check if we're in the right directory
if [[ ! -f "package.json" ]]; then
    echo "❌ Error: package.json not found. Run this script from the project root."
    exit 1
fi

# Check if .env exists but warn about deployment
if [[ -f ".env" ]]; then
    echo "⚠️  Warning: .env file found. This should NOT be deployed to production."
    echo "   Make sure to set environment variables on your hosting platform instead."
fi

# Install dependencies
echo "\n📦 Installing dependencies..."
npm install

# Run tests
echo "\n🧪 Running tests..."
if ./test.sh > /dev/null 2>&1; then
    echo "✅ All tests passed"
else
    echo "❌ Tests failed. Please check your configuration."
    exit 1
fi

# Create production build info
echo "\n📝 Creating production build info..."
cat > build-info.json << EOF
{
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "version": "$(node -p "require('./package.json').version")",
  "gitCommit": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')",
  "nodeVersion": "$(node --version)"
}
EOF

echo "✅ Production build ready!"
echo "\n📋 Deployment Checklist:"
echo "   [ ] Set GEMINI_API_KEY environment variable on hosting platform"
echo "   [ ] Configure PORT if needed (default: 3000)"
echo "   [ ] Deploy all files except .env and node_modules"
echo "   [ ] Test the deployed application"

echo "\n🔗 Useful commands:"
echo "   • Test locally: npm start"
echo "   • View logs: check your hosting platform's logs"
echo "   • Update deps: npm update"

echo "\n📖 See DEPLOYMENT.md for platform-specific instructions"
