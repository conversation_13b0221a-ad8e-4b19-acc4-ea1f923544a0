<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication & RLS Diagnostic Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .diagnostic-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-left: 10px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #007bff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-insert {
            background-color: #28a745;
        }
        .test-insert:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Authentication & RLS Diagnostic Tool</h1>
        <p>This tool diagnoses authentication and Row Level Security (RLS) issues preventing data persistence.</p>

        <div class="diagnostic-section">
            <h2>1. Authentication Status</h2>
            <button onclick="checkAuthStatus()">Check Authentication</button>
            <div id="auth-status"></div>
        </div>

        <div class="diagnostic-section">
            <h2>2. RLS Function Tests</h2>
            <button onclick="testRLSFunctions()">Test RLS Functions</button>
            <div id="rls-functions"></div>
        </div>

        <div class="diagnostic-section">
            <h2>3. Database Connection Test</h2>
            <button onclick="testDatabaseConnection()">Test Database Connection</button>
            <div id="db-connection"></div>
        </div>

        <div class="diagnostic-section">
            <h2>4. Test Insert Operation</h2>
            <button class="test-insert" onclick="testInsertOperation()">Test Insert New Analyte</button>
            <div id="insert-test"></div>
        </div>

        <div class="diagnostic-section">
            <h2>5. Fix Authentication Issues</h2>
            <button onclick="initializeAuth()">Initialize Authentication</button>
            <button onclick="loginAsAdmin()">Login as Admin</button>
            <button onclick="signUpAsAdmin()">Create Admin Account</button>
            <div id="auth-fix"></div>
        </div>

        <div class="diagnostic-section">
            <h2>6. Direct Database Test</h2>
            <button onclick="testDirectQuery()">Test Direct Query</button>
            <button onclick="createUserProfile()">Create User Profile</button>
            <div id="direct-insert"></div>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/+esm';

        // Initialize Supabase clients
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';

        const supabase = createClient(supabaseUrl, supabaseAnonKey);

        window.checkAuthStatus = async function() {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = '<p>Checking authentication status...</p>';

            try {
                // Check current session
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                
                let html = '<h3>Authentication Status:</h3>';
                
                if (sessionError) {
                    html += `<p>Session Error: <span class="status error">${sessionError.message}</span></p>`;
                }
                
                if (session) {
                    html += `<p>Authenticated: <span class="status success">YES</span></p>`;
                    html += `<p>User ID: <code>${session.user.id}</code></p>`;
                    html += `<p>Email: <code>${session.user.email}</code></p>`;
                    html += `<p>Role: <code>${session.user.role || 'Not set'}</code></p>`;
                    html += `<p>Session Expires: <code>${new Date(session.expires_at * 1000).toLocaleString()}</code></p>`;
                } else {
                    html += `<p>Authenticated: <span class="status error">NO</span></p>`;
                    html += `<p>No active session found.</p>`;
                }

                // Check user data
                const { data: { user } } = await supabase.auth.getUser();
                if (user) {
                    html += `<h4>User Details:</h4>`;
                    html += `<pre>${JSON.stringify(user, null, 2)}</pre>`;
                }

                statusDiv.innerHTML = html;
            } catch (error) {
                statusDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.testRLSFunctions = async function() {
            const rlsDiv = document.getElementById('rls-functions');
            rlsDiv.innerHTML = '<p>Testing RLS functions...</p>';

            try {
                const { data, error } = await supabase.rpc('sql', {
                    query: `SELECT 
                        auth.uid() as current_user_id,
                        is_user_approved() as user_approved,
                        get_user_role() as user_role,
                        is_admin() as is_admin_check;`
                });

                let html = '<h3>RLS Functions Test:</h3>';
                
                if (error) {
                    html += `<p class="status error">Error: ${error.message}</p>`;
                } else {
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
                    if (data && data.length > 0) {
                        const result = data[0];
                        html += '<h4>Analysis:</h4>';
                        html += `<p>Current User ID: ${result.current_user_id ? '<span class="status success">Set</span>' : '<span class="status error">NULL</span>'}</p>`;
                        html += `<p>User Approved: ${result.user_approved ? '<span class="status success">Yes</span>' : '<span class="status error">No/NULL</span>'}</p>`;
                        html += `<p>User Role: ${result.user_role ? '<span class="status success">' + result.user_role + '</span>' : '<span class="status error">NULL</span>'}</p>`;
                        html += `<p>Is Admin: ${result.is_admin_check ? '<span class="status success">Yes</span>' : '<span class="status error">No/NULL</span>'}</p>`;
                    }
                }

                rlsDiv.innerHTML = html;
            } catch (error) {
                rlsDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.testDatabaseConnection = async function() {
            const dbDiv = document.getElementById('db-connection');
            dbDiv.innerHTML = '<p>Testing database connection...</p>';

            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count')
                    .limit(1);

                let html = '<h3>Database Connection Test:</h3>';
                
                if (error) {
                    html += `<p class="status error">Connection Error: ${error.message}</p>`;
                } else {
                    html += `<p>Connection: <span class="status success">OK</span></p>`;
                    html += `<p>Can read gas_analytes table</p>`;
                }

                dbDiv.innerHTML = html;
            } catch (error) {
                dbDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.testInsertOperation = async function() {
            const insertDiv = document.getElementById('insert-test');
            insertDiv.innerHTML = '<p>Testing insert operation...</p>';

            try {
                const testAnalyte = {
                    name: 'Test Analyte ' + Date.now(),
                    current_ranges: [{ min: 1, max: 100, label: 'Test Range' }],
                    target_ranges: [{ min: 1, max: 1000, label: 'Test Target' }],
                    gap_notes: 'Test analyte for diagnostic purposes',
                    is_custom: true,
                    is_shared: false,
                    user_id: null // Will be set by RLS
                };

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert([testAnalyte])
                    .select();

                let html = '<h3>Insert Operation Test:</h3>';
                
                if (error) {
                    html += `<p class="status error">Insert Failed: ${error.message}</p>`;
                    html += `<p>Error Code: ${error.code}</p>`;
                    html += `<p>Error Details: ${error.details}</p>`;
                    html += `<p>Hint: ${error.hint}</p>`;
                } else {
                    html += `<p>Insert: <span class="status success">SUCCESS</span></p>`;
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }

                insertDiv.innerHTML = html;
            } catch (error) {
                insertDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.initializeAuth = async function() {
            const authDiv = document.getElementById('auth-fix');
            authDiv.innerHTML = '<p>Initializing authentication...</p>';

            try {
                // Check if already authenticated
                const { data: { session } } = await supabase.auth.getSession();
                
                let html = '<h3>Authentication Initialization:</h3>';
                
                if (session) {
                    html += `<p>Already authenticated as: <span class="status success">${session.user.email}</span></p>`;
                } else {
                    html += `<p>No active session. <span class="status warning">Need to login</span></p>`;
                    html += `<p>Please use the "Login as Admin" button to authenticate.</p>`;
                }

                authDiv.innerHTML = html;
            } catch (error) {
                authDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.loginAsAdmin = async function() {
            const authDiv = document.getElementById('auth-fix');
            authDiv.innerHTML = '<p>Attempting to login as admin...</p>';

            try {
                // First try to get existing session
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    let html = '<h3>Already Authenticated:</h3>';
                    html += `<p>Already logged in as: <span class="status success">${session.user.email}</span></p>`;
                    html += `<p>User ID: <code>${session.user.id}</code></p>`;
                    authDiv.innerHTML = html;
                    return;
                }

                // Prompt for password
                const password = prompt('Enter <NAME_EMAIL>:');
                if (!password) {
                    authDiv.innerHTML = '<p class="status warning">Login cancelled</p>';
                    return;
                }

                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: password
                });

                let html = '<h3>Admin Login Attempt:</h3>';
                
                if (error) {
                    html += `<p class="status error">Login Failed: ${error.message}</p>`;
                    if (error.message.includes('Invalid login credentials')) {
                        html += `<p>Please check your password. If you don't have an account, use the signup option.</p>`;
                        html += `<button onclick="signUpAsAdmin()" style="background-color: #28a745;">Create Admin Account</button>`;
                    }
                } else {
                    html += `<p>Login: <span class="status success">SUCCESS</span></p>`;
                    html += `<p>Logged in as: <code>${data.user.email}</code></p>`;
                    html += `<p>User ID: <code>${data.user.id}</code></p>`;
                    html += `<p>Now retry the insert test to see if it works.</p>`;
                }

                authDiv.innerHTML = html;
            } catch (error) {
                authDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.signUpAsAdmin = async function() {
            const authDiv = document.getElementById('auth-fix');
            authDiv.innerHTML = '<p>Creating admin account...</p>';

            try {
                const password = prompt('Create <NAME_EMAIL>:');
                if (!password) {
                    authDiv.innerHTML = '<p class="status warning">Signup cancelled</p>';
                    return;
                }

                const { data, error } = await supabase.auth.signUp({
                    email: '<EMAIL>',
                    password: password,
                    options: {
                        data: {
                            full_name: 'Tommy Lee',
                            role: 'admin'
                        }
                    }
                });

                let html = '<h3>Admin Signup Attempt:</h3>';
                
                if (error) {
                    html += `<p class="status error">Signup Failed: ${error.message}</p>`;
                } else {
                    html += `<p>Signup: <span class="status success">SUCCESS</span></p>`;
                    html += `<p>Account created for: <code>${data.user?.email}</code></p>`;
                    html += `<p>Check your email for verification link (if email confirmation is enabled)</p>`;
                    html += `<p>User ID: <code>${data.user?.id}</code></p>`;
                }

                authDiv.innerHTML = html;
            } catch (error) {
                authDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.testDirectQuery = async function() {
            const directDiv = document.getElementById('direct-insert');
            directDiv.innerHTML = '<p>Testing direct database query...</p>';

            try {
                // Test basic database connection
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .limit(5);

                let html = '<h3>Direct Database Query:</h3>';
                
                if (error) {
                    html += `<p class="status error">Query Failed: ${error.message}</p>`;
                } else {
                    html += `<p>Query: <span class="status success">SUCCESS</span></p>`;
                    html += `<p>Found ${data.length} records</p>`;
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }

                directDiv.innerHTML = html;
            } catch (error) {
                directDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.createUserProfile = async function() {
            const directDiv = document.getElementById('direct-insert');
            directDiv.innerHTML = '<p>Creating user profile...</p>';

            try {
                const { data: { user } } = await supabase.auth.getUser();
                
                if (!user) {
                    directDiv.innerHTML = '<p class="status error">No authenticated user. Please login first.</p>';
                    return;
                }

                // Check if profile already exists
                const { data: existingProfile } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();

                let html = '<h3>User Profile Status:</h3>';
                
                if (existingProfile) {
                    html += `<p>Profile: <span class="status success">EXISTS</span></p>`;
                    html += '<pre>' + JSON.stringify(existingProfile, null, 2) + '</pre>';
                } else {
                    // Create profile
                    const { data: newProfile, error } = await supabase
                        .from('user_profiles')
                        .insert([{
                            user_id: user.id,
                            email: user.email,
                            full_name: 'Tommy Lee',
                            role: 'admin',
                            status: 'approved',
                            approved_by: user.id,
                            approved_at: new Date().toISOString()
                        }])
                        .select()
                        .single();

                    if (error) {
                        html += `<p class="status error">Profile Creation Failed: ${error.message}</p>`;
                    } else {
                        html += `<p>Profile: <span class="status success">CREATED</span></p>`;
                        html += '<pre>' + JSON.stringify(newProfile, null, 2) + '</pre>';
                    }
                }

                directDiv.innerHTML = html;
            } catch (error) {
                directDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        window.testDirectInsert = async function() {
            const directDiv = document.getElementById('direct-insert');
            directDiv.innerHTML = '<p>Testing insert with authenticated user...</p>';

            try {
                const { data: { user } } = await supabase.auth.getUser();
                
                if (!user) {
                    directDiv.innerHTML = '<p class="status error">No authenticated user. Please login first.</p>';
                    return;
                }

                const testAnalyte = {
                    name: 'Test Analyte ' + Date.now(),
                    current_ranges: [{ min: 1, max: 100, label: 'Test Range' }],
                    target_ranges: [{ min: 1, max: 1000, label: 'Test Target' }],
                    gap_notes: 'Test analyte for authentication verification',
                    is_custom: true,
                    is_shared: false,
                    user_id: user.id // Explicitly set user_id
                };

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert([testAnalyte])
                    .select();

                let html = '<h3>Authenticated Insert Test:</h3>';
                
                if (error) {
                    html += `<p class="status error">Insert Failed: ${error.message}</p>`;
                    html += `<p>Error Code: ${error.code}</p>`;
                    html += `<p>Error Details: ${error.details}</p>`;
                } else {
                    html += `<p>Insert: <span class="status success">SUCCESS!</span></p>`;
                    html += '<p>🎉 Data persistence is now working!</p>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }

                directDiv.innerHTML = html;
            } catch (error) {
                directDiv.innerHTML = `<p class="status error">Error: ${error.message}</p>`;
            }
        };

        // Auto-run basic checks when page loads
        window.addEventListener('load', () => {
            checkAuthStatus();
            testRLSFunctions();
        });
    </script>
</body>
</html>
