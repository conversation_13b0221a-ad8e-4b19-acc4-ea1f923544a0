<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🕐 Authentication Speed Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .timing {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
        .step {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
        }
        .fast { background: rgba(0, 255, 0, 0.2); }
        .slow { background: rgba(255, 165, 0, 0.2); }
        .error { background: rgba(255, 0, 0, 0.2); }
        .loading { color: #ffd700; }
        .success { color: #90EE90; }
        .warning { color: #FFA500; }
        .error-text { color: #FF6B6B; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 Authentication Speed Diagnostic</h1>
        <p>This tool measures each step of the authentication process to identify bottlenecks.</p>

        <div class="timing">
            <h3>⏱️ Timing Results</h3>
            <div id="timingResults">Starting diagnostic...</div>
        </div>

        <div>
            <h3>🔍 Step-by-Step Analysis</h3>
            <div id="stepAnalysis"></div>
        </div>

        <div>
            <button onclick="runDiagnostic()" style="background: #4299e1; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;">
                Run Diagnostic Again
            </button>
            <button onclick="testLoginRedirect()" style="background: #48bb78; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;">
                Test Login Redirect
            </button>
        </div>

        <div class="timing" id="detailedLog" style="max-height: 300px; overflow-y: auto; margin-top: 20px;">
            <h3>📋 Detailed Log</h3>
            <div id="logContent"></div>
        </div>
    </div>

    <script type="module">
        let startTime = Date.now();
        let timings = {};

        function addTiming(step, duration) {
            timings[step] = duration;
            updateTimingDisplay();
        }

        function updateTimingDisplay() {
            const timingDiv = document.getElementById('timingResults');
            const totalTime = Object.values(timings).reduce((sum, time) => sum + time, 0);
            
            let html = `<strong>Total Time: ${totalTime}ms</strong><br>`;
            Object.entries(timings).forEach(([step, time]) => {
                const status = time > 2000 ? 'slow' : time > 500 ? 'warning' : 'fast';
                html += `<span class="${status}">${step}: ${time}ms</span><br>`;
            });
            timingDiv.innerHTML = html;
        }

        function addStep(step, status, details) {
            const stepDiv = document.getElementById('stepAnalysis');
            const stepElement = document.createElement('div');
            stepElement.className = `step ${status}`;
            stepElement.innerHTML = `<strong>${step}</strong>: ${details}`;
            stepDiv.appendChild(stepElement);
        }

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logContent');
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[AUTH-DIAGNOSTIC] ${message}`);
        }

        // Main diagnostic function
        async function runDiagnostic() {
            document.getElementById('stepAnalysis').innerHTML = '';
            document.getElementById('logContent').innerHTML = '';
            timings = {};
            
            log('🚀 Starting authentication speed diagnostic...');
            
            const overallStart = performance.now();

            try {
                // Step 1: Import auth module
                log('📥 Step 1: Importing auth module...');
                const importStart = performance.now();
                const { authManager } = await import('./auth.js');
                const importTime = performance.now() - importStart;
                addTiming('Import Auth Module', Math.round(importTime));
                addStep('Import Auth Module', importTime > 500 ? 'slow' : 'fast', 
                    `${Math.round(importTime)}ms - ${importTime > 500 ? 'SLOW: Module loading issue' : 'OK'}`);
                log(`✅ Auth module imported in ${Math.round(importTime)}ms`);

                // Step 2: Supabase client initialization  
                log('🔗 Step 2: Testing Supabase connection...');
                const supabaseStart = performance.now();
                const { supabase } = await import('./supabase-client.js');
                const connectionTime = performance.now() - supabaseStart;
                addTiming('Supabase Connection', Math.round(connectionTime));
                addStep('Supabase Connection', connectionTime > 1000 ? 'slow' : 'fast',
                    `${Math.round(connectionTime)}ms - ${connectionTime > 1000 ? 'SLOW: Network or config issue' : 'OK'}`);
                log(`✅ Supabase client loaded in ${Math.round(connectionTime)}ms`);

                // Step 3: Session check
                log('🔍 Step 3: Checking existing session...');
                const sessionStart = performance.now();
                const { data: { session } } = await supabase.auth.getSession();
                const sessionTime = performance.now() - sessionStart;
                addTiming('Session Check', Math.round(sessionTime));
                addStep('Session Check', sessionTime > 2000 ? 'slow' : 'fast',
                    `${Math.round(sessionTime)}ms - ${session ? 'User found' : 'No user'} - ${sessionTime > 2000 ? 'SLOW: Network timeout' : 'OK'}`);
                log(`✅ Session check completed in ${Math.round(sessionTime)}ms - ${session ? `Found user: ${session.user.email}` : 'No user found'}`);

                // Step 4: Full auth manager init
                log('🔐 Step 4: Full auth manager initialization...');
                const authStart = performance.now();
                const user = await authManager.init();
                const authTime = performance.now() - authStart;
                addTiming('Auth Manager Init', Math.round(authTime));
                addStep('Auth Manager Init', authTime > 3000 ? 'slow' : 'fast',
                    `${Math.round(authTime)}ms - ${user ? 'Authenticated' : 'Not authenticated'} - ${authTime > 3000 ? 'SLOW: Auth processing issue' : 'OK'}`);
                log(`✅ Auth manager initialization completed in ${Math.round(authTime)}ms`);

                // Step 5: Test database connection if authenticated
                if (user) {
                    log('📊 Step 5: Testing database connection...');
                    const dbStart = performance.now();
                    try {
                        const { data, error } = await supabase
                            .from('gas_analytes')
                            .select('count(*)')
                            .limit(1);
                        const dbTime = performance.now() - dbStart;
                        addTiming('Database Test', Math.round(dbTime));
                        addStep('Database Test', dbTime > 2000 ? 'slow' : 'fast',
                            `${Math.round(dbTime)}ms - ${error ? 'Error: ' + error.message : 'Connected'} - ${dbTime > 2000 ? 'SLOW: Database performance issue' : 'OK'}`);
                        log(`✅ Database test completed in ${Math.round(dbTime)}ms`);
                    } catch (error) {
                        log(`❌ Database test failed: ${error.message}`);
                        addStep('Database Test', 'error', `Error: ${error.message}`);
                    }
                }

                const totalTime = performance.now() - overallStart;
                log(`🏁 Total diagnostic time: ${Math.round(totalTime)}ms`);

                // Analysis and recommendations
                log('📋 Analysis:');
                if (totalTime > 5000) {
                    log('⚠️  SLOW: Total time > 5 seconds - Users will experience significant delay');
                    addStep('Overall Performance', 'error', `${Math.round(totalTime)}ms - UNACCEPTABLE for user experience`);
                } else if (totalTime > 2000) {
                    log('⚠️  MODERATE: Total time > 2 seconds - Noticeable delay');
                    addStep('Overall Performance', 'slow', `${Math.round(totalTime)}ms - Could be improved`);
                } else {
                    log('✅ GOOD: Total time < 2 seconds - Acceptable performance');
                    addStep('Overall Performance', 'fast', `${Math.round(totalTime)}ms - Good user experience`);
                }

                // Recommendations
                if (timings['Supabase Connection'] > 1000) {
                    log('💡 Recommendation: Supabase connection is slow - check network or region settings');
                }
                if (timings['Session Check'] > 2000) {
                    log('💡 Recommendation: Session check is slow - may be a network timeout issue');
                }
                if (timings['Import Auth Module'] > 500) {
                    log('💡 Recommendation: Module import is slow - consider code splitting or caching');
                }

            } catch (error) {
                log(`❌ Diagnostic failed: ${error.message}`);
                addStep('Diagnostic', 'error', `Failed: ${error.message}`);
            }
        }

        // Test login redirect
        window.testLoginRedirect = function() {
            log('🔀 Testing login redirect...');
            const start = performance.now();
            
            // Simulate what happens in index.html for unauthenticated users
            setTimeout(() => {
                const redirectTime = performance.now() - start;
                log(`🚀 Redirect would happen after ${Math.round(redirectTime)}ms`);
                log('Opening login page in new tab...');
                window.open('./login.html', '_blank');
            }, 100);
        };

        // Make runDiagnostic available globally
        window.runDiagnostic = runDiagnostic;

        // Auto-run diagnostic on load
        window.addEventListener('load', () => {
            log('🧪 Auth speed diagnostic tool loaded');
            runDiagnostic();
        });
    </script>
</body>
</html>
