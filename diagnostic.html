<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Diagnostic Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 12px; margin: 10px 0; border-radius: 6px; border: 1px solid; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Supabase Diagnostic Tool</h1>
        <p>This tool will help diagnose and fix any issues with your Supabase integration.</p>
        
        <div class="test-section">
            <h3>📊 Connection Status</h3>
            <div id="connectionStatus"></div>
            <button onclick="testConnection()">Test Connection</button>
        </div>

        <div class="test-section">
            <h3>💾 Data Operations</h3>
            <div id="dataStatus"></div>
            <button onclick="testDataSave()">Test Save</button>
            <button onclick="testDataLoad()">Test Load</button>
            <button onclick="viewSessionData()">View Session Data</button>
        </div>

        <div class="test-section">
            <h3>🔍 Raw Data Inspection</h3>
            <div id="rawDataDisplay"></div>
            <button onclick="inspectDatabase()">Inspect Database</button>
        </div>

        <div class="test-section">
            <h3>🧪 Test Adding New Data</h3>
            <input type="text" id="testAnalyteName" placeholder="Test Analyte Name" style="padding: 8px; margin: 5px; width: 200px;">
            <button onclick="addTestData()">Add Test Data</button>
            <div id="testResults"></div>
        </div>
    </div>

    <script type="module">
        import { supabase, supabaseStorage, userSession } from './supabase-client.js';
        
        window.supabase = supabase;
        window.supabaseStorage = supabaseStorage;
        window.userSession = userSession;

        function addStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            container.appendChild(div);
        }

        window.testConnection = async function() {
            const container = document.getElementById('connectionStatus');
            container.innerHTML = '';
            
            try {
                addStatus('connectionStatus', 'Testing Supabase connection...', 'info');
                
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .limit(1);
                
                if (error) {
                    addStatus('connectionStatus', `❌ Connection failed: ${error.message}`, 'error');
                    return;
                }
                
                addStatus('connectionStatus', '✅ Supabase connection successful!', 'success');
                addStatus('connectionStatus', `Session ID: ${userSession}`, 'info');
                
            } catch (error) {
                addStatus('connectionStatus', `❌ Connection error: ${error.message}`, 'error');
            }
        };

        window.testDataSave = async function() {
            const container = document.getElementById('dataStatus');
            
            try {
                addStatus('dataStatus', 'Testing data save...', 'info');
                
                const testData = [{
                    name: 'Test Analyte ' + Date.now(),
                    current: [{ min: 1, max: 100, label: 'Test range' }],
                    target: [{ min: 0.1, max: 1000, label: 'Test target' }],
                    gapNotes: 'Test notes',
                    isCustom: true
                }];
                
                await supabaseStorage.saveData(testData);
                addStatus('dataStatus', '✅ Data save successful!', 'success');
                
            } catch (error) {
                addStatus('dataStatus', `❌ Save failed: ${error.message}`, 'error');
            }
        };

        window.testDataLoad = async function() {
            const container = document.getElementById('dataStatus');
            
            try {
                addStatus('dataStatus', 'Testing data load...', 'info');
                
                const data = await supabaseStorage.loadData();
                addStatus('dataStatus', `✅ Loaded ${data.length} records`, 'success');
                
                if (data.length > 0) {
                    addStatus('dataStatus', `Sample record: ${data[0].name}`, 'info');
                }
                
            } catch (error) {
                addStatus('dataStatus', `❌ Load failed: ${error.message}`, 'error');
            }
        };

        window.viewSessionData = async function() {
            const container = document.getElementById('dataStatus');
            
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_session', userSession);
                
                if (error) {
                    addStatus('dataStatus', `❌ Query failed: ${error.message}`, 'error');
                    return;
                }
                
                addStatus('dataStatus', `Found ${data.length} records for session ${userSession}`, 'info');
                
            } catch (error) {
                addStatus('dataStatus', `❌ Session query failed: ${error.message}`, 'error');
            }
        };

        window.inspectDatabase = async function() {
            const container = document.getElementById('rawDataDisplay');
            container.innerHTML = '';
            
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .limit(5);
                
                if (error) {
                    addStatus('rawDataDisplay', `❌ Database query failed: ${error.message}`, 'error');
                    return;
                }
                
                addStatus('rawDataDisplay', `Found ${data.length} total records in database`, 'info');
                
                if (data.length > 0) {
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(data, null, 2);
                    container.appendChild(pre);
                }
                
            } catch (error) {
                addStatus('rawDataDisplay', `❌ Inspection failed: ${error.message}`, 'error');
            }
        };

        window.addTestData = async function() {
            const analyteName = document.getElementById('testAnalyteName').value;
            const container = document.getElementById('testResults');
            
            if (!analyteName) {
                addStatus('testResults', '⚠️ Please enter a test analyte name', 'warning');
                return;
            }
            
            try {
                addStatus('testResults', `Adding test data: ${analyteName}...`, 'info');
                
                // Simulate adding data like the main app
                const testData = {
                    name: analyteName,
                    current: [{ min: Math.random() * 100, max: Math.random() * 1000, label: 'Test current' }],
                    target: [{ min: 0.1, max: 10000, label: 'Test target' }],
                    gapNotes: 'Test gap notes',
                    isCustom: true
                };
                
                // Get existing data
                const existingData = await supabaseStorage.loadData();
                existingData.push(testData);
                
                // Save updated data
                await supabaseStorage.saveData(existingData);
                
                addStatus('testResults', `✅ Successfully added: ${analyteName}`, 'success');
                addStatus('testResults', 'Try refreshing the main app to see if it persists!', 'info');
                
                // Clear input
                document.getElementById('testAnalyteName').value = '';
                
            } catch (error) {
                addStatus('testResults', `❌ Failed to add test data: ${error.message}`, 'error');
            }
        };

        // Auto-run connection test on load
        setTimeout(testConnection, 1000);
    </script>
</body>
</html>
