<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Quick Access</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="glass-effect rounded-2xl p-8 w-full max-w-md mx-4">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">Alpha Gas Solution</h1>
            <p class="text-gray-200">Quick Access to Analysis Tools</p>
        </div>

        <!-- Quick Access Options - Simple Redirects -->
        <div class="space-y-4 mb-6">
            <a href="./guest-mode-simple.html" class="block w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors text-center no-underline">
                🚀 Continue as Guest (Instant Access)
            </a>
            
            <div class="text-center text-gray-300 text-sm">
                Guest mode: Full functionality, data saves locally
            </div>
            
            <hr class="border-gray-400 my-4">
            
            <button onclick="showCreateAccount()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                📧 Create New Account
            </button>
            
            <button onclick="showSignIn()" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                🔑 Sign In to Existing Account
            </button>
        </div>

        <!-- Account Creation Form -->
        <div id="accountForm" class="hidden space-y-4">
            <div>
                <label class="block text-white text-sm font-bold mb-2">Email:</label>
                <input id="emailInput" type="email" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="<EMAIL>">
            </div>
            <div>
                <label class="block text-white text-sm font-bold mb-2">Password:</label>
                <input id="passwordInput" type="password" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="Enter password">
            </div>
            <div>
                <button onclick="createAccount()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    Create Account
                </button>
            </div>
            <div>
                <button onclick="showMainOptions()" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    ← Back to Options
                </button>
            </div>
        </div>

        <!-- Sign In Form -->
        <div id="signinForm" class="hidden space-y-4">
            <div>
                <label class="block text-white text-sm font-bold mb-2">Email:</label>
                <input id="signinEmailInput" type="email" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="<EMAIL>">
            </div>
            <div>
                <label class="block text-white text-sm font-bold mb-2">Password:</label>
                <input id="signinPasswordInput" type="password" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="Enter password">
            </div>
            <div>
                <button onclick="signIn()" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    Sign In
                </button>
            </div>
            <div>
                <button onclick="showMainOptions()" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    ← Back to Options
                </button>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessage" class="hidden mt-4 p-3 rounded-lg text-center"></div>
    </div>

    <script>
        // Initialize Supabase
        const SUPABASE_URL = 'https://xllrffehgebiaisgapdk.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Simple JavaScript without modules
        function showMainOptions() {
            document.getElementById('accountForm').classList.add('hidden');
            document.getElementById('signinForm').classList.add('hidden');
            document.getElementById('statusMessage').classList.add('hidden');
        }

        function showCreateAccount() {
            document.getElementById('accountForm').classList.remove('hidden');
            document.getElementById('signinForm').classList.add('hidden');
            document.getElementById('statusMessage').classList.add('hidden');
        }

        function showSignIn() {
            document.getElementById('accountForm').classList.add('hidden');
            document.getElementById('signinForm').classList.remove('hidden');
            document.getElementById('statusMessage').classList.add('hidden');
        }

        function showStatus(message, type = 'info') {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `mt-4 p-3 rounded-lg text-center ${
                type === 'error' ? 'bg-red-600 text-white' :
                type === 'success' ? 'bg-green-600 text-white' :
                type === 'warning' ? 'bg-yellow-600 text-white' :
                'bg-blue-600 text-white'
            }`;
            statusMessage.classList.remove('hidden');
        }

        async function createAccount() {
            const email = document.getElementById('emailInput').value.trim();
            const password = document.getElementById('passwordInput').value.trim();

            if (!email || !password) {
                showStatus('Please enter both email and password', 'error');
                return;
            }

            if (password.length < 6) {
                showStatus('Password must be at least 6 characters', 'error');
                return;
            }

            showStatus('Creating account...', 'info');

            try {
                // Sign up with Supabase
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password
                });

                if (error) {
                    throw error;
                }

                if (data.user) {
                    if (!data.user.email_confirmed_at) {
                        showStatus('Account created! Check your email for verification link, then sign in.', 'warning');
                        // Switch to sign in form after account creation
                        setTimeout(() => {
                            document.getElementById('signinEmailInput').value = email;
                            showSignIn();
                        }, 3000);
                    } else {
                        showStatus('Account created successfully! Your account is pending admin approval. You can use guest mode in the meantime.', 'warning');
                        // Add role-based information
                        setTimeout(() => {
                            const statusDiv = document.getElementById('statusMessage');
                            statusDiv.innerHTML += '<br><br><small>📋 <strong>Next Steps:</strong><br>• Your account needs admin approval<br>• You\'ll receive an email when approved<br>• Use Guest Mode for immediate access</small>';
                        }, 1000);
                        setTimeout(() => {
                            window.location.href = './guest-mode-simple.html';
                        }, 5000);
                    }
                } else {
                    throw new Error('Account creation failed');
                }
            } catch (error) {
                console.error('Account creation error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }

        async function signIn() {
            const email = document.getElementById('signinEmailInput').value.trim();
            const password = document.getElementById('signinPasswordInput').value.trim();

            if (!email || !password) {
                showStatus('Please enter both email and password', 'error');
                return;
            }

            showStatus('Signing in...', 'info');

            try {
                // Sign in with Supabase
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });

                if (error) {
                    throw error;
                }

                if (data.user) {
                    showStatus('Signed in successfully! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = './app.html';
                    }, 1000);
                } else {
                    throw new Error('Sign in failed');
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }

        // Check if user is already signed in on page load
        async function checkExistingAuth() {
            try {
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (user && !error) {
                    showStatus('Already signed in! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = './app.html';
                    }, 1000);
                }
            } catch (error) {
                console.log('No existing session, showing login options');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            checkExistingAuth();
        });

        // Show debug info
        console.log('Direct login page loaded successfully with real Supabase authentication');
        console.log('All functions available:', {
            showMainOptions: typeof showMainOptions,
            showCreateAccount: typeof showCreateAccount,
            showSignIn: typeof showSignIn,
            createAccount: typeof createAccount,
            signIn: typeof signIn,
            checkExistingAuth: typeof checkExistingAuth
        });
    </script>
</body>
</html>
