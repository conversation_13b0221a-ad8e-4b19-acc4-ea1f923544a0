<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Quick Access</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="glass-effect rounded-2xl p-8 w-full max-w-md mx-4">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">Alpha Gas Solution</h1>
            <p class="text-gray-200">Quick Access to Analysis Tools</p>
        </div>

        <!-- Quick Access Options -->
        <div class="space-y-4 mb-6">
            <button id="guestAccessBtn" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                🚀 Continue as Guest (Instant Access)
            </button>
            
            <div class="text-center text-gray-300 text-sm">
                Guest mode: Full functionality, data saves locally
            </div>
            
            <hr class="border-gray-400 my-4">
            
            <button id="createAccountBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                📧 Create New Account
            </button>
            
            <button id="signInBtn" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                🔑 Sign In to Existing Account
            </button>
        </div>

        <!-- Account Creation Form -->
        <div id="accountForm" class="hidden space-y-4">
            <div>
                <label class="block text-white text-sm font-bold mb-2">Email:</label>
                <input id="emailInput" type="email" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="<EMAIL>">
            </div>
            <div>
                <label class="block text-white text-sm font-bold mb-2">Password:</label>
                <input id="passwordInput" type="password" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="Choose a strong password">
            </div>
            <div>
                <button id="submitAccountBtn" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                    Create Account & Sign In
                </button>
            </div>
            <div>
                <button id="backBtn" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    ← Back to Options
                </button>
            </div>
        </div>

        <!-- Sign In Form -->
        <div id="signinForm" class="hidden space-y-4">
            <div>
                <label class="block text-white text-sm font-bold mb-2">Email:</label>
                <input id="signinEmailInput" type="email" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="<EMAIL>">
            </div>
            <div>
                <label class="block text-white text-sm font-bold mb-2">Password:</label>
                <input id="signinPasswordInput" type="password" class="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded text-white placeholder-gray-300" placeholder="Your password">
            </div>
            <div>
                <button id="submitSigninBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                    Sign In
                </button>
            </div>
            <div>
                <button id="backSigninBtn" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    ← Back to Options
                </button>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessage" class="hidden mt-4 p-3 rounded-lg text-center"></div>
    </div>

    <script type="module">
        import { authManager } from './auth.js';

        // DOM elements
        const guestAccessBtn = document.getElementById('guestAccessBtn');
        const createAccountBtn = document.getElementById('createAccountBtn');
        const signInBtn = document.getElementById('signInBtn');
        const accountForm = document.getElementById('accountForm');
        const signinForm = document.getElementById('signinForm');
        const emailInput = document.getElementById('emailInput');
        const passwordInput = document.getElementById('passwordInput');
        const signinEmailInput = document.getElementById('signinEmailInput');
        const signinPasswordInput = document.getElementById('signinPasswordInput');
        const submitAccountBtn = document.getElementById('submitAccountBtn');
        const submitSigninBtn = document.getElementById('submitSigninBtn');
        const backBtn = document.getElementById('backBtn');
        const backSigninBtn = document.getElementById('backSigninBtn');
        const statusMessage = document.getElementById('statusMessage');

        // Show status message
        function showStatus(message, type = 'info') {
            statusMessage.textContent = message;
            statusMessage.className = `mt-4 p-3 rounded-lg text-center ${
                type === 'error' ? 'bg-red-600 text-white' :
                type === 'success' ? 'bg-green-600 text-white' :
                type === 'warning' ? 'bg-yellow-600 text-white' :
                'bg-blue-600 text-white'
            }`;
            statusMessage.classList.remove('hidden');
        }

        function hideStatus() {
            statusMessage.classList.add('hidden');
        }

        function showMainOptions() {
            guestAccessBtn.parentElement.classList.remove('hidden');
            accountForm.classList.add('hidden');
            signinForm.classList.add('hidden');
            hideStatus();
        }

        // Guest access - go directly to app
        guestAccessBtn.addEventListener('click', () => {
            showStatus('Accessing app as guest...', 'info');
            setTimeout(() => {
                window.location.href = './guest-mode.html';
            }, 500);
        });

        // Show account creation form
        createAccountBtn.addEventListener('click', () => {
            guestAccessBtn.parentElement.classList.add('hidden');
            accountForm.classList.remove('hidden');
            hideStatus();
        });

        // Show sign in form
        signInBtn.addEventListener('click', () => {
            guestAccessBtn.parentElement.classList.add('hidden');
            signinForm.classList.remove('hidden');
            hideStatus();
        });

        // Back buttons
        backBtn.addEventListener('click', showMainOptions);
        backSigninBtn.addEventListener('click', showMainOptions);

        // Create account
        submitAccountBtn.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            const password = passwordInput.value.trim();

            if (!email || !password) {
                showStatus('Please enter both email and password', 'error');
                return;
            }

            if (password.length < 6) {
                showStatus('Password must be at least 6 characters', 'error');
                return;
            }

            showStatus('Creating account...', 'info');
            submitAccountBtn.disabled = true;

            try {
                // Initialize auth manager
                await authManager.init();
                
                // Create account
                const { user, error } = await authManager.signUp(email, password);
                
                if (error) {
                    throw new Error(error);
                }

                if (user) {
                    if (!user.email_confirmed_at) {
                        showStatus('Account created! Check your email for verification link, then sign in.', 'warning');
                        // Show sign in form after account creation
                        setTimeout(() => {
                            signinEmailInput.value = email;
                            accountForm.classList.add('hidden');
                            signinForm.classList.remove('hidden');
                            hideStatus();
                        }, 3000);
                    } else {
                        showStatus('Account created and signed in! Redirecting...', 'success');
                        setTimeout(() => {
                            window.location.href = './app.html';
                        }, 1000);
                    }
                } else {
                    throw new Error('Account creation failed');
                }
            } catch (error) {
                console.error('Account creation error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            } finally {
                submitAccountBtn.disabled = false;
            }
        });

        // Sign in
        submitSigninBtn.addEventListener('click', async () => {
            const email = signinEmailInput.value.trim();
            const password = signinPasswordInput.value.trim();

            if (!email || !password) {
                showStatus('Please enter both email and password', 'error');
                return;
            }

            showStatus('Signing in...', 'info');
            submitSigninBtn.disabled = true;

            try {
                // Initialize auth manager
                await authManager.init();
                
                // Sign in
                const { user, error } = await authManager.signIn(email, password);
                
                if (error) {
                    throw new Error(error);
                }

                if (user) {
                    showStatus('Signed in successfully! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = './app.html';
                    }, 1000);
                } else {
                    throw new Error('Sign in failed');
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            } finally {
                submitSigninBtn.disabled = false;
            }
        });

        // Check if user is already signed in
        async function checkExistingAuth() {
            try {
                const user = await authManager.init();
                if (user) {
                    showStatus('Already signed in! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = './app.html';
                    }, 1000);
                }
            } catch (error) {
                console.log('No existing session, showing login options');
            }
        }

        // Initialize
        checkExistingAuth();
    </script>
</body>
</html>
