<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RBAC Error Handling & Edge Cases Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">🔍 RBAC Error Handling & Edge Cases</h1>
        <p class="text-gray-600 mb-6">Test error handling, edge cases, and recovery scenarios</p>

        <!-- Test Categories -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <!-- Network/Connection Tests -->
            <div class="border rounded-lg p-4">
                <h3 class="font-semibold mb-3">🌐 Network & Connection</h3>
                <div class="space-y-2">
                    <button id="testOfflineMode" class="w-full bg-orange-500 text-white px-3 py-2 rounded text-sm">
                        Test Offline Mode
                    </button>
                    <button id="testInvalidCredentials" class="w-full bg-red-500 text-white px-3 py-2 rounded text-sm">
                        Test Invalid Database Access
                    </button>
                    <button id="testConnectionRecovery" class="w-full bg-green-500 text-white px-3 py-2 rounded text-sm">
                        Test Connection Recovery
                    </button>
                </div>
            </div>

            <!-- Data Integrity Tests -->
            <div class="border rounded-lg p-4">
                <h3 class="font-semibold mb-3">🔒 Data Integrity</h3>
                <div class="space-y-2">
                    <button id="testCorruptedData" class="w-full bg-purple-500 text-white px-3 py-2 rounded text-sm">
                        Test Corrupted Data Handling
                    </button>
                    <button id="testMissingProperties" class="w-full bg-indigo-500 text-white px-3 py-2 rounded text-sm">
                        Test Missing Properties
                    </button>
                    <button id="testInvalidRoles" class="w-full bg-pink-500 text-white px-3 py-2 rounded text-sm">
                        Test Invalid Roles
                    </button>
                </div>
            </div>

            <!-- Permission Edge Cases -->
            <div class="border rounded-lg p-4">
                <h3 class="font-semibold mb-3">🚫 Permission Edge Cases</h3>
                <div class="space-y-2">
                    <button id="testUnauthorizedAccess" class="w-full bg-red-600 text-white px-3 py-2 rounded text-sm">
                        Test Unauthorized Access
                    </button>
                    <button id="testRoleChangeDuringSession" class="w-full bg-yellow-600 text-white px-3 py-2 rounded text-sm">
                        Test Role Change During Session
                    </button>
                    <button id="testDeletedUserAccess" class="w-full bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        Test Deleted User Access
                    </button>
                </div>
            </div>

            <!-- Recovery Scenarios -->
            <div class="border rounded-lg p-4">
                <h3 class="font-semibold mb-3">🔄 Recovery Scenarios</h3>
                <div class="space-y-2">
                    <button id="testDataMigration" class="w-full bg-blue-600 text-white px-3 py-2 rounded text-sm">
                        Test Data Migration
                    </button>
                    <button id="testSystemRecovery" class="w-full bg-teal-600 text-white px-3 py-2 rounded text-sm">
                        Test System Recovery
                    </button>
                    <button id="testBackupFallback" class="w-full bg-cyan-600 text-white px-3 py-2 rounded text-sm">
                        Test Backup Fallback
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="border rounded-lg p-4">
            <div class="flex justify-between items-center mb-3">
                <h3 class="font-semibold">📋 Test Results</h3>
                <div class="text-sm">
                    <span id="testCounter" class="bg-blue-100 text-blue-800 px-2 py-1 rounded">0 tests run</span>
                    <span id="errorCounter" class="bg-red-100 text-red-800 px-2 py-1 rounded ml-2">0 errors</span>
                </div>
            </div>
            <div id="testResults" class="space-y-1 max-h-96 overflow-y-auto bg-gray-50 p-3 rounded text-sm font-mono"></div>
            <div class="mt-3 flex gap-2">
                <button id="clearResults" class="bg-gray-500 text-white px-3 py-1 rounded text-sm">
                    Clear Results
                </button>
                <button id="runAllEdgeCases" class="bg-purple-600 text-white px-3 py-1 rounded text-sm">
                    Run All Edge Cases
                </button>
                <button id="exportTestReport" class="bg-green-600 text-white px-3 py-1 rounded text-sm">
                    Export Report
                </button>
            </div>
        </div>

        <!-- Status Panel -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold mb-2">System Status</h3>
            <div id="systemStatus" class="text-sm text-gray-600">
                <div id="dbStatus">Database: <span class="text-green-600">Connected</span></div>
                <div id="authStatus">Authentication: <span class="text-blue-600">Ready</span></div>
                <div id="roleStatus">Role Manager: <span class="text-purple-600">Initialized</span></div>
            </div>
        </div>
    </div>

    <script type="module">
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        let testCount = 0;
        let errorCount = 0;

        function log(message, type = 'info') {
            const container = document.getElementById('testResults');
            const div = document.createElement('div');
            
            const typeColors = {
                pass: 'text-green-700 bg-green-50 border-l-4 border-green-500 pl-2',
                fail: 'text-red-700 bg-red-50 border-l-4 border-red-500 pl-2',
                warn: 'text-yellow-700 bg-yellow-50 border-l-4 border-yellow-500 pl-2',
                info: 'text-blue-700 bg-blue-50 border-l-4 border-blue-500 pl-2',
                error: 'text-red-800 bg-red-100 border-l-4 border-red-600 pl-2'
            };
            
            div.className = `p-2 rounded mb-1 ${typeColors[type] || typeColors.info}`;
            div.innerHTML = `<span class="text-xs text-gray-500">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;

            testCount++;
            if (type === 'fail' || type === 'error') {
                errorCount++;
            }
            
            updateCounters();
        }

        function updateCounters() {
            document.getElementById('testCounter').textContent = `${testCount} tests run`;
            document.getElementById('errorCounter').textContent = `${errorCount} errors`;
        }

        async function updateSystemStatus() {
            try {
                // Test database connection
                const { error: dbError } = await supabase.from('user_profiles').select('count').limit(1);
                document.getElementById('dbStatus').innerHTML = dbError ? 
                    'Database: <span class="text-red-600">Error</span>' : 
                    'Database: <span class="text-green-600">Connected</span>';

                // Test authentication
                const { data: { session } } = await supabase.auth.getSession();
                document.getElementById('authStatus').innerHTML = session ? 
                    'Authentication: <span class="text-green-600">Authenticated</span>' : 
                    'Authentication: <span class="text-blue-600">Guest Mode</span>';

                // Test role manager
                try {
                    const { roleManager } = await import('./role-manager.js');
                    await roleManager.init();
                    document.getElementById('roleStatus').innerHTML = 
                        'Role Manager: <span class="text-green-600">Active</span>';
                } catch (error) {
                    document.getElementById('roleStatus').innerHTML = 
                        'Role Manager: <span class="text-red-600">Error</span>';
                }
            } catch (error) {
                console.error('Status update error:', error);
            }
        }

        // Test functions
        async function testOfflineMode() {
            log('🌐 Testing offline mode handling...', 'info');
            
            try {
                // Simulate offline by using invalid URL
                const offlineSupabase = window.supabase.createClient('https://invalid-url.supabase.co', 'invalid-key');
                
                const { data, error } = await offlineSupabase.from('user_profiles').select('*').limit(1);
                
                if (error) {
                    log(`✅ Offline mode handled correctly: ${error.message}`, 'pass');
                } else {
                    log('❌ Offline mode not detected properly', 'fail');
                }
                
                // Test module behavior in offline mode
                try {
                    const { authManager } = await import('./auth.js');
                    // This should handle gracefully
                    const user = await authManager.init();
                    log('✅ Auth manager handles offline mode gracefully', 'pass');
                } catch (authError) {
                    log(`⚠️ Auth manager offline handling: ${authError.message}`, 'warn');
                }
                
            } catch (error) {
                log(`❌ Offline test failed: ${error.message}`, 'error');
            }
        }

        async function testInvalidCredentials() {
            log('🔒 Testing invalid database access...', 'info');
            
            try {
                // Test with invalid credentials
                const invalidSupabase = window.supabase.createClient(supabaseUrl, 'invalid_key_12345');
                
                const { data, error } = await invalidSupabase.from('user_profiles').select('*').limit(1);
                
                if (error && error.message.includes('Invalid API key')) {
                    log('✅ Invalid credentials properly rejected', 'pass');
                } else {
                    log('⚠️ Invalid credentials not properly handled', 'warn');
                }
                
            } catch (error) {
                log(`✅ Invalid credentials test passed: ${error.message}`, 'pass');
            }
        }

        async function testConnectionRecovery() {
            log('🔄 Testing connection recovery...', 'info');
            
            try {
                // Test normal connection first
                const { data: beforeData, error: beforeError } = await supabase.from('user_profiles').select('count').limit(1);
                
                if (!beforeError) {
                    log('✅ Initial connection successful', 'pass');
                    
                    // Simulate recovery by testing connection again
                    setTimeout(async () => {
                        const { data: afterData, error: afterError } = await supabase.from('user_profiles').select('count').limit(1);
                        
                        if (!afterError) {
                            log('✅ Connection recovery successful', 'pass');
                        } else {
                            log(`❌ Connection recovery failed: ${afterError.message}`, 'fail');
                        }
                    }, 1000);
                } else {
                    log(`⚠️ Initial connection failed: ${beforeError.message}`, 'warn');
                }
                
            } catch (error) {
                log(`❌ Connection recovery test failed: ${error.message}`, 'error');
            }
        }

        async function testCorruptedData() {
            log('🔒 Testing corrupted data handling...', 'info');
            
            try {
                // Test with malformed analyte data
                const corruptedAnalyte = {
                    name: null, // Invalid name
                    current: "invalid_json_string", // Invalid structure
                    target: undefined, // Missing data
                    isCustom: "not_boolean", // Wrong type
                    is_shared: null,
                    data_type: 123 // Wrong type
                };
                
                // Test filtering with corrupted data
                const testAnalytes = [corruptedAnalyte];
                try {
                    const filtered = testAnalytes.filter(analyte => !analyte.is_shared && analyte.isCustom);
                    log('✅ Corrupted data filtered without crashing', 'pass');
                } catch (filterError) {
                    log(`⚠️ Filter error with corrupted data: ${filterError.message}`, 'warn');
                }
                
                // Test role manager with corrupted profile data
                try {
                    const { roleManager } = await import('./role-manager.js');
                    
                    // Simulate corrupted user profile
                    const originalGetRole = roleManager.getRole;
                    roleManager.getRole = () => { throw new Error('Corrupted role data'); };
                    
                    try {
                        const restrictions = roleManager.getUIRestrictions();
                        log('⚠️ Role manager did not handle corrupted data properly', 'warn');
                    } catch (roleError) {
                        log('✅ Role manager properly handles corrupted data', 'pass');
                    }
                    
                    // Restore original function
                    roleManager.getRole = originalGetRole;
                    
                } catch (moduleError) {
                    log(`⚠️ Role manager module error: ${moduleError.message}`, 'warn');
                }
                
            } catch (error) {
                log(`❌ Corrupted data test failed: ${error.message}`, 'error');
            }
        }

        async function testMissingProperties() {
            log('🔍 Testing missing properties handling...', 'info');
            
            try {
                // Test analyte without required properties
                const incompleteAnalyte = {
                    name: "Incomplete Analyte"
                    // Missing: current, target, isCustom, is_shared, data_type
                };
                
                // Test form handler logic
                const testAnalytes = [incompleteAnalyte];
                const filtered = testAnalytes.filter(analyte => !analyte.is_shared && analyte.isCustom);
                
                if (filtered.length === 0) {
                    log('✅ Incomplete analytes properly filtered out', 'pass');
                } else {
                    log('❌ Incomplete analytes not filtered properly', 'fail');
                }
                
                // Test role manager with missing profile
                try {
                    const { roleManager } = await import('./role-manager.js');
                    
                    // Test with no profile data
                    roleManager.currentRole = undefined;
                    roleManager.currentStatus = undefined;
                    
                    const role = roleManager.getRole();
                    const status = roleManager.getStatus();
                    
                    if (role && status) {
                        log('✅ Role manager handles missing profile gracefully', 'pass');
                    } else {
                        log('⚠️ Role manager may not handle missing profile properly', 'warn');
                    }
                    
                } catch (roleError) {
                    log(`⚠️ Role manager error with missing data: ${roleError.message}`, 'warn');
                }
                
            } catch (error) {
                log(`❌ Missing properties test failed: ${error.message}`, 'error');
            }
        }

        async function testInvalidRoles() {
            log('👤 Testing invalid roles handling...', 'info');
            
            try {
                const { roleManager } = await import('./role-manager.js');
                
                // Test with invalid role values
                const invalidRoles = ['super_admin', 'moderator', '', null, undefined, 123, {}];
                
                for (const invalidRole of invalidRoles) {
                    try {
                        roleManager.currentRole = invalidRole;
                        const hasAccess = roleManager.hasAccess('admin_dashboard');
                        
                        if (!hasAccess) {
                            log(`✅ Invalid role "${invalidRole}" properly denied access`, 'pass');
                        } else {
                            log(`⚠️ Invalid role "${invalidRole}" incorrectly granted access`, 'warn');
                        }
                    } catch (roleError) {
                        log(`✅ Invalid role "${invalidRole}" properly handled with error`, 'pass');
                    }
                }
                
            } catch (error) {
                log(`❌ Invalid roles test failed: ${error.message}`, 'error');
            }
        }

        async function testUnauthorizedAccess() {
            log('🚫 Testing unauthorized access attempts...', 'info');
            
            try {
                // Test accessing admin functions without proper role
                const { data, error } = await supabase
                    .from('admin_audit_log')
                    .insert({
                        action: 'unauthorized_test',
                        admin_email: '<EMAIL>',
                        target_user_email: '<EMAIL>',
                        details: 'Unauthorized access test'
                    });

                if (error) {
                    log(`✅ Unauthorized admin access properly blocked: ${error.message}`, 'pass');
                } else {
                    log('❌ Unauthorized admin access was not blocked', 'fail');
                    
                    // Clean up if it somehow succeeded
                    if (data && data[0]) {
                        await supabase.from('admin_audit_log').delete().eq('id', data[0].id);
                    }
                }
                
                // Test accessing user data from different user
                const { data: sessionData } = await supabase.auth.getSession();
                if (sessionData?.session?.user) {
                    const { data: userData, error: userError } = await supabase
                        .from('gas_analytes')
                        .select('*')
                        .neq('user_id', sessionData.session.user.id)
                        .limit(1);

                    if (userError || (userData && userData.length === 0)) {
                        log('✅ Cross-user data access properly restricted', 'pass');
                    } else {
                        log('⚠️ Cross-user data access may not be properly restricted', 'warn');
                    }
                }
                
            } catch (error) {
                log(`❌ Unauthorized access test failed: ${error.message}`, 'error');
            }
        }

        async function testRoleChangeDuringSession() {
            log('🔄 Testing role change during session...', 'info');
            
            try {
                const { roleManager } = await import('./role-manager.js');
                
                // Get initial state
                await roleManager.init();
                const initialRole = roleManager.getRole();
                const initialAccess = roleManager.hasAccess('admin_dashboard');
                
                log(`Initial role: ${initialRole}, Admin access: ${initialAccess}`, 'info');
                
                // Simulate role change
                roleManager.currentRole = initialRole === 'admin' ? 'user' : 'admin';
                roleManager.currentStatus = 'approved';
                
                const newAccess = roleManager.hasAccess('admin_dashboard');
                log(`After role change: ${roleManager.getRole()}, Admin access: ${newAccess}`, 'info');
                
                if (initialAccess !== newAccess) {
                    log('✅ Role change properly affects access controls', 'pass');
                } else {
                    log('⚠️ Role change may not affect access controls immediately', 'warn');
                }
                
                // Test refresh mechanism
                await roleManager.refresh();
                const refreshedRole = roleManager.getRole();
                log(`After refresh: ${refreshedRole}`, 'info');
                
                log('✅ Role change during session test completed', 'pass');
                
            } catch (error) {
                log(`❌ Role change test failed: ${error.message}`, 'error');
            }
        }

        async function testDeletedUserAccess() {
            log('👻 Testing deleted user access scenarios...', 'info');
            
            try {
                // Test with non-existent user ID
                const fakeUserId = '00000000-0000-0000-0000-000000000000';
                
                const { data, error } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('user_id', fakeUserId)
                    .single();

                if (error && error.message.includes('No rows returned')) {
                    log('✅ Deleted/non-existent user properly handled', 'pass');
                } else if (data) {
                    log('⚠️ Non-existent user returned data unexpectedly', 'warn');
                } else {
                    log(`✅ Deleted user access handled: ${error.message}`, 'pass');
                }
                
                // Test auth manager behavior with deleted user session
                try {
                    const { authManager } = await import('./auth.js');
                    
                    // This should gracefully handle missing user profiles
                    const profile = await authManager.getUserProfile();
                    log('✅ Auth manager handles missing profiles gracefully', 'pass');
                    
                } catch (authError) {
                    log(`⚠️ Auth manager error with deleted user: ${authError.message}`, 'warn');
                }
                
            } catch (error) {
                log(`❌ Deleted user test failed: ${error.message}`, 'error');
            }
        }

        async function testDataMigration() {
            log('📦 Testing data migration scenarios...', 'info');
            
            try {
                // Test migration of old data format to new format
                const oldFormatAnalyte = {
                    name: "Legacy Analyte",
                    current: [{ min: 10, max: 100 }],
                    target: [{ min: 1, max: 1000 }]
                    // Missing: isCustom, is_shared, data_type
                };
                
                // Simulate migration by adding missing properties
                const migratedAnalyte = {
                    ...oldFormatAnalyte,
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };
                
                // Test that migrated data works with current system
                const testAnalytes = [migratedAnalyte];
                const filtered = testAnalytes.filter(analyte => !analyte.is_shared && analyte.isCustom);
                
                if (filtered.length === 1) {
                    log('✅ Data migration maintains functionality', 'pass');
                } else {
                    log('❌ Data migration breaks functionality', 'fail');
                }
                
                log('✅ Data migration test completed', 'pass');
                
            } catch (error) {
                log(`❌ Data migration test failed: ${error.message}`, 'error');
            }
        }

        async function testSystemRecovery() {
            log('🔄 Testing system recovery scenarios...', 'info');
            
            try {
                // Test module recovery after error
                try {
                    const { roleManager } = await import('./role-manager.js');
                    
                    // Force an error and test recovery
                    const originalInit = roleManager.init;
                    roleManager.init = async () => { throw new Error('Simulated init error'); };
                    
                    try {
                        await roleManager.init();
                        log('❌ Error simulation did not work', 'fail');
                    } catch (simError) {
                        log('✅ Error simulation successful', 'pass');
                        
                        // Restore and test recovery
                        roleManager.init = originalInit;
                        await roleManager.init();
                        log('✅ System recovered from error', 'pass');
                    }
                    
                } catch (moduleError) {
                    log(`⚠️ Module recovery test: ${moduleError.message}`, 'warn');
                }
                
                // Test fallback to guest mode
                try {
                    const { roleManager } = await import('./role-manager.js');
                    roleManager.currentRole = 'guest';
                    roleManager.currentStatus = 'guest';
                    
                    const restrictions = roleManager.getUIRestrictions();
                    if (restrictions.showGuestWarning) {
                        log('✅ Fallback to guest mode works correctly', 'pass');
                    } else {
                        log('⚠️ Guest mode fallback may not be working', 'warn');
                    }
                    
                } catch (fallbackError) {
                    log(`❌ Guest mode fallback failed: ${fallbackError.message}`, 'fail');
                }
                
            } catch (error) {
                log(`❌ System recovery test failed: ${error.message}`, 'error');
            }
        }

        async function testBackupFallback() {
            log('💾 Testing backup fallback mechanisms...', 'info');
            
            try {
                // Test localStorage fallback when database is unavailable
                const testData = { role: 'user', status: 'approved', timestamp: Date.now() };
                
                // Store test data in localStorage
                localStorage.setItem('rbac_fallback_test', JSON.stringify(testData));
                
                // Retrieve and validate
                const retrieved = JSON.parse(localStorage.getItem('rbac_fallback_test'));
                
                if (retrieved && retrieved.role === testData.role) {
                    log('✅ localStorage fallback works correctly', 'pass');
                } else {
                    log('❌ localStorage fallback failed', 'fail');
                }
                
                // Clean up
                localStorage.removeItem('rbac_fallback_test');
                
                // Test session storage fallback
                sessionStorage.setItem('rbac_session_test', JSON.stringify(testData));
                const sessionRetrieved = JSON.parse(sessionStorage.getItem('rbac_session_test'));
                
                if (sessionRetrieved && sessionRetrieved.status === testData.status) {
                    log('✅ sessionStorage fallback works correctly', 'pass');
                } else {
                    log('❌ sessionStorage fallback failed', 'fail');
                }
                
                // Clean up
                sessionStorage.removeItem('rbac_session_test');
                
                log('✅ Backup fallback test completed', 'pass');
                
            } catch (error) {
                log(`❌ Backup fallback test failed: ${error.message}`, 'error');
            }
        }

        async function runAllEdgeCases() {
            log('🚀 Running all edge case tests...', 'info');
            
            const tests = [
                testOfflineMode,
                testInvalidCredentials,
                testConnectionRecovery,
                testCorruptedData,
                testMissingProperties,
                testInvalidRoles,
                testUnauthorizedAccess,
                testRoleChangeDuringSession,
                testDeletedUserAccess,
                testDataMigration,
                testSystemRecovery,
                testBackupFallback
            ];
            
            for (const test of tests) {
                try {
                    await test();
                    await new Promise(resolve => setTimeout(resolve, 500)); // Brief pause between tests
                } catch (error) {
                    log(`❌ Test ${test.name} failed: ${error.message}`, 'error');
                }
            }
            
            log('✅ All edge case tests completed!', 'pass');
        }

        function exportTestReport() {
            const results = {
                timestamp: new Date().toISOString(),
                testCount,
                errorCount,
                successRate: `${((testCount - errorCount) / testCount * 100).toFixed(1)}%`,
                logs: Array.from(document.querySelectorAll('#testResults > div')).map(div => div.textContent)
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rbac-edge-case-report-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📄 Test report exported', 'info');
        }

        // Event listeners
        document.getElementById('testOfflineMode').addEventListener('click', testOfflineMode);
        document.getElementById('testInvalidCredentials').addEventListener('click', testInvalidCredentials);
        document.getElementById('testConnectionRecovery').addEventListener('click', testConnectionRecovery);
        document.getElementById('testCorruptedData').addEventListener('click', testCorruptedData);
        document.getElementById('testMissingProperties').addEventListener('click', testMissingProperties);
        document.getElementById('testInvalidRoles').addEventListener('click', testInvalidRoles);
        document.getElementById('testUnauthorizedAccess').addEventListener('click', testUnauthorizedAccess);
        document.getElementById('testRoleChangeDuringSession').addEventListener('click', testRoleChangeDuringSession);
        document.getElementById('testDeletedUserAccess').addEventListener('click', testDeletedUserAccess);
        document.getElementById('testDataMigration').addEventListener('click', testDataMigration);
        document.getElementById('testSystemRecovery').addEventListener('click', testSystemRecovery);
        document.getElementById('testBackupFallback').addEventListener('click', testBackupFallback);
        document.getElementById('runAllEdgeCases').addEventListener('click', runAllEdgeCases);
        document.getElementById('exportTestReport').addEventListener('click', exportTestReport);

        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('testResults').innerHTML = '';
            testCount = 0;
            errorCount = 0;
            updateCounters();
        });

        // Initialize
        updateSystemStatus();
        setInterval(updateSystemStatus, 30000); // Update status every 30 seconds
    </script>
</body>
</html>
