/* 
   Elevated SaaS UI Design System
   Alpha Gas Solution - Professional Interface
*/

/* ===== ROOT VARIABLES ===== */
:root {
    /* SaaS Color Palette */
    --saas-bg: #F7F8FC;             /* Light gray background */
    --saas-surface: #FFFFFF;        /* Pure white surfaces */
    --saas-text-primary: #1A202C;   /* Dark charcoal for primary text */
    --saas-text-secondary: #6B7280; /* Medium gray for secondary text */
    --saas-accent: #E54D2E;         /* Red accent color */
    
    /* Extended Color System */
    --saas-border: #E2E8F0;         /* Light border color */
    --saas-border-hover: #CBD5E0;   /* Darker border on hover */
    --saas-success: #059669;        /* Green for success states */
    --saas-warning: #D97706;        /* Orange for warnings */
    --saas-error: #DC2626;          /* Red for errors */
    --saas-info: #2563EB;           /* Blue for info */
    
    /* Background Variations */
    --saas-bg-subtle: #F8FAFC;      /* Slightly darker background */
    --saas-bg-muted: #F1F5F9;       /* More noticeable background */
    
    /* Text Color Variations */
    --saas-text-muted: #9CA3AF;     /* Very light gray text */
    --saas-text-disabled: #D1D5DB;  /* Disabled text color */
    
    /* Shadow System */
    --saas-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --saas-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --saas-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --saas-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --saas-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Consolas', 'Liberation Mono', monospace;
    
    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    
    /* Border Radius */
    --radius-sm: 0.375rem;  /* 6px */
    --radius: 0.5rem;       /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    background-color: var(--saas-bg);
    color: var(--saas-text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    min-height: 100vh;
}

/* ===== TYPOGRAPHY ===== */
.saas-heading-xl {
    font-size: 2.25rem;      /* 36px */
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: var(--saas-text-primary);
}

.saas-heading-lg {
    font-size: 1.875rem;     /* 30px */
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.025em;
    color: var(--saas-text-primary);
}

.saas-heading-md {
    font-size: 1.5rem;       /* 24px */
    font-weight: 600;
    line-height: 1.3;
    color: var(--saas-text-primary);
}

.saas-heading-sm {
    font-size: 1.25rem;      /* 20px */
    font-weight: 600;
    line-height: 1.4;
    color: var(--saas-text-primary);
}

.saas-text-xs { 
    font-size: 0.75rem; 
    line-height: 1.1rem;     /* 12px */
}

.saas-text-sm { 
    font-size: 0.875rem; 
    line-height: 1.25rem;    /* 14px */
}

.saas-text-base { 
    font-size: 1rem; 
    line-height: 1.5rem;      /* 16px */
}

.saas-text-lg { 
    font-size: 1.125rem; 
    line-height: 1.75rem;     /* 18px */
}

.saas-text-xl { 
    font-size: 1.25rem; 
    line-height: 1.75rem;     /* 20px */
}

.saas-text-2xl { 
    font-size: 1.5rem; 
    line-height: 2rem;        /* 24px */
}

.saas-text-3xl { 
    font-size: 1.875rem; 
    line-height: 2.25rem;     /* 30px */
}

/* Text Color Utilities */
.saas-text-primary { color: var(--saas-text-primary); }
.saas-text-secondary { color: var(--saas-text-secondary); }
.saas-text-muted { color: var(--saas-text-muted); }
.saas-text-accent { color: var(--saas-accent); }
.saas-text-success { color: var(--saas-success); }
.saas-text-warning { color: var(--saas-warning); }
.saas-text-error { color: var(--saas-error); }
.saas-text-info { color: var(--saas-info); }

/* Font Weight Utilities */
.saas-font-light { font-weight: 300; }
.saas-font-normal { font-weight: 400; }
.saas-font-medium { font-weight: 500; }
.saas-font-semibold { font-weight: 600; }
.saas-font-bold { font-weight: 700; }

/* ===== CARD SYSTEM ===== */
.saas-card {
    background-color: var(--saas-surface);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--saas-shadow);
    padding: var(--space-lg);
    transition: all 0.2s ease-in-out;
}

.saas-card:hover {
    border-color: var(--saas-border-hover);
    box-shadow: var(--saas-shadow-md);
}

.saas-card-compact {
    padding: var(--space-md);
}

.saas-card-spacious {
    padding: var(--space-xl);
}

.saas-card-header {
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--saas-border);
}

.saas-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--saas-text-primary);
    margin-bottom: var(--space-xs);
}

.saas-card-subtitle {
    font-size: 0.875rem;
    color: var(--saas-text-secondary);
}

.saas-card-content {
    margin-bottom: var(--space-lg);
}

.saas-card-footer {
    margin-top: var(--space-lg);
    padding-top: var(--space-md);
    border-top: 1px solid var(--saas-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* ===== NAVIGATION ===== */
.saas-nav {
    background-color: var(--saas-surface);
    border-bottom: 1px solid var(--saas-border);
    padding: var(--space-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--saas-shadow-sm);
}

.saas-nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.saas-nav-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--saas-text-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.saas-nav-brand .brand-icon {
    color: var(--saas-accent);
}

.saas-nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.saas-nav-link {
    color: var(--saas-text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius);
    transition: all 0.2s ease-in-out;
}

.saas-nav-link:hover {
    color: var(--saas-text-primary);
    background-color: var(--saas-bg-subtle);
}

.saas-nav-link.active {
    color: var(--saas-accent);
    background-color: rgba(229, 77, 46, 0.1);
}

/* ===== BUTTON SYSTEM ===== */
.saas-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border: 1px solid transparent;
    border-radius: var(--radius);
    font-family: var(--font-primary);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    white-space: nowrap;
    user-select: none;
}

.saas-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Button Variants */
.saas-btn-primary {
    background-color: var(--saas-accent);
    color: white;
    border-color: var(--saas-accent);
}

.saas-btn-primary:hover:not(:disabled) {
    background-color: #D73D1D;
    border-color: #D73D1D;
    box-shadow: var(--saas-shadow);
}

.saas-btn-secondary {
    background-color: var(--saas-surface);
    color: var(--saas-text-primary);
    border-color: var(--saas-border);
}

.saas-btn-secondary:hover:not(:disabled) {
    background-color: var(--saas-bg-subtle);
    border-color: var(--saas-border-hover);
}

.saas-btn-outline {
    background-color: transparent;
    color: var(--saas-accent);
    border-color: var(--saas-accent);
}

.saas-btn-outline:hover:not(:disabled) {
    background-color: var(--saas-accent);
    color: white;
}

.saas-btn-ghost {
    background-color: transparent;
    color: var(--saas-text-secondary);
    border-color: transparent;
}

.saas-btn-ghost:hover:not(:disabled) {
    background-color: var(--saas-bg-subtle);
    color: var(--saas-text-primary);
}

.saas-btn-success {
    background-color: var(--saas-success);
    color: white;
    border-color: var(--saas-success);
}

.saas-btn-success:hover:not(:disabled) {
    background-color: #047857;
    border-color: #047857;
}

.saas-btn-warning {
    background-color: var(--saas-warning);
    color: white;
    border-color: var(--saas-warning);
}

.saas-btn-warning:hover:not(:disabled) {
    background-color: #B45309;
    border-color: #B45309;
}

.saas-btn-error {
    background-color: var(--saas-error);
    color: white;
    border-color: var(--saas-error);
}

.saas-btn-error:hover:not(:disabled) {
    background-color: #B91C1C;
    border-color: #B91C1C;
}

/* Button Sizes */
.saas-btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.75rem;
}

.saas-btn-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: 1rem;
}

.saas-btn-xl {
    padding: var(--space-lg) var(--space-xl);
    font-size: 1.125rem;
}

/* ===== FORM COMPONENTS ===== */
.saas-form-group {
    margin-bottom: var(--space-lg);
}

.saas-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--saas-text-primary);
    margin-bottom: var(--space-sm);
}

.saas-input {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius);
    font-family: var(--font-primary);
    font-size: 0.875rem;
    color: var(--saas-text-primary);
    background-color: var(--saas-surface);
    transition: all 0.2s ease-in-out;
}

.saas-input:focus {
    outline: none;
    border-color: var(--saas-accent);
    box-shadow: 0 0 0 3px rgba(229, 77, 46, 0.1);
}

.saas-input::placeholder {
    color: var(--saas-text-muted);
}

.saas-textarea {
    resize: vertical;
    min-height: 120px;
}

.saas-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* ===== STATUS INDICATORS ===== */
.saas-status {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 500;
}

.saas-status-success {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--saas-success);
    border: 1px solid rgba(5, 150, 105, 0.2);
}

.saas-status-warning {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--saas-warning);
    border: 1px solid rgba(217, 119, 6, 0.2);
}

.saas-status-error {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--saas-error);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.saas-status-info {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--saas-info);
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.saas-status-neutral {
    background-color: var(--saas-bg-subtle);
    color: var(--saas-text-secondary);
    border: 1px solid var(--saas-border);
}

/* ===== LAYOUT UTILITIES ===== */
.saas-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.saas-grid {
    display: grid;
    gap: var(--space-lg);
}

.saas-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.saas-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.saas-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.saas-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.saas-flex {
    display: flex;
}

.saas-flex-col {
    flex-direction: column;
}

.saas-items-center {
    align-items: center;
}

.saas-justify-center {
    justify-content: center;
}

.saas-justify-between {
    justify-content: space-between;
}

.saas-gap-xs { gap: var(--space-xs); }
.saas-gap-sm { gap: var(--space-sm); }
.saas-gap-md { gap: var(--space-md); }
.saas-gap-lg { gap: var(--space-lg); }
.saas-gap-xl { gap: var(--space-xl); }

/* Spacing Utilities */
.saas-p-xs { padding: var(--space-xs); }
.saas-p-sm { padding: var(--space-sm); }
.saas-p-md { padding: var(--space-md); }
.saas-p-lg { padding: var(--space-lg); }
.saas-p-xl { padding: var(--space-xl); }

.saas-m-xs { margin: var(--space-xs); }
.saas-m-sm { margin: var(--space-sm); }
.saas-m-md { margin: var(--space-md); }
.saas-m-lg { margin: var(--space-lg); }
.saas-m-xl { margin: var(--space-xl); }

.saas-mb-xs { margin-bottom: var(--space-xs); }
.saas-mb-sm { margin-bottom: var(--space-sm); }
.saas-mb-md { margin-bottom: var(--space-md); }
.saas-mb-lg { margin-bottom: var(--space-lg); }
.saas-mb-xl { margin-bottom: var(--space-xl); }

.saas-mt-xs { margin-top: var(--space-xs); }
.saas-mt-sm { margin-top: var(--space-sm); }
.saas-mt-md { margin-top: var(--space-md); }
.saas-mt-lg { margin-top: var(--space-lg); }
.saas-mt-xl { margin-top: var(--space-xl); }

/* ===== SPECIALIZED COMPONENTS ===== */
.saas-metric-card {
    background-color: var(--saas-surface);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    text-align: center;
    transition: all 0.2s ease-in-out;
}

.saas-metric-card:hover {
    box-shadow: var(--saas-shadow-md);
}

.saas-metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--saas-text-primary);
    margin-bottom: var(--space-xs);
}

.saas-metric-label {
    font-size: 0.875rem;
    color: var(--saas-text-secondary);
    margin-bottom: var(--space-sm);
}

.saas-metric-change {
    font-size: 0.75rem;
    font-weight: 500;
}

.saas-metric-change.positive {
    color: var(--saas-success);
}

.saas-metric-change.negative {
    color: var(--saas-error);
}

/* ===== ANIMATION UTILITIES ===== */
.saas-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.saas-slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .saas-container {
        padding: 0 var(--space-md);
    }
    
    .saas-grid-cols-2,
    .saas-grid-cols-3,
    .saas-grid-cols-4 {
        grid-template-columns: 1fr;
    }
    
    .saas-nav-container {
        padding: 0 var(--space-md);
    }
    
    .saas-nav-menu {
        gap: var(--space-md);
    }
    
    .saas-heading-xl {
        font-size: 1.875rem;
    }
    
    .saas-heading-lg {
        font-size: 1.5rem;
    }
}

/* ===== USER MENU COMPONENT ===== */
.saas-user-menu {
    position: relative;
}

.saas-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--saas-accent);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.saas-user-avatar:hover {
    box-shadow: var(--saas-shadow-md);
}

.saas-user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-sm);
    min-width: 250px;
    background-color: var(--saas-surface);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--saas-shadow-lg);
    padding: var(--space-md);
    z-index: 1000;
}

.saas-user-info {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding-bottom: var(--space-md);
    margin-bottom: var(--space-md);
    border-bottom: 1px solid var(--saas-border);
}

.saas-user-info .avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--saas-accent);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.saas-user-details h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--saas-text-primary);
    margin-bottom: var(--space-xs);
}

.saas-user-details p {
    font-size: 0.75rem;
    color: var(--saas-text-secondary);
}

/* ===== TAB SYSTEM ===== */
.saas-tabs {
    border-bottom: 1px solid var(--saas-border);
    margin-bottom: var(--space-lg);
}

.saas-tab-list {
    display: flex;
    gap: var(--space-lg);
}

.saas-tab {
    padding: var(--space-md) 0;
    border-bottom: 2px solid transparent;
    color: var(--saas-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    background: none;
    border-left: none;
    border-right: none;
    border-top: none;
}

.saas-tab:hover {
    color: var(--saas-text-primary);
}

.saas-tab.active {
    color: var(--saas-accent);
    border-bottom-color: var(--saas-accent);
}

.saas-tab-content {
    display: none;
}

.saas-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* ===== UTILITY CLASSES ===== */
.hidden { display: none !important; }
.visible { display: block !important; }
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.saas-rounded { border-radius: var(--radius); }
.saas-rounded-lg { border-radius: var(--radius-lg); }
.saas-rounded-xl { border-radius: var(--radius-xl); }
.saas-rounded-full { border-radius: 9999px; }

.saas-shadow { box-shadow: var(--saas-shadow); }
.saas-shadow-md { box-shadow: var(--saas-shadow-md); }
.saas-shadow-lg { box-shadow: var(--saas-shadow-lg); }
.saas-shadow-xl { box-shadow: var(--saas-shadow-xl); }

.saas-w-full { width: 100%; }
.saas-h-full { height: 100%; }
.saas-min-h-screen { min-height: 100vh; }

.saas-text-center { text-align: center; }
.saas-text-left { text-align: left; }
.saas-text-right { text-align: right; }

/* ===== CHART SYSTEM ===== */
.saas-chart-container {
    background: var(--saas-surface);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-xl);
    box-shadow: var(--saas-shadow);
}

.saas-chart-scale {
    position: relative;
    height: 20px;
    margin-bottom: 25px;
    border-bottom: 1px solid var(--saas-border);
}

.saas-gas-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--space-md);
    padding: var(--space-md) 0;
    border-bottom: 1px solid var(--saas-border);
    transition: all 0.2s ease-in-out;
    border-radius: var(--radius);
}

.saas-gas-row:last-child {
    border-bottom: none;
}

.saas-gas-row:hover {
    background: var(--saas-bg-subtle);
    transform: translateX(2px);
    box-shadow: var(--saas-shadow-sm);
    border-color: var(--saas-border-hover);
}

.saas-gas-name {
    width: 200px;
    font-weight: 500;
    color: var(--saas-text-primary);
    font-size: 0.875rem;
    padding-right: var(--space-md);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
}

.saas-bar-area {
    flex-grow: 1;
    height: 50px;
    position: relative;
    background: var(--saas-bg-subtle);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius);
    overflow: visible;
    margin: 0 var(--space-sm);
}

.saas-bar {
    position: absolute;
    height: 70%;
    top: 15%;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: white;
    font-weight: 500;
    box-shadow: var(--saas-shadow-sm);
    transition: all 0.2s ease-in-out;
}

.saas-bar:hover {
    transform: translateY(-2px);
    box-shadow: var(--saas-shadow-md);
}

.saas-current-bar {
    background: linear-gradient(135deg, var(--saas-accent) 0%, #B91C1C 100%);
    border: 1px solid var(--saas-accent);
    z-index: 3;
}

.saas-target-bar {
    background: linear-gradient(135deg, var(--saas-success) 0%, #047857 100%);
    border: 1px solid var(--saas-success);
    height: 100%;
    top: 0;
    opacity: 0.8;
    z-index: 2;
}

.saas-gap-notes {
    width: 280px;
    padding-left: var(--space-md);
    flex-shrink: 0;
}

.saas-gap-notes-text {
    font-size: 0.75rem;
    color: var(--saas-text-secondary);
    line-height: 1.4;
    padding: var(--space-sm);
    background: var(--saas-bg-subtle);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--saas-accent);
}

.saas-scale-label {
    position: absolute;
    bottom: -25px;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: var(--saas-text-secondary);
    white-space: nowrap;
    background: var(--saas-surface);
    padding: 2px 4px;
    border-radius: var(--radius-sm);
}

.saas-scale-tick {
    position: absolute;
    bottom: 0;
    width: 1px;
    height: 8px;
    background-color: var(--saas-border-hover);
}

.saas-chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: var(--saas-bg-subtle);
    border-radius: var(--radius);
}

.saas-legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.saas-legend-color {
    width: 16px;
    height: 16px;
    border-radius: var(--radius-sm);
    box-shadow: var(--saas-shadow-sm);
}

.saas-legend-text {
    font-size: 0.875rem;
    color: var(--saas-text-secondary);
    font-weight: 500;
}

/* Chart Tooltips */
.saas-tooltip {
    visibility: hidden;
    width: auto;
    min-width: 120px;
    background-color: var(--saas-text-primary);
    color: var(--saas-surface);
    text-align: center;
    border-radius: var(--radius);
    padding: var(--space-sm);
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    font-size: 0.75rem;
    white-space: nowrap;
    box-shadow: var(--saas-shadow-lg);
}

.saas-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--saas-text-primary) transparent transparent transparent;
}

.saas-bar:hover .saas-tooltip {
    visibility: visible;
    opacity: 1;
}

/* Delete Button for Custom Analytes */
.delete-analyte-btn {
    background: none;
    border: none;
    color: var(--saas-error);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease-in-out;
    font-size: 0.875rem;
}

.delete-analyte-btn:hover {
    background: rgba(220, 38, 38, 0.1);
    transform: scale(1.1);
}

/* ===== END CHART SYSTEM ===== */

/* ===== ADDITIONAL UTILITY CLASSES ===== */

/* List styles */
.saas-list {
    list-style: none;
    padding-left: 0;
}

.saas-list li {
    margin-bottom: var(--space-xs);
    color: var(--saas-text-secondary);
}

/* Width and Height Utilities */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }

/* Margin and Padding Utilities (additional) */
.saas-mx-auto { margin-left: auto; margin-right: auto; }
.saas-my-auto { margin-top: auto; margin-bottom: auto; }
.saas-mr-sm { margin-right: var(--space-sm); }
.saas-mr-md { margin-right: var(--space-md); }
.saas-ml-sm { margin-left: var(--space-sm); }
.saas-ml-md { margin-left: var(--space-md); }

/* Additional padding utilities */
.saas-py-3xl { padding-top: var(--space-3xl); padding-bottom: var(--space-3xl); }
.saas-px-lg { padding-left: var(--space-lg); padding-right: var(--space-lg); }

/* Flex utilities */
.saas-flex-1 { flex: 1; }

/* Position utilities */
.fixed { position: fixed; }
.relative { position: relative; }
.absolute { position: absolute; }
.sticky { position: sticky; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.left-0 { left: 0; }
.bottom-0 { bottom: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-1000 { z-index: 1000; }

/* Display utilities */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.grid { display: grid; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Background color utilities */
.saas-bg-accent { background-color: var(--saas-accent); }
.saas-bg-success { background-color: var(--saas-success); }
.saas-bg-warning { background-color: var(--saas-warning); }
.saas-bg-error { background-color: var(--saas-error); }
.saas-bg-info { background-color: var(--saas-info); }

/* Enhanced dropdown positioning for user menu */
.saas-user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-sm);
    min-width: 250px;
    background-color: var(--saas-surface);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--saas-shadow-lg);
    padding: var(--space-md);
    z-index: 1000;
}

.saas-chart-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--saas-text-primary);
    margin-bottom: var(--space-sm);
}

.saas-chart-subtitle {
    font-size: 0.875rem;
    color: var(--saas-text-secondary);
    margin-bottom: var(--space-lg);
}

.saas-legend {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.saas-legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.saas-legend-color {
    width: 1rem;
    height: 1rem;
    border-radius: var(--radius-sm);
    border: 1px solid var(--saas-border);
}

.saas-gas-row {
    display: flex;
    align-items: center;
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--saas-border);
    transition: background-color 0.2s ease-in-out;
}

.saas-gas-row:hover {
    background-color: var(--saas-bg-subtle);
}

.saas-gas-row:last-child {
    border-bottom: none;
}

.saas-gas-label {
    min-width: 120px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--saas-text-primary);
    padding-right: var(--space-md);
}

.saas-gas-bar-container {
    flex: 1;
    position: relative;
    height: 24px;
    background-color: var(--saas-bg-muted);
    border-radius: var(--radius);
    overflow: hidden;
}

.saas-gas-bar {
    height: 100%;
    border-radius: var(--radius);
    position: relative;
    transition: all 0.3s ease-in-out;
}

.saas-gas-bar.current {
    background: linear-gradient(90deg, var(--saas-accent) 0%, #DC2626 100%);
}

.saas-gas-bar.target {
    background: linear-gradient(90deg, var(--saas-success) 0%, var(--saas-info) 100%);
}

.saas-gas-value {
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.saas-gas-actions {
    min-width: 100px;
    padding-left: var(--space-md);
}

/* Chart scale */
.saas-chart-scale {
    position: relative;
    height: 20px;
    margin-bottom: 25px;
    border-bottom: 1px solid var(--saas-border);
}

.saas-scale-marker {
    position: absolute;
    top: 0;
    height: 100%;
    width: 1px;
    background-color: var(--saas-border);
}

.saas-scale-label {
    position: absolute;
    bottom: -20px;
    font-size: 0.75rem;
    color: var(--saas-text-muted);
    transform: translateX(-50%);
}

/* Form styling for ranges */
.saas-range-input-group {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    background-color: var(--saas-bg-subtle);
    border-radius: var(--radius);
    margin-bottom: var(--space-sm);
}

.saas-range-input {
    flex: 1;
    padding: var(--space-xs) var(--space-sm);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    background-color: var(--saas-surface);
}

.saas-range-input:focus {
    outline: none;
    border-color: var(--saas-accent);
}

.saas-range-remove {
    background: none;
    border: none;
    color: var(--saas-error);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: background-color 0.2s ease-in-out;
}

.saas-range-remove:hover {
    background-color: rgba(220, 38, 38, 0.1);
}

/* AI Analysis Results */
.saas-ai-results {
    background-color: var(--saas-bg-subtle);
    border: 1px solid var(--saas-border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin-top: var(--space-lg);
}

.saas-ai-result-item {
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--saas-border);
}

.saas-ai-result-item:last-child {
    border-bottom: none;
}

.saas-ai-result-header {
    font-weight: 600;
    color: var(--saas-text-primary);
    margin-bottom: var(--space-xs);
}

.saas-ai-result-content {
    color: var(--saas-text-secondary);
    font-size: 0.875rem;
}

/* Loading states */
.saas-loading {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md);
    color: var(--saas-text-secondary);
}

.saas-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--saas-border);
    border-top: 2px solid var(--saas-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
