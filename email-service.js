/**
 * Email Notification System for RBAC
 * Handles notifications for user approval/rejection and system events
 */

export class EmailNotificationService {
    constructor() {
        this.initialized = false;
        this.config = {
            // Email service configuration - would be set via environment variables
            service: 'web', // 'web' for web-based, 'smtp' for server-based
            templates: {
                userApproved: {
                    subject: 'Account Approved - Alpha Gas Solution Analysis',
                    template: 'user-approved'
                },
                userRejected: {
                    subject: 'Account Application Update - Alpha Gas Solution Analysis',
                    template: 'user-rejected'
                },
                newUserRegistration: {
                    subject: 'New User Registration - Admin Review Required',
                    template: 'new-user-admin'
                },
                roleChanged: {
                    subject: 'Account Role Updated - Alpha Gas Solution Analysis',
                    template: 'role-changed'
                }
            }
        };
        this.notificationQueue = [];
        this.adminEmails = ['<EMAIL>']; // Would be configured
    }

    /**
     * Initialize the notification service
     */
    async init() {
        if (this.initialized) return;
        
        console.log('📧 Initializing Email Notification Service...');
        
        // Start queue processor
        this.startQueueProcessor();
        
        this.initialized = true;
        console.log('✅ Email Notification Service initialized');
    }

    /**
     * Send user approval notification
     */
    async notifyUserApproved(userEmail, userName, role = 'user') {
        const notification = {
            type: 'user-approved',
            to: userEmail,
            data: {
                userName,
                role,
                loginUrl: window.location.origin + '/app.html',
                timestamp: new Date().toISOString()
            }
        };

        await this.queueNotification(notification);
    }

    /**
     * Send user rejection notification
     */
    async notifyUserRejected(userEmail, userName, reason = '') {
        const notification = {
            type: 'user-rejected',
            to: userEmail,
            data: {
                userName,
                reason,
                contactEmail: '<EMAIL>',
                timestamp: new Date().toISOString()
            }
        };

        await this.queueNotification(notification);
    }

    /**
     * Notify admins of new user registration
     */
    async notifyAdminsNewUser(userEmail, userName, userId) {
        for (const adminEmail of this.adminEmails) {
            const notification = {
                type: 'new-user-admin',
                to: adminEmail,
                data: {
                    userName,
                    userEmail,
                    userId,
                    reviewUrl: window.location.origin + '/admin-dashboard.html#users',
                    timestamp: new Date().toISOString()
                }
            };

            await this.queueNotification(notification);
        }
    }

    /**
     * Notify user of role change
     */
    async notifyRoleChanged(userEmail, userName, newRole, oldRole) {
        const notification = {
            type: 'role-changed',
            to: userEmail,
            data: {
                userName,
                newRole,
                oldRole,
                timestamp: new Date().toISOString()
            }
        };

        await this.queueNotification(notification);
    }

    /**
     * Queue notification for processing
     */
    async queueNotification(notification) {
        notification.id = `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        notification.status = 'queued';
        notification.attempts = 0;
        notification.queuedAt = new Date().toISOString();

        this.notificationQueue.push(notification);
        console.log(`📬 Queued notification: ${notification.type} to ${notification.to}`);
    }

    /**
     * Start processing notification queue
     */
    startQueueProcessor() {
        setInterval(async () => {
            await this.processQueue();
        }, 10000); // Process every 10 seconds
    }

    /**
     * Process notification queue
     */
    async processQueue() {
        const queuedNotifications = this.notificationQueue.filter(n => 
            n.status === 'queued' || (n.status === 'failed' && n.attempts < 3)
        );

        for (const notification of queuedNotifications) {
            try {
                await this.sendNotification(notification);
                notification.status = 'sent';
                notification.sentAt = new Date().toISOString();
                console.log(`✅ Sent notification: ${notification.type} to ${notification.to}`);
            } catch (error) {
                notification.attempts++;
                notification.status = 'failed';
                notification.lastError = error.message;
                notification.lastAttemptAt = new Date().toISOString();
                
                console.error(`❌ Failed to send notification (attempt ${notification.attempts}):`, error);
                
                if (notification.attempts >= 3) {
                    console.error(`💀 Notification permanently failed: ${notification.id}`);
                }
            }
        }

        // Clean up old processed notifications
        this.cleanupQueue();
    }

    /**
     * Send individual notification
     */
    async sendNotification(notification) {
        if (this.config.service === 'web') {
            return await this.sendWebNotification(notification);
        } else {
            throw new Error('SMTP service not implemented in client-side code');
        }
    }

    /**
     * Send notification via web interface (mailto or web service)
     */
    async sendWebNotification(notification) {
        const template = this.getEmailTemplate(notification.type, notification.data);
        
        // For client-side, we'll create a mailto link and optionally show in UI
        const mailtoUrl = this.createMailtoUrl(notification.to, template.subject, template.body);
        
        // Store notification for admin dashboard display
        await this.storeNotificationRecord(notification, template);
        
        // In a real implementation, this would integrate with a web email service
        console.log(`📧 Email notification prepared:`, {
            to: notification.to,
            subject: template.subject,
            mailtoUrl
        });

        // Simulate successful send for demo
        return true;
    }

    /**
     * Get email template for notification type
     */
    getEmailTemplate(type, data) {
        switch (type) {
            case 'user-approved':
                return {
                    subject: `Welcome to Alpha Gas Solution Analysis - Account Approved`,
                    body: `Dear ${data.userName},

Great news! Your account has been approved and you now have ${data.role} access to the Alpha Gas Solution Analysis Gap application.

You can now log in and start using the application:
${data.loginUrl}

Your account details:
- Role: ${data.role}
- Approved on: ${new Date(data.timestamp).toLocaleDateString()}

If you have any questions, please don't hesitate to contact our support team.

Best regards,
Alpha Gas Solution Team`
                };

            case 'user-rejected':
                return {
                    subject: 'Account Application Update - Alpha Gas Solution Analysis',
                    body: `Dear ${data.userName},

Thank you for your interest in the Alpha Gas Solution Analysis Gap application.

Unfortunately, we are unable to approve your account at this time.
${data.reason ? `\n\nReason: ${data.reason}` : ''}

If you believe this is an error or would like to discuss your application, please contact us at ${data.contactEmail}.

Best regards,
Alpha Gas Solution Team`
                };

            case 'new-user-admin':
                return {
                    subject: 'New User Registration - Admin Review Required',
                    body: `A new user has registered and requires admin approval:

User Details:
- Name: ${data.userName}
- Email: ${data.userEmail}
- User ID: ${data.userId}
- Registration Time: ${new Date(data.timestamp).toLocaleDateString()} ${new Date(data.timestamp).toLocaleTimeString()}

Please review and approve/reject this user in the admin dashboard:
${data.reviewUrl}

This is an automated notification from the Alpha Gas Solution Analysis system.`
                };

            case 'role-changed':
                return {
                    subject: 'Account Role Updated - Alpha Gas Solution Analysis',
                    body: `Dear ${data.userName},

Your account role has been updated in the Alpha Gas Solution Analysis system.

Role Change:
- Previous Role: ${data.oldRole}
- New Role: ${data.newRole}
- Updated on: ${new Date(data.timestamp).toLocaleDateString()}

This change may affect your access permissions within the application. Please log in to see your updated capabilities.

If you have any questions about this change, please contact our support team.

Best regards,
Alpha Gas Solution Team`
                };

            default:
                throw new Error(`Unknown notification type: ${type}`);
        }
    }

    /**
     * Create mailto URL for email client
     */
    createMailtoUrl(to, subject, body) {
        const params = new URLSearchParams({
            subject,
            body
        });
        return `mailto:${to}?${params.toString()}`;
    }

    /**
     * Store notification record for admin tracking
     */
    async storeNotificationRecord(notification, template) {
        try {
            // In a real implementation, this would store in the database
            const record = {
                id: notification.id,
                type: notification.type,
                recipient: notification.to,
                subject: template.subject,
                status: 'sent',
                sent_at: new Date().toISOString(),
                attempts: notification.attempts
            };

            // Store in localStorage for demo purposes
            const notifications = JSON.parse(localStorage.getItem('email_notifications') || '[]');
            notifications.push(record);
            localStorage.setItem('email_notifications', JSON.stringify(notifications.slice(-100))); // Keep last 100
            
        } catch (error) {
            console.error('Failed to store notification record:', error);
        }
    }

    /**
     * Clean up old processed notifications from queue
     */
    cleanupQueue() {
        const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
        
        this.notificationQueue = this.notificationQueue.filter(notification => {
            const notificationTime = new Date(notification.queuedAt).getTime();
            return notificationTime > cutoff && notification.status !== 'sent';
        });
    }

    /**
     * Get notification history for admin dashboard
     */
    getNotificationHistory() {
        try {
            return JSON.parse(localStorage.getItem('email_notifications') || '[]');
        } catch (error) {
            console.error('Failed to get notification history:', error);
            return [];
        }
    }

    /**
     * Get queue status for monitoring
     */
    getQueueStatus() {
        const queued = this.notificationQueue.filter(n => n.status === 'queued').length;
        const failed = this.notificationQueue.filter(n => n.status === 'failed').length;
        const processing = this.notificationQueue.filter(n => n.status === 'processing').length;
        
        return {
            total: this.notificationQueue.length,
            queued,
            failed,
            processing
        };
    }

    /**
     * Manual trigger for testing notifications
     */
    async testNotification(type, recipientEmail, testData = {}) {
        const defaultData = {
            userName: 'Test User',
            role: 'user',
            timestamp: new Date().toISOString(),
            ...testData
        };

        switch (type) {
            case 'user-approved':
                await this.notifyUserApproved(recipientEmail, defaultData.userName, defaultData.role);
                break;
            case 'user-rejected':
                await this.notifyUserRejected(recipientEmail, defaultData.userName, 'Test rejection');
                break;
            case 'role-changed':
                await this.notifyRoleChanged(recipientEmail, defaultData.userName, 'admin', 'user');
                break;
            default:
                throw new Error(`Unknown test notification type: ${type}`);
        }
    }
}

// Create singleton instance
export const emailService = new EmailNotificationService();

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        emailService.init().catch(console.error);
    });
}

console.log('📧 Email Notification Service module loaded');
