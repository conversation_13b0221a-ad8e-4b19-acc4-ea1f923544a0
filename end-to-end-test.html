<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>End-to-End RBAC Flow Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-4">🔄 End-to-End RBAC Flow Test</h1>
        
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
            <h2 class="font-semibold text-blue-800 mb-2">Test Scenario</h2>
            <p class="text-sm text-blue-600">
                This test simulates the complete user journey through the RBAC system, 
                from guest access to user registration and role-based feature access.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Test Flow -->
            <div class="space-y-4">
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">1. Guest Access Test</h3>
                    <div id="guestTest" class="space-y-2 text-sm"></div>
                    <button id="testGuest" class="mt-2 bg-blue-600 text-white px-3 py-1 rounded text-sm">
                        Test Guest Mode
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">2. Authentication Flow</h3>
                    <div id="authFlow" class="space-y-2 text-sm"></div>
                    <button id="testAuthFlow" class="mt-2 bg-green-600 text-white px-3 py-1 rounded text-sm">
                        Test Auth Flow
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">3. Role Transitions</h3>
                    <div id="roleTransitions" class="space-y-2 text-sm"></div>
                    <button id="testRoleTransitions" class="mt-2 bg-purple-600 text-white px-3 py-1 rounded text-sm">
                        Test Role Changes
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">4. Feature Access Control</h3>
                    <div id="featureAccess" class="space-y-2 text-sm"></div>
                    <button id="testFeatures" class="mt-2 bg-orange-600 text-white px-3 py-1 rounded text-sm">
                        Test Feature Access
                    </button>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">5. Data Persistence</h3>
                    <div id="dataPersistence" class="space-y-2 text-sm"></div>
                    <button id="testData" class="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm">
                        Test Data Operations
                    </button>
                </div>
            </div>
            
            <!-- Results & Actions -->
            <div class="space-y-4">
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Test Results Summary</h3>
                    <div id="summaryResults" class="space-y-2 text-sm">
                        <div class="text-gray-500">Run tests to see results...</div>
                    </div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Application Links</h3>
                    <div class="space-y-2">
                        <a href="./index.html" target="_blank" class="block text-blue-600 hover:underline">
                            → Main Application (index.html)
                        </a>
                        <a href="./app.html" target="_blank" class="block text-blue-600 hover:underline">
                            → Full App Interface (app.html)
                        </a>
                        <a href="./guest-mode-simple.html" target="_blank" class="block text-blue-600 hover:underline">
                            → Guest Mode (guest-mode-simple.html)
                        </a>
                        <a href="./direct-login-simple.html" target="_blank" class="block text-blue-600 hover:underline">
                            → Login Interface (direct-login-simple.html)
                        </a>
                        <a href="./admin-dashboard.html" target="_blank" class="block text-blue-600 hover:underline">
                            → Admin Dashboard (admin-dashboard.html)
                        </a>
                    </div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Quick Actions</h3>
                    <div class="space-y-2">
                        <button id="runAllTests" class="w-full bg-indigo-600 text-white px-4 py-2 rounded">
                            Run All Tests
                        </button>
                        <button id="openDebugConsole" class="w-full bg-gray-600 text-white px-4 py-2 rounded">
                            Open Debug Console
                        </button>
                        <button id="checkCurrentUser" class="w-full bg-teal-600 text-white px-4 py-2 rounded">
                            Check Current User Status
                        </button>
                    </div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Live Status</h3>
                    <div id="liveStatus" class="space-y-2 text-sm">
                        <div class="text-gray-500">Initializing...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        let testResults = {
            guest: { status: 'pending', results: [] },
            auth: { status: 'pending', results: [] },
            roles: { status: 'pending', results: [] },
            features: { status: 'pending', results: [] },
            data: { status: 'pending', results: [] }
        };

        function addResult(testType, message, status = 'info') {
            const section = document.getElementById(testType + 'Test') || 
                          document.getElementById(testType + 'Flow') || 
                          document.getElementById(testType + 'Transitions') ||
                          document.getElementById('featureAccess') ||
                          document.getElementById('dataPersistence');
            
            if (section) {
                const div = document.createElement('div');
                const colors = {
                    success: 'text-green-600',
                    error: 'text-red-600',
                    warning: 'text-yellow-600',
                    info: 'text-blue-600'
                };
                div.className = colors[status] || 'text-gray-600';
                div.textContent = message;
                section.appendChild(div);
                
                // Store result
                if (testResults[testType]) {
                    testResults[testType].results.push({ message, status, timestamp: new Date().toISOString() });
                    if (status === 'error') {
                        testResults[testType].status = 'error';
                    } else if (status === 'success' && testResults[testType].status === 'pending') {
                        testResults[testType].status = 'success';
                    }
                }
            }
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summaryResults');
            let html = '';
            
            Object.keys(testResults).forEach(testType => {
                const result = testResults[testType];
                const icon = result.status === 'success' ? '✅' : 
                           result.status === 'error' ? '❌' : 
                           result.status === 'warning' ? '⚠️' : '⏳';
                html += `<div class="flex justify-between">
                    <span>${testType.charAt(0).toUpperCase() + testType.slice(1)} Test:</span>
                    <span>${icon}</span>
                </div>`;
            });
            
            summaryDiv.innerHTML = html || '<div class="text-gray-500">No results yet</div>';
        }

        async function testGuestAccess() {
            document.getElementById('guestTest').innerHTML = '';
            addResult('guest', 'Testing guest access capabilities...', 'info');
            
            try {
                // Test role manager initialization for guest
                const roleModule = await import('./role-manager.js');
                const roleManager = roleModule.roleManager;
                
                await roleManager.init();
                const guestRole = roleManager.getRole();
                
                if (guestRole === 'guest') {
                    addResult('guest', '✓ Guest role properly assigned', 'success');
                } else {
                    addResult('guest', `⚠ Expected guest role, got: ${guestRole}`, 'warning');
                }

                // Test guest permissions
                const permissions = roleManager.getPermissions();
                const expectedGuestPerms = ['view_system_data', 'use_calculator'];
                
                let hasBasicPerms = expectedGuestPerms.every(perm => permissions[perm]);
                if (hasBasicPerms) {
                    addResult('guest', '✓ Guest has basic permissions', 'success');
                } else {
                    addResult('guest', '⚠ Guest permissions incomplete', 'warning');
                }

                // Test guest restrictions
                const restrictions = roleManager.getUIRestrictions();
                if (restrictions.hideAdminFeatures && restrictions.hideUserFeatures) {
                    addResult('guest', '✓ Guest restrictions properly applied', 'success');
                } else {
                    addResult('guest', '⚠ Guest restrictions not fully applied', 'warning');
                }

                addResult('guest', '✅ Guest access test completed', 'success');
                
            } catch (error) {
                addResult('guest', `❌ Guest test failed: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function testAuthenticationFlow() {
            document.getElementById('authFlow').innerHTML = '';
            addResult('auth', 'Testing authentication flow...', 'info');
            
            try {
                const authModule = await import('./auth.js');
                const authManager = authModule.authManager;

                // Test current user status
                const currentUser = authManager.getCurrentUser();
                if (currentUser) {
                    addResult('auth', `✓ User authenticated: ${currentUser.email}`, 'success');
                    
                    // Test profile retrieval
                    const profile = await authManager.getUserProfile();
                    if (profile) {
                        addResult('auth', `✓ Profile found: ${profile.role} (${profile.status})`, 'success');
                        
                        // Test role-based auth functions
                        const isAdmin = await authManager.isAdmin();
                        const isApproved = await authManager.isApproved();
                        const userRole = await authManager.getUserRole();
                        
                        addResult('auth', `✓ Auth functions: Admin(${isAdmin}), Approved(${isApproved}), Role(${userRole})`, 'success');
                    } else {
                        addResult('auth', '⚠ Profile not found in database', 'warning');
                    }
                } else {
                    addResult('auth', '⚠ No user authenticated (testing as guest)', 'warning');
                    
                    // Test guest auth behavior
                    try {
                        const profile = await authManager.getUserProfile();
                        if (!profile) {
                            addResult('auth', '✓ Null profile correctly returned for guest', 'success');
                        }
                    } catch (e) {
                        addResult('auth', '✓ Auth functions handle guest state correctly', 'success');
                    }
                }

                addResult('auth', '✅ Authentication flow test completed', 'success');
                
            } catch (error) {
                addResult('auth', `❌ Auth flow test failed: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function testRoleTransitions() {
            document.getElementById('roleTransitions').innerHTML = '';
            addResult('roles', 'Testing role transition handling...', 'info');
            
            try {
                const roleModule = await import('./role-manager.js');
                const roleManager = roleModule.roleManager;

                await roleManager.init();
                addResult('roles', '✓ Role manager initialized', 'success');

                // Test role state consistency
                const role = roleManager.getRole();
                const status = roleManager.getStatus();
                addResult('roles', `✓ Current state: ${role} (${status})`, 'success');

                // Test role-based UI setup
                try {
                    const uiInfo = roleManager.setupUI();
                    addResult('roles', `✓ UI setup configured: ${JSON.stringify(uiInfo)}`, 'success');
                } catch (e) {
                    addResult('roles', `⚠ UI setup test: ${e.message}`, 'warning');
                }

                // Test permission checking
                const adminAccess = roleManager.hasRole('admin');
                const userAccess = roleManager.hasRole('user');
                const guestAccess = roleManager.hasRole('guest');
                
                addResult('roles', `✓ Role checks - Admin: ${adminAccess}, User: ${userAccess}, Guest: ${guestAccess}`, 'success');

                // Test status notifications
                try {
                    const notifications = roleManager.getStatusNotifications();
                    if (notifications) {
                        addResult('roles', '✓ Status notifications configured', 'success');
                    }
                } catch (e) {
                    addResult('roles', '⚠ Status notifications not available', 'warning');
                }

                addResult('roles', '✅ Role transition test completed', 'success');

            } catch (error) {
                addResult('roles', `❌ Role transition test failed: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function testFeatureAccess() {
            document.getElementById('featureAccess').innerHTML = '';
            addResult('features', 'Testing feature access control...', 'info');
            
            try {
                const roleModule = await import('./role-manager.js');
                const roleManager = roleModule.roleManager;

                await roleManager.init();
                
                // Test permission matrix
                const permissions = roleManager.getPermissions();
                const requiredPerms = ['view_system_data', 'use_calculator'];
                
                let permissionResults = [];
                requiredPerms.forEach(perm => {
                    permissionResults.push(`${perm}: ${permissions[perm] ? '✓' : '✗'}`);
                });
                
                addResult('features', `✓ Permissions: ${permissionResults.join(', ')}`, 'success');

                // Test UI restrictions
                const restrictions = roleManager.getUIRestrictions();
                const restrictionKeys = Object.keys(restrictions);
                addResult('features', `✓ UI restrictions: ${restrictionKeys.length} configured`, 'success');

                // Test feature enabling/disabling
                const role = roleManager.getRole();
                let expectedFeatures = [];
                
                switch (role) {
                    case 'admin':
                        expectedFeatures = ['admin_dashboard', 'user_management', 'data_persistence', 'system_features'];
                        break;
                    case 'user':
                        expectedFeatures = ['data_persistence', 'personal_analytes', 'system_features'];
                        break;
                    case 'guest':
                        expectedFeatures = ['view_only', 'calculator'];
                        break;
                }

                addResult('features', `✓ Expected features for ${role}: ${expectedFeatures.join(', ')}`, 'success');

                // Test admin-specific features
                if (role === 'admin') {
                    // Test admin dashboard access
                    try {
                        const hasAdminAccess = permissions.admin_dashboard;
                        addResult('features', `✓ Admin dashboard access: ${hasAdminAccess}`, 'success');
                    } catch (e) {
                        addResult('features', '⚠ Admin feature test skipped', 'warning');
                    }
                }

                addResult('features', '✅ Feature access test completed', 'success');

            } catch (error) {
                addResult('features', `❌ Feature access test failed: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function testDataPersistence() {
            document.getElementById('dataPersistence').innerHTML = '';
            addResult('data', 'Testing data persistence with RBAC...', 'info');
            
            try {
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

                // Test system data access
                const { data: systemData, error: systemError } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('data_type', 'system')
                    .limit(5);

                if (systemError) {
                    addResult('data', `⚠ System data access: ${systemError.message}`, 'warning');
                } else {
                    addResult('data', `✓ System data accessible: ${systemData.length} records`, 'success');
                }

                // Test user data access (if authenticated)
                const { data: currentUser } = await supabase.auth.getUser();
                if (currentUser.user) {
                    const { data: userData, error: userError } = await supabase
                        .from('gas_analytes')
                        .select('*')
                        .eq('data_type', 'user_target')
                        .limit(5);

                    if (userError) {
                        addResult('data', `⚠ User data access: ${userError.message}`, 'warning');
                    } else {
                        addResult('data', `✓ User data accessible: ${userData.length} records`, 'success');
                    }
                } else {
                    addResult('data', '⚠ User data access test skipped (not authenticated)', 'warning');
                }

                // Test form handler integration
                try {
                    const formModule = await import('./form-handler.js');
                    addResult('data', '✓ Form handler module loaded', 'success');
                    
                    // Check if hybrid properties are configured
                    const testAnalyte = {
                        name: 'Test Analyte',
                        target_value: 100,
                        unit: 'ppm'
                    };
                    
                    // This would test the actual form submission logic
                    addResult('data', '✓ Form handler ready for analyte creation', 'success');
                } catch (e) {
                    addResult('data', `⚠ Form handler test: ${e.message}`, 'warning');
                }

                // Test storage system integration
                try {
                    const storageModule = await import('./storage.js');
                    addResult('data', '✓ Storage system module loaded', 'success');
                } catch (e) {
                    addResult('data', `⚠ Storage system test: ${e.message}`, 'warning');
                }

                addResult('data', '✅ Data persistence test completed', 'success');

            } catch (error) {
                addResult('data', `❌ Data persistence test failed: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function runAllTests() {
            // Clear all previous results
            Object.keys(testResults).forEach(key => {
                testResults[key] = { status: 'pending', results: [] };
            });

            // Clear UI
            ['guestTest', 'authFlow', 'roleTransitions', 'featureAccess', 'dataPersistence'].forEach(id => {
                const element = document.getElementById(id);
                if (element) element.innerHTML = '';
            });

            addResult('guest', '🚀 Starting comprehensive RBAC test suite...', 'info');

            await testGuestAccess();
            await testAuthenticationFlow();
            await testRoleTransitions();
            await testFeatureAccess();
            await testDataPersistence();

            updateSummary();
        }

        async function checkCurrentUserStatus() {
            const statusDiv = document.getElementById('liveStatus');
            statusDiv.innerHTML = '<div class="text-gray-500">Checking current status...</div>';
            
            try {
                const authModule = await import('./auth.js');
                const roleModule = await import('./role-manager.js');
                
                const authManager = authModule.authManager;
                const roleManager = roleModule.roleManager;

                await roleManager.init();

                const currentUser = authManager.getCurrentUser();
                const role = roleManager.getRole();
                const status = roleManager.getStatus();

                let statusHTML = '';
                if (currentUser) {
                    statusHTML = `
                        <div><strong>User:</strong> ${currentUser.email}</div>
                        <div><strong>Role:</strong> ${role}</div>
                        <div><strong>Status:</strong> ${status}</div>
                        <div><strong>Authenticated:</strong> ✅</div>
                    `;

                    const profile = await authManager.getUserProfile();
                    if (profile) {
                        statusHTML += `<div><strong>Profile:</strong> ✅ Found</div>`;
                    } else {
                        statusHTML += `<div><strong>Profile:</strong> ⚠️ Not found</div>`;
                    }
                } else {
                    statusHTML = `
                        <div><strong>Status:</strong> Guest</div>
                        <div><strong>Authenticated:</strong> ❌</div>
                        <div><strong>Role:</strong> ${role}</div>
                    `;
                }

                statusDiv.innerHTML = statusHTML;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="text-red-500">Error: ${error.message}</div>`;
            }
        }

        // Event listeners
        document.getElementById('testGuest').addEventListener('click', testGuestAccess);
        document.getElementById('testAuthFlow').addEventListener('click', testAuthenticationFlow);
        document.getElementById('testRoleTransitions').addEventListener('click', testRoleTransitions);
        document.getElementById('testFeatures').addEventListener('click', testFeatureAccess);
        document.getElementById('testData').addEventListener('click', testDataPersistence);
        document.getElementById('runAllTests').addEventListener('click', runAllTests);
        document.getElementById('checkCurrentUser').addEventListener('click', checkCurrentUserStatus);
        
        document.getElementById('openDebugConsole').addEventListener('click', () => {
            window.open('./debug-console.html', '_blank');
        });

        // Initialize
        window.addEventListener('load', () => {
            setTimeout(checkCurrentUserStatus, 500);
            updateSummary();
        });

        // Handle errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled rejection:', event.reason);
        });
    </script>
</body>
</html>
