// Enhanced Error Handling and Recovery System for RBAC
// This module provides comprehensive error handling, retry mechanisms, and system recovery

export class ErrorHandler {
    constructor() {
        this.errorLog = [];
        this.retryAttempts = new Map();
        this.circuitBreakers = new Map();
        this.errorCallbacks = new Map();
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second
        this.circuitBreakerThreshold = 5;
        this.circuitBreakerTimeout = 30000; // 30 seconds
        
        this.initializeGlobalErrorHandling();
    }

    initializeGlobalErrorHandling() {
        // Capture unhandled errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'javascript_error',
                message: event.error?.message || 'Unknown error',
                stack: event.error?.stack,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: new Date().toISOString()
            });
        });

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'unhandled_promise',
                message: event.reason?.message || event.reason || 'Unhandled promise rejection',
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });
    }

    logError(error) {
        const errorEntry = {
            id: this.generateErrorId(),
            ...error,
            timestamp: error.timestamp || new Date().toISOString()
        };
        
        this.errorLog.push(errorEntry);
        
        // Keep only last 100 errors to prevent memory issues
        if (this.errorLog.length > 100) {
            this.errorLog = this.errorLog.slice(-100);
        }
        
        console.error('Error logged:', errorEntry);
        
        // Trigger error callbacks
        this.triggerErrorCallbacks(errorEntry);
        
        return errorEntry.id;
    }

    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    triggerErrorCallbacks(error) {
        this.errorCallbacks.forEach((callback, type) => {
            if (type === 'all' || error.type === type) {
                try {
                    callback(error);
                } catch (e) {
                    console.error('Error in error callback:', e);
                }
            }
        });
    }

    onError(type, callback) {
        this.errorCallbacks.set(type, callback);
    }

    async withErrorHandling(fn, context = 'unknown', options = {}) {
        const {
            retries = this.maxRetries,
            delay = this.retryDelay,
            fallback = null,
            circuitBreaker = false
        } = options;

        const operation = `${context}_${Date.now()}`;
        
        // Check circuit breaker
        if (circuitBreaker && this.isCircuitBreakerOpen(context)) {
            const error = new Error(`Circuit breaker open for ${context}`);
            this.logError({
                type: 'circuit_breaker',
                message: error.message,
                context,
                operation
            });
            
            if (fallback) {
                return await fallback();
            }
            throw error;
        }

        let lastError;
        let attempt = 0;

        while (attempt <= retries) {
            try {
                const result = await fn();
                
                // Reset circuit breaker on success
                if (circuitBreaker) {
                    this.resetCircuitBreaker(context);
                }
                
                // Reset retry counter on success
                this.retryAttempts.delete(operation);
                
                return result;
            } catch (error) {
                lastError = error;
                attempt++;
                
                const errorId = this.logError({
                    type: 'operation_error',
                    message: error.message,
                    stack: error.stack,
                    context,
                    operation,
                    attempt,
                    maxRetries: retries
                });

                // Update circuit breaker
                if (circuitBreaker) {
                    this.incrementCircuitBreaker(context);
                }

                // If this is the last attempt, don't wait
                if (attempt > retries) {
                    break;
                }

                // Wait before retry
                await this.delay(delay * attempt); // Exponential backoff
                
                console.warn(`Retrying operation ${context} (attempt ${attempt}/${retries + 1})`);
            }
        }

        // All retries exhausted
        const finalError = new Error(`Operation failed after ${retries + 1} attempts: ${lastError.message}`);
        finalError.originalError = lastError;
        finalError.context = context;
        
        this.logError({
            type: 'operation_failed',
            message: finalError.message,
            context,
            operation,
            attempts: attempt,
            originalError: lastError.message
        });

        if (fallback) {
            try {
                return await fallback();
            } catch (fallbackError) {
                this.logError({
                    type: 'fallback_error',
                    message: fallbackError.message,
                    context,
                    operation
                });
                throw finalError; // Throw original error if fallback fails
            }
        }

        throw finalError;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    incrementCircuitBreaker(context) {
        const current = this.circuitBreakers.get(context) || { failures: 0, lastFailure: null, isOpen: false };
        current.failures++;
        current.lastFailure = Date.now();
        
        if (current.failures >= this.circuitBreakerThreshold) {
            current.isOpen = true;
            current.openedAt = Date.now();
            console.warn(`Circuit breaker opened for ${context}`);
        }
        
        this.circuitBreakers.set(context, current);
    }

    isCircuitBreakerOpen(context) {
        const breaker = this.circuitBreakers.get(context);
        if (!breaker || !breaker.isOpen) {
            return false;
        }
        
        // Check if timeout has elapsed
        if (Date.now() - breaker.openedAt > this.circuitBreakerTimeout) {
            breaker.isOpen = false;
            breaker.failures = 0;
            this.circuitBreakers.set(context, breaker);
            console.info(`Circuit breaker reset for ${context}`);
            return false;
        }
        
        return true;
    }

    resetCircuitBreaker(context) {
        const breaker = this.circuitBreakers.get(context);
        if (breaker) {
            breaker.failures = 0;
            breaker.isOpen = false;
            breaker.openedAt = null;
            this.circuitBreakers.set(context, breaker);
        }
    }

    getErrorStats() {
        const stats = {
            totalErrors: this.errorLog.length,
            errorsByType: {},
            recentErrors: this.errorLog.slice(-10),
            circuitBreakers: Array.from(this.circuitBreakers.entries()),
            activeRetries: this.retryAttempts.size
        };
        
        this.errorLog.forEach(error => {
            stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
        });
        
        return stats;
    }

    clearErrors() {
        this.errorLog = [];
        this.retryAttempts.clear();
        this.circuitBreakers.clear();
    }

    // Specific error recovery strategies
    async recoverFromAuthError() {
        try {
            // Try to refresh auth state
            if (window.supabase) {
                const { data, error } = await window.supabase.auth.getSession();
                if (error) throw error;
                
                console.info('Auth recovery successful');
                return true;
            }
        } catch (error) {
            this.logError({
                type: 'auth_recovery_failed',
                message: error.message
            });
        }
        return false;
    }

    async recoverFromDatabaseError() {
        try {
            // Test database connectivity
            if (window.supabase) {
                const { data, error } = await window.supabase
                    .from('user_profiles')
                    .select('count')
                    .limit(1);
                
                if (error) throw error;
                
                console.info('Database recovery successful');
                return true;
            }
        } catch (error) {
            this.logError({
                type: 'database_recovery_failed',
                message: error.message
            });
        }
        return false;
    }

    async recoverFromRoleError() {
        try {
            // Reinitialize role manager
            if (window.roleManager) {
                await window.roleManager.init();
                console.info('Role system recovery successful');
                return true;
            }
        } catch (error) {
            this.logError({
                type: 'role_recovery_failed',
                message: error.message
            });
        }
        return false;
    }

    // Enhanced error boundaries for React-like error handling
    createErrorBoundary(component, fallbackComponent = null) {
        return async (...args) => {
            try {
                return await component(...args);
            } catch (error) {
                this.logError({
                    type: 'component_error',
                    message: error.message,
                    stack: error.stack,
                    component: component.name || 'anonymous'
                });
                
                if (fallbackComponent) {
                    try {
                        return await fallbackComponent(error, ...args);
                    } catch (fallbackError) {
                        this.logError({
                            type: 'fallback_component_error',
                            message: fallbackError.message,
                            component: component.name || 'anonymous'
                        });
                    }
                }
                
                // Return safe default or re-throw based on error type
                if (error.name === 'NetworkError' || error.message.includes('fetch')) {
                    return { error: 'Network connection problem. Please check your connection and try again.' };
                }
                
                throw error;
            }
        };
    }

    // User-friendly error messages
    getUserFriendlyMessage(error) {
        const errorMap = {
            'NetworkError': 'Connection problem. Please check your internet and try again.',
            'TypeError': 'Something went wrong. Please refresh the page.',
            'ReferenceError': 'A required component is missing. Please refresh the page.',
            'auth_error': 'Authentication problem. Please log in again.',
            'database_error': 'Database connection issue. Please try again in a moment.',
            'role_error': 'Permission system error. Please refresh the page.',
            'circuit_breaker': 'Service temporarily unavailable. Please try again later.'
        };
        
        return errorMap[error.type] || errorMap[error.name] || 'An unexpected error occurred. Please try again.';
    }

    // Recovery suggestions
    getRecoverySuggestions(error) {
        const suggestions = {
            'auth_error': [
                'Try logging out and logging back in',
                'Clear your browser cache and cookies',
                'Check if your session has expired'
            ],
            'database_error': [
                'Check your internet connection',
                'Try refreshing the page',
                'Wait a moment and try again'
            ],
            'role_error': [
                'Refresh the page to reload your permissions',
                'Log out and log back in',
                'Contact an administrator if the problem persists'
            ],
            'circuit_breaker': [
                'Wait a few minutes before trying again',
                'The system is protecting itself from overload',
                'Try again later when the service recovers'
            ]
        };
        
        return suggestions[error.type] || [
            'Refresh the page',
            'Try again in a few moments',
            'Contact support if the problem continues'
        ];
    }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();

// Enhanced wrapper functions for common operations
export const withAuth = (fn, options = {}) => {
    return errorHandler.withErrorHandling(fn, 'auth', {
        ...options,
        circuitBreaker: true,
        fallback: async () => {
            console.warn('Auth operation failed, attempting recovery...');
            const recovered = await errorHandler.recoverFromAuthError();
            if (recovered) {
                return await fn(); // Retry once after recovery
            }
            throw new Error('Authentication system unavailable');
        }
    });
};

export const withDatabase = (fn, options = {}) => {
    return errorHandler.withErrorHandling(fn, 'database', {
        ...options,
        circuitBreaker: true,
        fallback: async () => {
            console.warn('Database operation failed, attempting recovery...');
            const recovered = await errorHandler.recoverFromDatabaseError();
            if (recovered) {
                return await fn(); // Retry once after recovery
            }
            throw new Error('Database temporarily unavailable');
        }
    });
};

export const withRoles = (fn, options = {}) => {
    return errorHandler.withErrorHandling(fn, 'roles', {
        ...options,
        circuitBreaker: true,
        fallback: async () => {
            console.warn('Role operation failed, attempting recovery...');
            const recovered = await errorHandler.recoverFromRoleError();
            if (recovered) {
                return await fn(); // Retry once after recovery
            }
            throw new Error('Role system temporarily unavailable');
        }
    });
};

// Utility function to wrap existing functions with error handling
export const makeResilient = (fn, context, options = {}) => {
    return errorHandler.createErrorBoundary(fn, options.fallback);
};

// Export error handler for global access
if (typeof window !== 'undefined') {
    window.errorHandler = errorHandler;
}
