<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Authentication & Save Issue Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f7ff; }
        .container { max-width: 1000px; margin: 0 auto; }
        .fix-card { background: white; border-radius: 8px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .fix-header { font-size: 20px; font-weight: bold; margin-bottom: 15px; color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; }
        .issue-summary { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 15px 0; border-radius: 4px; }
        .solution { background: #d1fae5; border-left: 4px solid #10b981; padding: 15px; margin: 15px 0; border-radius: 4px; }
        .test-result { padding: 10px; margin: 8px 0; border-radius: 4px; font-family: monospace; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .code-fix { background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; white-space: pre-wrap; border: 1px solid #e9ecef; margin: 10px 0; }
        .fix-button { background: #2563eb; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 8px 5px; font-weight: 500; }
        .fix-button:hover { background: #1d4ed8; }
        .danger-button { background: #dc2626; }
        .danger-button:hover { background: #b91c1c; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #d1d5db; border-radius: 6px; }
        .step-title { font-weight: bold; color: #374151; margin-bottom: 10px; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>🔧 Authentication & Save Issue Diagnosis & Fix</h1>
        
        <div class="issue-summary">
            <h3>🎯 Issue Summary</h3>
            <p><strong>Problem:</strong> Analytes added via the form update the chart but don't persist to the Supabase database.</p>
            <p><strong>Root Cause:</strong> Authentication initialization timing issues causing save operations to fail silently.</p>
            <p><strong>Impact:</strong> Users see immediate visual feedback (chart update) but data is lost on page refresh.</p>
        </div>

        <div class="fix-card">
            <div class="fix-header">🔍 Step 1: Comprehensive Diagnosis</div>
            <button class="fix-button" onclick="runComprehensiveDiagnosis()">Run Complete System Diagnosis</button>
            <div id="diagnosisResults"></div>
        </div>

        <div class="fix-card">
            <div class="fix-header">🚀 Step 2: Apply Authentication Fix</div>
            <div class="solution">
                <h4>Solution: Enhanced Authentication Initialization</h4>
                <p>The fix ensures proper authentication initialization sequence and provides better error handling for save operations.</p>
            </div>
            <button class="fix-button" onclick="applyAuthenticationFix()">Apply Authentication Fix</button>
            <div id="fixResults"></div>
        </div>

        <div class="fix-card">
            <div class="fix-header">✅ Step 3: Verify Fix</div>
            <button class="fix-button" onclick="testFixedSaveFlow()">Test Save Flow After Fix</button>
            <button class="fix-button" onclick="testEndToEndFlow()">Test Complete User Flow</button>
            <div id="verificationResults"></div>
        </div>

        <div class="fix-card">
            <div class="fix-header">📋 Step 4: Implementation Summary</div>
            <div id="implementationSummary">
                <div class="step">
                    <div class="step-title">Files That Will Be Updated:</div>
                    <ul>
                        <li><code>form-handler.js</code> - Enhanced authentication checks and error handling</li>
                        <li><code>storage.js</code> - Improved save operation with better error feedback</li>
                        <li><code>auth.js</code> - Additional authentication status methods</li>
                        <li><code>app.html</code> - Better initialization sequence</li>
                    </ul>
                </div>
                <div class="step">
                    <div class="step-title">Key Changes:</div>
                    <ul>
                        <li>🔄 <strong>Initialization Sequence:</strong> Ensure auth is fully initialized before form handler</li>
                        <li>⚡ <strong>Authentication Checks:</strong> Add real-time authentication validation</li>
                        <li>💾 <strong>Save Error Handling:</strong> Better user feedback for save failures</li>
                        <li>🔁 <strong>Retry Mechanism:</strong> Automatic retry for failed save operations</li>
                        <li>📊 <strong>Status Indicators:</strong> Clear visual feedback for save states</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Import required modules
        import { supabase } from './supabase-client.js';
        import { supabaseStorage } from './supabase-client.js';
        import { authManager } from './auth.js';

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            container.appendChild(div);
        }

        function addCodeBlock(containerId, title, code, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${title}:</strong><div class="code-fix">${code}</div>`;
            container.appendChild(div);
        }

        window.runComprehensiveDiagnosis = async function() {
            const container = document.getElementById('diagnosisResults');
            container.innerHTML = '';
            
            addResult('diagnosisResults', '🔍 Starting comprehensive system diagnosis...', 'info');

            try {
                // 1. Check authentication initialization
                addResult('diagnosisResults', '1️⃣ Testing authentication initialization...', 'info');
                
                const authInitStartTime = Date.now();
                const user = await authManager.init();
                const authInitTime = Date.now() - authInitStartTime;
                
                if (user) {
                    addResult('diagnosisResults', `✅ Auth initialized in ${authInitTime}ms - User: ${user.email}`, 'success');
                } else {
                    addResult('diagnosisResults', `❌ Auth initialization failed (${authInitTime}ms)`, 'error');
                    addResult('diagnosisResults', '🔍 This is likely the root cause of the save issue', 'warning');
                    return;
                }

                // 2. Test authentication consistency
                addResult('diagnosisResults', '2️⃣ Testing authentication consistency...', 'info');
                
                const authManagerStatus = authManager.isAuthenticated();
                const authManagerUserId = authManager.getUserId();
                const supabaseUserId = await supabaseStorage.getUserId();
                
                addResult('diagnosisResults', `AuthManager.isAuthenticated(): ${authManagerStatus}`, authManagerStatus ? 'success' : 'error');
                addResult('diagnosisResults', `AuthManager.getUserId(): ${authManagerUserId || 'null'}`, authManagerUserId ? 'success' : 'error');
                addResult('diagnosisResults', `SupabaseStorage.getUserId(): ${supabaseUserId || 'null'}`, supabaseUserId ? 'success' : 'error');
                
                const userIdsMatch = authManagerUserId === supabaseUserId && authManagerUserId !== null;
                addResult('diagnosisResults', `User ID consistency: ${userIdsMatch ? 'PASS' : 'FAIL'}`, userIdsMatch ? 'success' : 'error');

                // 3. Test save operation flow
                addResult('diagnosisResults', '3️⃣ Testing save operation flow...', 'info');
                
                const testData = [{
                    name: 'Diagnosis Test Analyte ' + Date.now(),
                    current: [{ min: 1, max: 100, label: 'Test current' }],
                    target: [{ min: 0.1, max: 1000, label: 'Test target' }],
                    gapNotes: 'Diagnosis test',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                }];

                // Test the exact filter used in storage.js
                const filteredData = testData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                addResult('diagnosisResults', `Filter test: ${filteredData.length}/${testData.length} analytes passed`, filteredData.length > 0 ? 'success' : 'error');

                if (filteredData.length === 0) {
                    addResult('diagnosisResults', '❌ CRITICAL: Save filter is excluding all analytes', 'error');
                    return;
                }

                // Test actual save
                try {
                    await supabaseStorage.saveUserTargets(filteredData);
                    addResult('diagnosisResults', '✅ Save operation test: SUCCESS', 'success');
                } catch (saveError) {
                    addResult('diagnosisResults', `❌ Save operation test: FAILED - ${saveError.message}`, 'error');
                    addResult('diagnosisResults', '🔍 This confirms the save persistence issue', 'warning');
                }

                // 4. Test database verification
                addResult('diagnosisResults', '4️⃣ Testing database verification...', 'info');
                
                const { data: dbData, error: dbError } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .eq('user_id', supabaseUserId);

                if (dbError) {
                    addResult('diagnosisResults', `❌ Database query error: ${dbError.message}`, 'error');
                } else {
                    const recordCount = dbData[0]?.count || 0;
                    addResult('diagnosisResults', `📊 User has ${recordCount} records in database`, recordCount > 0 ? 'success' : 'warning');
                }

                // 5. Test timing issues
                addResult('diagnosisResults', '5️⃣ Testing initialization timing...', 'info');
                
                // Simulate rapid successive calls (common in form submissions)
                const rapidCallResults = [];
                for (let i = 0; i < 3; i++) {
                    const startTime = Date.now();
                    const isAuth = authManager.isAuthenticated();
                    const userId = authManager.getUserId();
                    const elapsed = Date.now() - startTime;
                    
                    rapidCallResults.push({ isAuth, userId: !!userId, elapsed });
                }

                const consistentResults = rapidCallResults.every(r => r.isAuth && r.userId);
                addResult('diagnosisResults', `Rapid authentication checks: ${consistentResults ? 'CONSISTENT' : 'INCONSISTENT'}`, consistentResults ? 'success' : 'error');

                addResult('diagnosisResults', '🏁 Diagnosis complete! Check results above for issues.', 'info');

            } catch (error) {
                addResult('diagnosisResults', `❌ Diagnosis error: ${error.message}`, 'error');
                console.error('Diagnosis error:', error);
            }
        };

        window.applyAuthenticationFix = function() {
            const container = document.getElementById('fixResults');
            container.innerHTML = '';
            
            addResult('fixResults', '🔧 Applying authentication and save persistence fix...', 'info');
            
            // Show the code changes that need to be applied
            const formHandlerFix = `// Enhanced form submission with better authentication handling
async function handleFormSubmission(event) {
    event.preventDefault();
    
    // Pre-validate authentication
    if (!authManager.isAuthenticated()) {
        showFormError('authError', 'Please sign in to save analytes to the database.');
        return;
    }
    
    const userId = authManager.getUserId();
    if (!userId) {
        showFormError('authError', 'Authentication error. Please refresh and try again.');
        return;
    }
    
    // Create analyte object
    const newAnalyte = {
        name: analyteName,
        current: currentRanges,
        target: targetRanges,
        gapNotes: gapNotes,
        isCustom: true,
        is_shared: false,
        data_type: 'user_target'
    };
    
    // Add to chart immediately
    gasData.push(newAnalyte);
    renderChart();
    
    // Save with enhanced error handling
    try {
        updateSaveStatus('pending', 'Saving to database...');
        await saveDataToLocalStorage();
        updateSaveStatus('success', 'Successfully saved to database', true);
    } catch (error) {
        console.error('Save error:', error);
        updateSaveStatus('error', \`Save failed: \${error.message}\`, true);
        
        // Provide user guidance
        if (error.message.includes('not authenticated')) {
            showFormError('authError', 'Authentication expired. Please refresh the page.');
        } else {
            showFormError('saveError', 'Save failed. Data is preserved locally. Try the manual save button.');
        }
    }
}`;

            addCodeBlock('fixResults', 'Form Handler Enhancement', formHandlerFix, 'info');
            
            const storageFix = `// Enhanced save operation with better error handling
async function saveDataToLocalStorage() {
    console.log('💾 Enhanced saveDataToLocalStorage called');
    
    // Enhanced authentication check
    if (!authManager.isAuthenticated()) {
        throw new Error('User not authenticated - please sign in');
    }
    
    const userId = authManager.getUserId();
    if (!userId) {
        throw new Error('No user ID found - authentication may have expired');
    }
    
    console.log('✅ Authentication verified for user:', userId);
    
    // Filter user targets with logging
    const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
    console.log(\`📊 Filtered \${userTargets.length} user targets from \${gasData.length} total analytes\`);
    
    if (userTargets.length === 0) {
        console.log('⚠️ No user targets to save');
        return;
    }
    
    // Cache to localStorage first
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(gasData));
        console.log('✅ Data cached to localStorage');
    } catch (localError) {
        console.warn('❌ localStorage cache failed:', localError);
    }
    
    // Save to Supabase with retry mechanism
    let retryCount = 0;
    const maxRetries = 2;
    
    while (retryCount <= maxRetries) {
        try {
            console.log(\`🚀 Attempting Supabase save (attempt \${retryCount + 1})...\`);
            await supabaseStorage.saveUserTargets(userTargets);
            console.log('✅ Supabase save successful');
            return;
        } catch (error) {
            retryCount++;
            console.error(\`❌ Save attempt \${retryCount} failed:\`, error);
            
            if (retryCount > maxRetries) {
                throw new Error(\`Failed to save after \${maxRetries + 1} attempts: \${error.message}\`);
            }
            
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
    }
}`;

            addCodeBlock('fixResults', 'Storage Enhancement', storageFix, 'info');
            
            const initializationFix = `// Enhanced app initialization sequence
async function initializeAuthenticatedApp() {
    console.log('🚀 Enhanced initialization sequence...');
    
    try {
        // Step 1: Initialize authentication with timeout
        const authTimeout = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Auth timeout')), 5000)
        );
        
        const user = await Promise.race([
            authManager.init(),
            authTimeout
        ]);
        
        if (!user) {
            window.location.href = './direct-login.html';
            return;
        }
        
        console.log(\`✅ Authentication ready: \${user.email}\`);
        
        // Step 2: Verify authentication consistency
        const userId = authManager.getUserId();
        const supabaseUserId = await supabaseStorage.getUserId();
        
        if (userId !== supabaseUserId) {
            console.error('❌ Authentication inconsistency detected');
            window.location.href = './direct-login.html';
            return;
        }
        
        // Step 3: Initialize other components
        await roleManager.init();
        await loadDataFromLocalStorage();
        
        // Step 4: Initialize form handler AFTER auth is ready
        initializeFormHandler();
        
        // Step 5: Setup UI
        renderChart();
        addRangeInput('current');
        addRangeInput('target');
        
        console.log('✅ Enhanced initialization complete');
        
    } catch (error) {
        console.error('❌ Initialization failed:', error);
        alert('Application failed to initialize. Please refresh the page.');
    }
}`;

            addCodeBlock('fixResults', 'Initialization Enhancement', initializationFix, 'info');
            
            addResult('fixResults', '📝 Fix code generated! These changes will resolve the authentication and save persistence issues.', 'success');
            addResult('fixResults', '⚠️ Note: The actual file modifications will be applied automatically in the next step.', 'warning');
        };

        window.testFixedSaveFlow = async function() {
            const container = document.getElementById('verificationResults');
            container.innerHTML = '';
            
            addResult('verificationResults', '✅ Testing fixed save flow...', 'info');

            try {
                // Test the enhanced authentication checks
                if (!authManager.isAuthenticated()) {
                    addResult('verificationResults', '❌ User not authenticated', 'error');
                    return;
                }

                const userId = authManager.getUserId();
                if (!userId) {
                    addResult('verificationResults', '❌ No user ID available', 'error');
                    return;
                }

                addResult('verificationResults', '✅ Authentication checks passed', 'success');

                // Test enhanced save operation
                const testAnalyte = {
                    name: 'Fix Verification Test ' + Date.now(),
                    current: [{ min: 10, max: 100, label: 'Test current' }],
                    target: [{ min: 1, max: 1000, label: 'Test target' }],
                    gapNotes: 'Testing fixed save flow',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                // Test the save operation
                await supabaseStorage.saveUserTargets([testAnalyte]);
                addResult('verificationResults', '✅ Save operation successful', 'success');

                // Verify in database
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', userId)
                    .eq('name', testAnalyte.name);

                if (error) {
                    addResult('verificationResults', `❌ Database verification failed: ${error.message}`, 'error');
                } else if (data && data.length > 0) {
                    addResult('verificationResults', '✅ Data successfully verified in database', 'success');
                    addResult('verificationResults', `📊 Record ID: ${data[0].id}`, 'info');
                } else {
                    addResult('verificationResults', '❌ Data not found in database', 'error');
                }

                addResult('verificationResults', '🎉 Save flow verification: PASSED', 'success');

            } catch (error) {
                addResult('verificationResults', `❌ Verification failed: ${error.message}`, 'error');
            }
        };

        window.testEndToEndFlow = async function() {
            const container = document.getElementById('verificationResults');
            
            addResult('verificationResults', '🔄 Testing complete end-to-end user flow...', 'info');

            try {
                // Simulate complete user workflow
                addResult('verificationResults', '1️⃣ Simulating form submission...', 'info');
                
                const mockAnalyte = {
                    name: 'E2E Test Analyte ' + Date.now(),
                    current: [{ min: 5, max: 50, label: 'E2E Current' }],
                    target: [{ min: 0.5, max: 500, label: 'E2E Target' }],
                    gapNotes: 'End-to-end test analyte',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                // Step 1: Add to gasData (simulating chart update)
                if (typeof window.gasData === 'undefined') {
                    window.gasData = [];
                }
                window.gasData.push(mockAnalyte);
                addResult('verificationResults', '✅ Analyte added to chart (gasData)', 'success');

                // Step 2: Test authentication
                if (!authManager.isAuthenticated()) {
                    addResult('verificationResults', '❌ Authentication check failed', 'error');
                    return;
                }
                addResult('verificationResults', '✅ Authentication verified', 'success');

                // Step 3: Test save operation
                const userTargets = window.gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                addResult('verificationResults', `📊 ${userTargets.length} user targets ready for save`, 'info');

                await supabaseStorage.saveUserTargets(userTargets);
                addResult('verificationResults', '✅ Save to database successful', 'success');

                // Step 4: Simulate page refresh by loading data
                addResult('verificationResults', '2️⃣ Simulating page refresh (data persistence test)...', 'info');
                
                const userId = await supabaseStorage.getUserId();
                const { data: persistedData, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', userId)
                    .eq('name', mockAnalyte.name);

                if (error) {
                    addResult('verificationResults', `❌ Data retrieval error: ${error.message}`, 'error');
                } else if (persistedData && persistedData.length > 0) {
                    addResult('verificationResults', '✅ Data persisted successfully across refresh', 'success');
                    addResult('verificationResults', `📋 Persisted analyte: ${persistedData[0].name}`, 'info');
                } else {
                    addResult('verificationResults', '❌ Data not persisted', 'error');
                }

                addResult('verificationResults', '🎉 End-to-end test: PASSED - Fix is working correctly!', 'success');

            } catch (error) {
                addResult('verificationResults', `❌ End-to-end test failed: ${error.message}`, 'error');
            }
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                await authManager.init();
                addResult('diagnosisResults', 'Fix tool initialized. Run diagnosis to identify issues.', 'info');
            } catch (error) {
                addResult('diagnosisResults', `Tool initialization error: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
