// Form handling for adding new analytes

function addRangeInput(type) {
    const containerId = type === 'current' ? 'currentRangesContainer' : 'targetRangesContainer';
    const container = document.getElementById(containerId);
    const group = document.createElement('div');
    group.classList.add('range-input-group');
    group.innerHTML = `
        <input type="number" step="any" placeholder="Min (ppm)" class="md-input range-min">
        <input type="number" step="any" placeholder="Max (ppm)" class="md-input range-max">
        <input type="text" placeholder="Label (optional)" class="md-input range-label">
        <button type="button" class="md-btn md-btn-text md-btn-sm" onclick="this.parentElement.remove()">Remove</button>
    `;
    container.appendChild(group);
}

function showFormError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        setTimeout(() => {
            errorElement.style.display = 'none';
            errorElement.textContent = '';
        }, 3000);
    }
}

// Initialize form event listener
function initializeFormHandler() {
    const form = document.getElementById('addAnalyteForm');
    
    // Prevent multiple event listeners by checking if already initialized
    if (form.hasAttribute('data-initialized')) {
        console.log('⚠️ Form handler already initialized, skipping...');
        return;
    }
    
    form.setAttribute('data-initialized', 'true');
    console.log('🎯 Initializing form handler...');
    
    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        
        // Check authentication status first
        if (typeof authManager !== 'undefined') {
            if (!authManager.isAuthenticated()) {
                showFormError('analyteNameError', "Please login to save data to the database. Redirecting to login page...");
                setTimeout(() => {
                    window.location.href = './simple-auth-login.html';
                }, 2000);
                return;
            }
        }
        
        const analyteNameInput = document.getElementById('analyteName');
        const analyteName = analyteNameInput.value.trim();
        const gapNotes = document.getElementById('gapNotes').value;
        
        if (!analyteName) {
            showFormError('analyteNameError', "Please enter an analyte name.");
            analyteNameInput.focus();
            return;
        }
        
        const currentRanges = [];
        document.querySelectorAll('#currentRangesContainer .range-input-group').forEach(group => {
            const min = group.querySelector('.range-min').value;
            const max = group.querySelector('.range-max').value;
            const label = group.querySelector('.range-label').value;
            if (min && max) { 
                currentRanges.push({ min: parseFloat(min) || null, max: parseFloat(max) || null, label: label || "" });
            }
        });
        
        const targetRanges = [];
        document.querySelectorAll('#targetRangesContainer .range-input-group').forEach(group => {
            const min = group.querySelector('.range-min').value;
            const max = group.querySelector('.range-max').value;
            const label = group.querySelector('.range-label').value;
             if (min && max) { 
                targetRanges.push({ min: parseFloat(min) || null, max: parseFloat(max) || null, label: label || "" });
            }
        });
        
        // Debug: Log data before adding to gasData
        console.log('📝 Adding new analyte:', {
            name: analyteName,
            currentRanges: currentRanges.length,
            targetRanges: targetRanges.length,
            gapNotes: gapNotes ? 'Yes' : 'No',
            gasDataLengthBefore: gasData.length
        });

        // Add new analyte with proper hybrid system properties
        gasData.push({ 
            name: analyteName, 
            current: currentRanges, 
            target: targetRanges, 
            gapNotes: gapNotes,
            isCustom: true,           // Mark as user-created analyte
            is_shared: false,         // Not a shared capability
            data_type: 'user_target'  // Hybrid system data type
        });
        
        // Debug: Log data after adding to gasData
        console.log('✅ Added to gasData array:', {
            gasDataLength: gasData.length,
            newAnalyte: gasData[gasData.length - 1],
            allAnalyteNames: gasData.map(a => a.name)
        });

        try {
            console.log('💾 Starting save operation...');
            updateSaveStatus('pending', 'Auto-saving after adding to chart...');
            
            // Ensure authentication is ready before attempting save
            if (typeof authManager !== 'undefined' && authManager.isAuthenticated()) {
                console.log('🔐 Ensuring authentication is ready before save...');
                await authManager.ensureReady();
                console.log('✅ Authentication confirmed ready, proceeding with save');
            }
            
            // Debug: Check if saveDataToLocalStorage is available
            if (typeof saveDataToLocalStorage === 'undefined') {
                throw new Error('saveDataToLocalStorage function is not available. Check storage.js import.');
            }
            
            console.log('💾 Calling saveDataToLocalStorage function...');
            await saveDataToLocalStorage(); // Save to Supabase/localStorage
            console.log('✅ Save operation completed successfully');
            updateSaveStatus('success', 'Auto-save completed successfully', true);
        } catch (error) {
            console.error('❌ Error saving data:', error);
            
            // More specific error feedback
            if (error.message.includes('saveDataToLocalStorage')) {
                updateSaveStatus('error', `Auto-save failed: Storage functions not loaded. Refresh page and try again.`, true);
            } else if (error.message.includes('auth') || error.message.includes('Auth') || error.message.includes('user')) {
                updateSaveStatus('error', `Auto-save failed: Authentication not ready. Please use manual save button.`, true);
            } else {
                updateSaveStatus('error', `Auto-save failed: ${error.message}. Use manual save button.`, true);
            }
        }
        renderChart(); 
        this.reset(); 
        document.getElementById('currentRangesContainer').innerHTML = ''; 
        document.getElementById('targetRangesContainer').innerHTML = ''; 
        addRangeInput('current'); 
        addRangeInput('target'); 
    });
    
    // Initialize save confirmation features
    initializeSaveConfirmation();
    enhanceFormSubmission();
}

// Save confirmation and authentication status management
let authStatusInterval = null;

function updateAuthenticationStatus() {
    const authStatus = document.getElementById('authStatus');
    const authIndicator = document.getElementById('authIndicator');
    const authStatusText = document.getElementById('authStatusText');
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');
    const saveButtonText = document.getElementById('saveButtonText');
    
    if (typeof authManager !== 'undefined' && authManager.isAuthenticated()) {
        authStatus.className = 'auth-status authenticated';
        authIndicator.textContent = '🔐';
        authStatusText.textContent = 'Authenticated - Database sync enabled';
        confirmSaveBtn.disabled = false;
        saveButtonText.textContent = '💾 Confirm Save to Database';
    } else {
        authStatus.className = 'auth-status guest';
        authIndicator.textContent = '👤';
        authStatusText.textContent = 'Guest Mode - Local storage only';
        confirmSaveBtn.disabled = false;
        saveButtonText.textContent = '💾 Save Locally (Guest Mode)';
    }
}

function updateSaveStatus(status, message, temporary = false) {
    const saveStatus = document.getElementById('saveStatus');
    const saveIndicator = document.getElementById('saveIndicator');
    const saveStatusText = document.getElementById('saveStatusText');
    
    saveStatus.style.display = 'flex';
    saveStatus.className = `save-status ${status}`;
    
    switch (status) {
        case 'pending':
            saveIndicator.textContent = '⏳';
            break;
        case 'success':
            saveIndicator.textContent = '✅';
            break;
        case 'error':
            saveIndicator.textContent = '❌';
            break;
        default:
            saveIndicator.textContent = '💾';
    }
    
    saveStatusText.textContent = message;
    
    if (temporary) {
        setTimeout(() => {
            saveStatus.style.display = 'none';
        }, 5000);
    }
}

function updateSaveButton(state, text = null) {
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');
    const saveButtonText = document.getElementById('saveButtonText');
    
    // Remove all state classes
    confirmSaveBtn.classList.remove('saving', 'success', 'error');
    
    if (state) {
        confirmSaveBtn.classList.add(state);
    }
    
    if (text) {
        saveButtonText.textContent = text;
    }
    
    confirmSaveBtn.disabled = state === 'saving';
}

async function confirmManualSave() {
    // Check authentication first
    if (typeof authManager !== 'undefined' && !authManager.isAuthenticated()) {
        updateSaveButton('error', '❌ Authentication Required');
        updateSaveStatus('error', 'Please login to save data. Redirecting to login page...', true);
        setTimeout(() => {
            window.location.href = './simple-auth-login.html';
        }, 2000);
        return;
    }
    
    updateSaveButton('saving', '⏳ Saving...');
    updateSaveStatus('pending', 'Attempting to save data...');
    
    try {
        if (typeof authManager !== 'undefined' && authManager.isAuthenticated()) {
            // Authenticated save
            console.log('🔐 Performing authenticated save to Supabase...');
            console.log('🔐 Ensuring authentication is ready before save...');
            await authManager.ensureReady();
            console.log('✅ Authentication confirmed ready, proceeding with save');
            await saveDataToLocalStorage();
            updateSaveButton('success', '✅ Saved to Database');
            updateSaveStatus('success', 'Data successfully saved to database and local cache', true);
        } else {
            // Guest mode save
            console.log('👤 Performing guest mode save to localStorage...');
            localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
            updateSaveButton('success', '✅ Saved Locally');
            updateSaveStatus('success', 'Data saved locally (will be lost on refresh)', true);
        }
        
        // Reset button after delay
        setTimeout(() => {
            updateAuthenticationStatus(); // Reset button text and state
        }, 3000);
        
    } catch (error) {
        console.error('❌ Save operation failed:', error);
        updateSaveButton('error', '❌ Save Failed');
        updateSaveStatus('error', `Save failed: ${error.message}`, true);
        
        // Reset button after delay
        setTimeout(() => {
            updateAuthenticationStatus();
        }, 5000);
    }
}

function initializeSaveConfirmation() {
    // Set up manual save button
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');
    if (confirmSaveBtn) {
        confirmSaveBtn.addEventListener('click', confirmManualSave);
    }
    
    // Update authentication status immediately and periodically
    updateAuthenticationStatus();
    
    // Check auth status every 5 seconds
    if (authStatusInterval) {
        clearInterval(authStatusInterval);
    }
    authStatusInterval = setInterval(updateAuthenticationStatus, 5000);
}

// Enhanced form submission with better save feedback
function enhanceFormSubmission() {
    const form = document.getElementById('addAnalyteForm');
    const addAnalyteBtn = document.getElementById('addAnalyteBtn');
    
    if (form && addAnalyteBtn) {
        // Update button text to be more clear about what it does
        addAnalyteBtn.textContent = '📊 Add to Chart (Local)';
        
        // Add tooltip to explain the difference
        addAnalyteBtn.title = 'Adds analyte to chart immediately. Use "Confirm Save" button to persist to database.';
    }
}
