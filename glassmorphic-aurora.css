/* 
   Elevated SaaS UI Design System
   Alpha Gas Solution - Professional Interface
*/

/* ===== ROOT VARIABLES ===== */
:root {
    /* SaaS Color Palette */
    --saas-bg: #F7F8FC;             /* Light gray background */
    --saas-surface: #FFFFFF;        /* Pure white surfaces */
    --saas-text-primary: #1A202C;   /* Dark charcoal for primary text */
    --saas-text-secondary: #6B7280; /* Medium gray for secondary text */
    --saas-accent: #E54D2E;         /* Red accent color */
    
    /* Extended Color System */
    --saas-border: #E2E8F0;         /* Light border color */
    --saas-border-hover: #CBD5E0;   /* Darker border on hover */
    --saas-success: #059669;        /* Green for success states */
    --saas-warning: #D97706;        /* Orange for warnings */
    --saas-error: #DC2626;          /* Red for errors */
    --saas-info: #2563EB;           /* Blue for info */
    
    /* Background Variations */
    --saas-bg-subtle: #F8FAFC;      /* Slightly darker background */
    --saas-bg-muted: #F1F5F9;       /* More noticeable background */
    
    /* Text Color Variations */
    --saas-text-muted: #9CA3AF;     /* Very light gray text */
    --saas-text-disabled: #D1D5DB;  /* Disabled text color */
    
    /* Shadow System */
    --saas-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --saas-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --saas-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --saas-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --saas-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Consolas', 'Liberation Mono', monospace;
    
    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    
    /* Border Radius */
    --radius-sm: 0.375rem;  /* 6px */
    --radius: 0.5rem;       /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    background-color: var(--saas-bg);
    color: var(--saas-text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    min-height: 100vh;
}

/* ===== TYPOGRAPHY ===== */
.saas-heading-xl {
    font-size: 2.25rem;      /* 36px */
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: var(--saas-text-primary);
}

.saas-heading-lg {
    font-size: 1.875rem;     /* 30px */
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.025em;
    color: var(--saas-text-primary);
}

.saas-heading-md {
    font-size: 1.5rem;       /* 24px */
    font-weight: 600;
    line-height: 1.3;
    color: var(--saas-text-primary);
}

.saas-heading-sm {
    font-size: 1.25rem;      /* 20px */
    font-weight: 600;
    line-height: 1.4;
    color: var(--saas-text-primary);
}

.saas-text-lg {
    font-size: 1.125rem;     /* 18px */
    line-height: 1.6;
    color: var(--saas-text-primary);
}

.saas-text-base {
    font-size: 1rem;         /* 16px */
    line-height: 1.6;
    color: var(--saas-text-primary);
}

.saas-text-sm {
    font-size: 0.875rem;     /* 14px */
    line-height: 1.5;
    color: var(--saas-text-secondary);
}

.saas-text-xs {
    font-size: 0.75rem;      /* 12px */
    line-height: 1.4;
    color: var(--saas-text-secondary);
}

/* Text Color Utilities */
.saas-text-primary { color: var(--saas-text-primary); }
.saas-text-secondary { color: var(--saas-text-secondary); }
.saas-text-muted { color: var(--saas-text-muted); }
.saas-text-accent { color: var(--saas-accent); }
.saas-text-success { color: var(--saas-success); }
.saas-text-warning { color: var(--saas-warning); }
.saas-text-error { color: var(--saas-error); }
.saas-text-info { color: var(--saas-info); }

/* Font Weight Utilities */
.saas-font-light { font-weight: 300; }
.saas-font-normal { font-weight: 400; }
.saas-font-medium { font-weight: 500; }
.saas-font-semibold { font-weight: 600; }
.saas-font-bold { font-weight: 700; }
}

body {
    font-family: var(--font-primary);
    background: var(--aurora-bg-accent);
    background-attachment: fixed;
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: 14px;
}

/* Aurora Background Animation */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--aurora-bg-primary);
    opacity: 0.9;
    z-index: -2;
    animation: auroraShift 15s ease-in-out infinite alternate;
}

@keyframes auroraShift {
    0% {
        background: var(--aurora-bg-primary);
        transform: translateX(0) translateY(0) scale(1);
    }
    50% {
        background: var(--aurora-bg-secondary);
        transform: translateX(-20px) translateY(-20px) scale(1.05);
    }
    100% {
        background: linear-gradient(225deg, #10b981 0%, #06b6d4 50%, #8b5cf6 100%);
        transform: translateX(20px) translateY(20px) scale(1.02);
    }
}

/* ===== GLASSMORPHIC COMPONENTS ===== */

/* Main Glass Container */
.glass-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
}

.glass-container:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-strong);
    transform: translateY(-2px);
}

/* Glass Cards */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--glass-shadow);
    padding: 24px;
    transition: all 0.3s ease;
}

.glass-card:hover {
    background: var(--glass-bg-light);
    transform: translateY(-1px);
}

.glass-card-compact {
    padding: 16px;
    border-radius: 12px;
}

/* Glass Panels */
.glass-panel {
    background: var(--glass-bg-strong);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow-strong);
    padding: 32px;
}

/* ===== AURORA BUTTONS ===== */
.aurora-btn {
    position: relative;
    background: linear-gradient(135deg, var(--aurora-primary) 0%, var(--aurora-secondary) 100%);
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--aurora-glow);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.aurora-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.aurora-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--aurora-glow-strong);
}

.aurora-btn:hover::before {
    left: 100%;
}

.aurora-btn:active {
    transform: translateY(0);
}

/* Button Variants */
.aurora-btn-success {
    background: linear-gradient(135deg, var(--aurora-success) 0%, var(--aurora-accent) 100%);
    box-shadow: var(--success-glow);
}

.aurora-btn-warning {
    background: linear-gradient(135deg, var(--aurora-warning) 0%, #fb923c 100%);
    box-shadow: var(--warning-glow);
}

.aurora-btn-error {
    background: linear-gradient(135deg, var(--aurora-error) 0%, #f87171 100%);
    box-shadow: var(--error-glow);
}

.aurora-btn-ghost {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow: none;
}

.aurora-btn-ghost:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow);
}

/* ===== AURORA INPUTS ===== */
.aurora-input {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 14px 16px;
    color: white;
    font-size: 14px;
    width: 100%;
    transition: all 0.3s ease;
}

.aurora-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.aurora-input:focus {
    outline: none;
    border-color: var(--aurora-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background: var(--glass-bg-light);
}

.aurora-textarea {
    min-height: 100px;
    resize: vertical;
}

.aurora-select {
    background: var(--glass-bg) url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23ffffff'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E") no-repeat right 12px center;
    background-size: 20px;
    appearance: none;
    cursor: pointer;
}

/* ===== AURORA NAVIGATION ===== */
.aurora-nav {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: 16px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.aurora-nav-brand {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--aurora-primary), var(--aurora-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.aurora-nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.aurora-nav-link:hover {
    color: white;
    background: var(--glass-bg);
    transform: translateY(-1px);
}

.aurora-nav-link.active {
    color: white;
    background: var(--glass-bg-light);
    box-shadow: var(--aurora-glow);
}

/* ===== AURORA TAB NAVIGATION ===== */
.aurora-tabs {
    display: flex;
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.aurora-tab {
    flex: 1;
    padding: 12px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    border: none;
}

.aurora-tab:hover {
    background: var(--glass-bg-light);
    color: rgba(255, 255, 255, 0.9);
}

.aurora-tab.active {
    background: var(--aurora-bg-primary);
    color: white;
    box-shadow: var(--aurora-glow);
}

.aurora-tab.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--aurora-accent), var(--aurora-success));
}

.aurora-tab span {
    position: relative;
    z-index: 1;
}

/* ===== AURORA TAB CONTENT ===== */
.aurora-tab-content {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.aurora-tab-content.active {
    display: block;
}

/* ===== AURORA STATUS INDICATORS ===== */
.aurora-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-status-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #10b981;
    box-shadow: var(--success-glow);
}

.aurora-status-warning {
    background: rgba(245, 158, 11, 0.2);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #f59e0b;
    box-shadow: var(--warning-glow);
}

.aurora-status-error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    box-shadow: var(--error-glow);
}

.aurora-status-info {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(99, 102, 241, 0.1));
    border: 1px solid rgba(99, 102, 241, 0.3);
    color: #6366f1;
    box-shadow: var(--aurora-glow);
}

/* ===== AURORA TABLES ===== */
.aurora-table-container {
    overflow-x: auto;
    border-radius: 12px;
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
}

.aurora-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.aurora-table th {
    padding: 16px 20px;
    text-align: left;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: var(--glass-bg-light);
    border-bottom: 1px solid var(--glass-border);
}

.aurora-table th:first-child {
    border-top-left-radius: 12px;
}

.aurora-table th:last-child {
    border-top-right-radius: 12px;
}

.aurora-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.aurora-table tbody tr:hover {
    background: var(--glass-bg-light);
}

.aurora-table tbody tr:last-child td {
    border-bottom: none;
}

.aurora-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 12px;
}

.aurora-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 12px;
}

/* ===== AURORA LOADING SPINNER ===== */
.aurora-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--glass-border);
    border-top: 3px solid var(--aurora-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== AURORA STATUS BADGES ===== */
.aurora-status-pending {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(245, 158, 11, 0.1));
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: var(--aurora-warning);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-status-approved {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: var(--aurora-success);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-status-rejected {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: var(--aurora-error);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-status-suspended {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.6);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== AURORA ROLE BADGES ===== */
.aurora-role-admin {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(139, 92, 246, 0.1));
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: var(--aurora-secondary);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-role-user {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(99, 102, 241, 0.1));
    border: 1px solid rgba(99, 102, 241, 0.3);
    color: var(--aurora-primary);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-role-guest {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(6, 182, 212, 0.1));
    border: 1px solid rgba(6, 182, 212, 0.3);
    color: var(--aurora-accent);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-role-pending {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.6);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== AURORA SCROLLBAR ===== */
.aurora-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.aurora-scrollbar::-webkit-scrollbar-track {
    background: var(--glass-bg);
    border-radius: 3px;
}

.aurora-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--aurora-primary), var(--aurora-secondary));
    border-radius: 3px;
}

.aurora-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--aurora-secondary), var(--aurora-accent));
}

/* ===== GRID AND LAYOUT UTILITIES ===== */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

@media (min-width: 768px) {
    .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .lg\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }

.max-w-md { max-width: 28rem; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }

.min-h-screen { min-height: 100vh; }

.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.hidden { display: none; }
.relative { position: relative; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-20 { top: 5rem; }
.z-50 { z-index: 50; }

.text-sm { font-size: 0.875rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-6xl { font-size: 3.75rem; }

.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }

.uppercase { text-transform: uppercase; }
.tracking-wider { letter-spacing: 0.05em; }

.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.max-h-64 { max-height: 16rem; }

/* ===== RESPONSIVE DESIGN ===== */
