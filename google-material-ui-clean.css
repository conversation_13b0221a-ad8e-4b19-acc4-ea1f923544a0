/* Google Material Design 3 - Clean White Aesthetic */
/* Authentic Google Material Design with Clean White Background */

:root {
  /* Google Brand Colors - Official */
  --google-blue: #4285f4;
  --google-red: #ea4335;
  --google-yellow: #fbbc04;
  --google-green: #34a853;
  
  /* Material Design 3 Color System - Google Style */
  --md-primary: #4285f4;
  --md-primary-container: #e8f0fe;
  --md-on-primary: #ffffff;
  --md-on-primary-container: #1a73e8;
  
  --md-secondary: #34a853;
  --md-secondary-container: #e6f4ea;
  --md-on-secondary: #ffffff;
  --md-on-secondary-container: #137333;
  
  --md-tertiary: #ea4335;
  --md-tertiary-container: #fce8e6;
  --md-on-tertiary: #ffffff;
  --md-on-tertiary-container: #d93025;
  
  --md-error: #ea4335;
  --md-error-container: #fce8e6;
  --md-on-error: #ffffff;
  --md-on-error-container: #d93025;
  
  --md-success: #34a853;
  --md-success-container: #e6f4ea;
  --md-on-success: #ffffff;
  --md-on-success-container: #137333;
  
  --md-warning: #fbbc04;
  --md-warning-container: #fef7e0;
  --md-on-warning: #000000;
  --md-on-warning-container: #ea8600;
  
  /* Clean White Background System */
  --md-surface: #ffffff;
  --md-surface-container: #f8f9fa;
  --md-surface-container-low: #f1f3f4;
  --md-surface-container-high: #e8eaed;
  --md-surface-variant: #f8f9fa;
  --md-on-surface: #202124;
  --md-on-surface-variant: #5f6368;
  
  --md-background: #ffffff;
  --md-on-background: #202124;
  
  --md-outline: #dadce0;
  --md-outline-variant: #e8eaed;
  --md-shadow: rgba(60, 64, 67, 0.3);
  
  /* Typography Scale */
  --md-display-large: 57px;
  --md-headline-large: 32px;
  --md-headline-medium: 28px;
  --md-headline-small: 24px;
  --md-title-large: 22px;
  --md-title-medium: 16px;
  --md-body-large: 16px;
  --md-body-medium: 14px;
  --md-label-large: 14px;
  --md-label-medium: 12px;
  --md-label-small: 11px;
  
  /* Spacing */
  --md-space-xs: 4px;
  --md-space-sm: 8px;
  --md-space-md: 16px;
  --md-space-lg: 24px;
  --md-space-xl: 32px;
  
  /* Border Radius */
  --md-radius-xs: 4px;
  --md-radius-sm: 8px;
  --md-radius-md: 12px;
  --md-radius-lg: 16px;
  --md-radius-xl: 28px;
  --md-radius-full: 50%;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: var(--md-background);
  color: var(--md-on-background);
  line-height: 1.5;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Google Material Design Cards */
.md-card {
  background-color: var(--md-surface);
  border-radius: var(--md-radius-md);
  box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
  padding: var(--md-space-lg);
  margin-bottom: var(--md-space-md);
  transition: box-shadow 0.28s cubic-bezier(0.4, 0.0, 0.2, 1);
  border: 1px solid var(--md-outline-variant);
}

.md-card:hover {
  box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15), 0 8px 24px 4px rgba(60, 64, 67, 0.15);
}

.md-card-elevated {
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
}

.md-card-filled {
  background-color: var(--md-surface-container);
}

/* Google Material Design Buttons */
.md-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-space-sm);
  padding: 10px 24px;
  border-radius: var(--md-radius-xl);
  border: none;
  font-family: 'Roboto', sans-serif;
  font-size: var(--md-label-large);
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.4, 0.0, 0.2, 1);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  min-height: 40px;
}

.md-btn:disabled {
  opacity: 0.38;
  cursor: not-allowed;
}

/* Filled Button (Primary) - Google Blue */
.md-btn-filled {
  background-color: var(--md-primary);
  color: var(--md-on-primary);
  box-shadow: 0 1px 3px rgba(66, 133, 244, 0.3);
}

.md-btn-filled:hover:not(:disabled) {
  background-color: #1976d2;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.4);
  transform: translateY(-1px);
}

/* Outlined Button */
.md-btn-outlined {
  background-color: transparent;
  color: var(--md-primary);
  border: 1px solid var(--md-outline);
}

.md-btn-outlined:hover:not(:disabled) {
  background-color: rgba(66, 133, 244, 0.04);
  border-color: var(--md-primary);
}

/* Text Button */
.md-btn-text {
  background-color: transparent;
  color: var(--md-primary);
  padding: 10px 12px;
}

.md-btn-text:hover:not(:disabled) {
  background-color: rgba(66, 133, 244, 0.04);
}

/* Button Sizes */
.md-btn-lg {
  padding: 16px 32px;
  min-height: 56px;
  font-size: var(--md-body-large);
}

.md-btn-sm {
  padding: 8px 16px;
  min-height: 32px;
  font-size: var(--md-label-medium);
}

/* Form Fields - Google Style */
.md-form-group {
  margin-bottom: var(--md-space-lg);
}

.md-label {
  display: block;
  font-size: var(--md-body-medium);
  font-weight: 500;
  color: var(--md-on-surface-variant);
  margin-bottom: var(--md-space-xs);
}

.md-input {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--md-outline);
  border-radius: var(--md-radius-xs);
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  font-size: var(--md-body-large);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  font-family: 'Roboto', sans-serif;
}

.md-input:focus {
  outline: none;
  border-color: var(--md-primary);
  border-width: 2px;
  padding: 15px; /* Adjust padding to maintain size */
  box-shadow: 0 0 0 1px var(--md-primary);
}

.md-input::placeholder {
  color: var(--md-on-surface-variant);
}

/* Google Top App Bar */
.md-top-app-bar {
  background-color: var(--md-surface);
  box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3);
  padding: 0 var(--md-space-md);
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--md-outline-variant);
}

.md-top-app-bar-title {
  font-size: var(--md-title-large);
  font-weight: 500;
  color: var(--md-on-surface);
  display: flex;
  align-items: center;
  gap: var(--md-space-sm);
}

.md-top-app-bar-actions {
  display: flex;
  align-items: center;
  gap: var(--md-space-sm);
}

/* Tabs - Google Style */
.md-tabs {
  display: flex;
  border-bottom: 1px solid var(--md-outline-variant);
  margin-bottom: var(--md-space-lg);
  background-color: var(--md-surface);
}

.md-tab {
  background: none;
  border: none;
  padding: var(--md-space-md) var(--md-space-lg);
  cursor: pointer;
  font-size: var(--md-label-large);
  font-weight: 500;
  color: var(--md-on-surface-variant);
  position: relative;
  transition: color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  font-family: 'Roboto', sans-serif;
}

.md-tab.active {
  color: var(--md-primary);
}

.md-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--md-primary);
  border-radius: 1.5px 1.5px 0 0;
}

.md-tab:hover:not(.active) {
  background-color: rgba(66, 133, 244, 0.04);
}

/* Tab Content */
.md-tab-content {
  display: none;
}

.md-tab-content.active {
  display: block;
}

/* Chips - Google Style */
.md-chip {
  display: inline-flex;
  align-items: center;
  padding: 6px 16px;
  background-color: var(--md-surface-variant);
  color: var(--md-on-surface-variant);
  border-radius: var(--md-radius-sm);
  font-size: var(--md-label-large);
  gap: var(--md-space-xs);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  border: 1px solid var(--md-outline-variant);
}

.md-chip:hover {
  box-shadow: 0 1px 3px rgba(60, 64, 67, 0.3);
}

.md-chip-selected {
  background-color: var(--md-secondary-container);
  color: var(--md-on-secondary-container);
}

/* Typography */
.md-headline-large {
  font-size: var(--md-headline-large);
  font-weight: 400;
  line-height: 1.25;
  color: var(--md-on-surface);
}

.md-headline-medium {
  font-size: var(--md-headline-medium);
  font-weight: 400;
  line-height: 1.29;
  color: var(--md-on-surface);
}

.md-headline-small {
  font-size: var(--md-headline-small);
  font-weight: 400;
  line-height: 1.33;
  color: var(--md-on-surface);
}

.md-title-large {
  font-size: var(--md-title-large);
  font-weight: 500;
  line-height: 1.27;
  color: var(--md-on-surface);
}

.md-body-large {
  font-size: var(--md-body-large);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-on-surface);
}

.md-body-medium {
  font-size: var(--md-body-medium);
  font-weight: 400;
  line-height: 1.43;
  color: var(--md-on-surface);
}

.md-label-large {
  font-size: var(--md-label-large);
  font-weight: 500;
  line-height: 1.43;
}

/* Layout Utilities */
.md-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--md-space-md);
}

/* Flexbox Utilities */
.md-flex { display: flex; }
.md-flex-col { flex-direction: column; }
.md-items-center { align-items: center; }
.md-justify-center { justify-content: center; }
.md-justify-between { justify-content: space-between; }
.md-flex-1 { flex: 1; }

/* Width Utilities */
.md-w-full { width: 100%; }
.md-max-w-sm { max-width: 384px; }
.md-max-w-md { max-width: 448px; }
.md-max-w-lg { max-width: 512px; }

/* Display Utilities */
.md-hidden { display: none; }
.md-block { display: block; }

/* Text Utilities */
.md-text-center { text-align: center; }
.md-text-primary { color: var(--md-primary); }
.md-text-secondary { color: var(--md-on-surface-variant); }
.md-text-error { color: var(--md-error); }

/* Spacing Utilities */
.md-mb-xs { margin-bottom: var(--md-space-xs); }
.md-mb-sm { margin-bottom: var(--md-space-sm); }
.md-mb-md { margin-bottom: var(--md-space-md); }
.md-mb-lg { margin-bottom: var(--md-space-lg); }
.md-mb-xl { margin-bottom: var(--md-space-xl); }

/* Elevation */
.md-elevation-1 { box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15); }
.md-elevation-2 { box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15); }
.md-elevation-3 { box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15), 0 8px 24px 4px rgba(60, 64, 67, 0.15); }

/* Chart Styles - Google Material Design */
.md-chart-container {
  background-color: var(--md-surface);
  border-radius: var(--md-radius-md);
  box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
  padding: var(--md-space-lg);
  margin-bottom: var(--md-space-md);
  border: 1px solid var(--md-outline-variant);
}

.md-gas-row {
  display: flex;
  align-items: center;
  padding: var(--md-space-sm) 0;
  border-bottom: 1px solid var(--md-outline-variant);
  transition: background-color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.md-gas-row:hover {
  background-color: var(--md-surface-variant);
  border-radius: var(--md-radius-xs);
}

.md-gas-name {
  min-width: 200px;
  font-weight: 500;
  color: var(--md-on-surface);
  padding-right: var(--md-space-md);
}

.md-bar-area {
  flex: 1;
  height: 32px;
  position: relative;
  background-color: var(--md-surface-variant);
  border-radius: var(--md-radius-lg);
  overflow: hidden;
  border: 1px solid var(--md-outline-variant);
}

.md-target-bar {
  position: absolute;
  height: 100%;
  background: linear-gradient(135deg, var(--google-blue) 0%, var(--google-green) 100%);
  border-radius: var(--md-radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

.md-current-bar {
  position: absolute;
  height: 100%;
  background: linear-gradient(135deg, var(--google-red) 0%, #d93025 100%);
  border-radius: var(--md-radius-lg);
  box-shadow: 0 2px 8px rgba(234, 67, 53, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.md-tooltip {
  visibility: hidden;
  width: auto;
  min-width: 120px;
  background-color: rgba(60, 64, 67, 0.9);
  color: white;
  text-align: center;
  border-radius: var(--md-radius-sm);
  padding: var(--md-space-sm);
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  font-size: var(--md-label-small);
  white-space: nowrap;
  box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
}

.md-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(60, 64, 67, 0.9) transparent transparent transparent;
}

.md-target-bar:hover .md-tooltip,
.md-current-bar:hover .md-tooltip {
  visibility: visible;
  opacity: 1;
}

/* Gap Notes */
.md-gap-notes {
  width: 280px;
  padding-left: var(--md-space-md);
  flex-shrink: 0;
}

.md-gap-notes-text {
  font-size: var(--md-body-small);
  color: var(--md-on-surface-variant);
  line-height: 1.4;
  padding: var(--md-space-sm);
  background-color: var(--md-surface-variant);
  border-radius: var(--md-radius-sm);
  border-left: 3px solid var(--google-blue);
}

/* Animations */
.md-fade-in {
  animation: md-fadeIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes md-fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.md-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-space-sm);
  padding: var(--md-space-xl);
  color: var(--md-on-surface-variant);
}

.md-loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--md-outline-variant);
  border-top: 3px solid var(--md-primary);
  border-radius: 50%;
  animation: md-spin 1s linear infinite;
}

@keyframes md-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .md-container {
    padding: 0 var(--md-space-sm);
  }
  
  .md-top-app-bar {
    padding: 0 var(--md-space-sm);
  }
  
  .md-gas-name {
    min-width: 120px;
    font-size: var(--md-body-small);
  }
  
  .md-bar-area {
    height: 24px;
  }
}
