/* Modern Google Material Design 3 - Professional UI Framework */
/* Refined Google Material Design with Contemporary Aesthetics */

/* ============================================
   MODERN MATERIAL DESIGN 3 COLOR SYSTEM
   ============================================ */

:root {
  /* Enhanced Google Brand Colors */
  --google-blue: #1a73e8;
  --google-blue-light: #4285f4;
  --google-blue-dark: #1557b0;
  --google-red: #d93025;
  --google-red-light: #ea4335;
  --google-yellow: #f9ab00;
  --google-yellow-light: #fbbc04;
  --google-green: #137333;
  --google-green-light: #34a853;

  /* Sophisticated Primary Colors */
  --md-primary: #1a73e8;
  --md-primary-light: #4285f4;
  --md-primary-dark: #1557b0;
  --md-primary-container: #e8f0fe;
  --md-primary-container-light: #f1f8ff;
  --md-on-primary: #ffffff;
  --md-on-primary-container: #1a73e8;

  /* Refined Secondary Colors */
  --md-secondary: #5f6368;
  --md-secondary-light: #80868b;
  --md-secondary-container: #f8f9fa;
  --md-secondary-container-light: #fafbfc;
  --md-on-secondary: #ffffff;
  --md-on-secondary-container: #202124;

  /* Enhanced Semantic Colors */
  --md-success: #137333;
  --md-success-light: #34a853;
  --md-success-container: #e6f4ea;
  --md-success-container-light: #f0f9f2;
  --md-on-success: #ffffff;
  --md-on-success-container: #137333;

  --md-error: #d93025;
  --md-error-light: #ea4335;
  --md-error-container: #fce8e6;
  --md-error-container-light: #fef1f0;
  --md-on-error: #ffffff;
  --md-on-error-container: #d93025;

  --md-warning: #f9ab00;
  --md-warning-light: #fbbc04;
  --md-warning-container: #fef7e0;
  --md-warning-container-light: #fffbf0;
  --md-on-warning: #000000;
  --md-on-warning-container: #ea8600;

  /* Modern Surface System */
  --md-background: #ffffff;
  --md-background-alt: #fafbfc;
  --md-on-background: #202124;
  --md-surface: #ffffff;
  --md-surface-dim: #f8f9fa;
  --md-surface-bright: #ffffff;
  --md-surface-container-lowest: #ffffff;
  --md-surface-container-low: #f8f9fa;
  --md-surface-container: #f1f3f4;
  --md-surface-container-high: #e8eaed;
  --md-surface-container-highest: #dadce0;
  --md-surface-tint: rgba(26, 115, 232, 0.05);
  --md-on-surface: #202124;
  --md-on-surface-variant: #5f6368;

  /* Refined Text Colors */
  --md-text-primary: #202124;
  --md-text-secondary: #5f6368;
  --md-text-tertiary: #80868b;
  --md-text-disabled: #9aa0a6;
  --md-text-on-primary: #ffffff;
  --md-text-on-secondary: #ffffff;

  /* Modern Border System */
  --md-outline: #dadce0;
  --md-outline-light: #e8eaed;
  --md-outline-variant: #f1f3f4;
  --md-outline-focus: #1a73e8;

  /* Enhanced Elevation & Shadow System */
  --md-elevation-0: none;
  --md-elevation-1: 0 1px 3px 0 rgba(60, 64, 67, 0.12), 0 1px 2px 0 rgba(60, 64, 67, 0.24);
  --md-elevation-2: 0 2px 6px 2px rgba(60, 64, 67, 0.15), 0 1px 2px 0 rgba(60, 64, 67, 0.3);
  --md-elevation-3: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px 0 rgba(60, 64, 67, 0.3);
  --md-elevation-4: 0 6px 10px 4px rgba(60, 64, 67, 0.15), 0 2px 3px 0 rgba(60, 64, 67, 0.3);
  --md-elevation-5: 0 8px 12px 6px rgba(60, 64, 67, 0.15), 0 4px 4px 0 rgba(60, 64, 67, 0.3);
  --md-elevation-hover: 0 2px 8px 0 rgba(60, 64, 67, 0.2);
  --md-elevation-focus: 0 0 0 3px rgba(26, 115, 232, 0.2);

  /* Refined Spacing Scale */
  --md-spacing-xs: 4px;
  --md-spacing-sm: 8px;
  --md-spacing-md: 16px;
  --md-spacing-lg: 24px;
  --md-spacing-xl: 32px;
  --md-spacing-xxl: 48px;
  --md-spacing-xxxl: 64px;

  /* Modern Border Radius System */
  --md-radius-none: 0;
  --md-radius-xs: 4px;
  --md-radius-sm: 6px;
  --md-radius-md: 8px;
  --md-radius-lg: 12px;
  --md-radius-xl: 16px;
  --md-radius-xxl: 24px;
  --md-radius-full: 9999px;

  /* Animation & Transition System */
  --md-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-motion-easing-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-motion-easing-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-motion-duration-short: 150ms;
  --md-motion-duration-medium: 250ms;
  --md-motion-duration-long: 400ms;
}

/* ============================================
   MODERN GLOBAL STYLES
   ============================================ */

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
}

body {
  font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--md-background);
  color: var(--md-on-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1;
  overflow-x: hidden;
}

/* Enhanced focus management */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--md-outline-focus);
  outline-offset: 2px;
}

/* ============================================
   MODERN TYPOGRAPHY SCALE
   ============================================ */

.md-display-large {
  font-size: clamp(40px, 8vw, 57px);
  line-height: 1.12;
  font-weight: 400;
  letter-spacing: -0.25px;
  font-family: 'Google Sans Display', 'Roboto', sans-serif;
}

.md-display-medium {
  font-size: clamp(32px, 6vw, 45px);
  line-height: 1.16;
  font-weight: 400;
  letter-spacing: 0;
  font-family: 'Google Sans Display', 'Roboto', sans-serif;
}

.md-display-small {
  font-size: clamp(28px, 5vw, 36px);
  line-height: 1.22;
  font-weight: 400;
  letter-spacing: 0;
  font-family: 'Google Sans Display', 'Roboto', sans-serif;
}

.md-headline-large {
  font-size: clamp(24px, 4vw, 32px);
  line-height: 1.25;
  font-weight: 500;
  letter-spacing: 0;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-headline-medium {
  font-size: clamp(20px, 3.5vw, 28px);
  line-height: 1.29;
  font-weight: 500;
  letter-spacing: 0;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-headline-small {
  font-size: clamp(18px, 3vw, 24px);
  line-height: 1.33;
  font-weight: 500;
  letter-spacing: 0;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-title-large {
  font-size: 22px;
  line-height: 1.27;
  font-weight: 500;
  letter-spacing: 0;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-title-medium {
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.15px;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-title-small {
  font-size: 14px;
  line-height: 1.43;
  font-weight: 500;
  letter-spacing: 0.1px;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-label-large {
  font-size: 14px;
  line-height: 1.43;
  font-weight: 500;
  letter-spacing: 0.1px;
  text-transform: none;
}

.md-label-medium {
  font-size: 12px;
  line-height: 1.33;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: none;
}

.md-label-small {
  font-size: 11px;
  line-height: 1.45;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: none;
}

.md-body-large {
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  letter-spacing: 0.15px;
}

.md-body-medium {
  font-size: 14px;
  line-height: 1.43;
  font-weight: 400;
  letter-spacing: 0.25px;
}

.md-body-small {
  font-size: 12px;
  line-height: 1.33;
  font-weight: 400;
  letter-spacing: 0.4px;
}

/* ============================================
   LAYOUT & UTILITY CLASSES
   ============================================ */

.md-flex { display: flex; }
.md-inline-flex { display: inline-flex; }
.md-grid { display: grid; }
.md-hidden { display: none; }
.md-block { display: block; }
.md-inline-block { display: inline-block; }

/* Container */
.md-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--md-spacing-lg);
}

@media (max-width: 768px) {
  .md-container {
    padding: 0 var(--md-spacing-md);
  }
}

.md-flex-col { flex-direction: column; }
.md-flex-row { flex-direction: row; }
.md-items-center { align-items: center; }
.md-items-start { align-items: flex-start; }
.md-items-end { align-items: flex-end; }
.md-justify-center { justify-content: center; }
.md-justify-between { justify-content: space-between; }
.md-justify-around { justify-content: space-around; }
.md-justify-start { justify-content: flex-start; }
.md-justify-end { justify-content: flex-end; }

.md-flex-1 { flex: 1; }
.md-flex-grow { flex-grow: 1; }
.md-flex-shrink { flex-shrink: 1; }
.md-flex-none { flex: none; }

.md-w-full { width: 100%; }
.md-h-full { height: 100%; }
.md-max-w-xs { max-width: 320px; }
.md-max-w-sm { max-width: 384px; }
.md-max-w-md { max-width: 448px; }
.md-max-w-lg { max-width: 512px; }
.md-max-w-xl { max-width: 576px; }

/* Spacing */
.md-m-0 { margin: 0; }
.md-mt-xs { margin-top: var(--md-spacing-xs); }
.md-mt-sm { margin-top: var(--md-spacing-sm); }
.md-mt-md { margin-top: var(--md-spacing-md); }
.md-mt-lg { margin-top: var(--md-spacing-lg); }
.md-mt-xl { margin-top: var(--md-spacing-xl); }

.md-mb-xs { margin-bottom: var(--md-spacing-xs); }
.md-mb-sm { margin-bottom: var(--md-spacing-sm); }
.md-mb-md { margin-bottom: var(--md-spacing-md); }
.md-mb-lg { margin-bottom: var(--md-spacing-lg); }
.md-mb-xl { margin-bottom: var(--md-spacing-xl); }

.md-ml-xs { margin-left: var(--md-spacing-xs); }
.md-ml-sm { margin-left: var(--md-spacing-sm); }
.md-ml-md { margin-left: var(--md-spacing-md); }
.md-ml-lg { margin-left: var(--md-spacing-lg); }
.md-ml-xl { margin-left: var(--md-spacing-xl); }

.md-mr-xs { margin-right: var(--md-spacing-xs); }
.md-mr-sm { margin-right: var(--md-spacing-sm); }
.md-mr-md { margin-right: var(--md-spacing-md); }
.md-mr-lg { margin-right: var(--md-spacing-lg); }
.md-mr-xl { margin-right: var(--md-spacing-xl); }

.md-p-xs { padding: var(--md-spacing-xs); }
.md-p-sm { padding: var(--md-spacing-sm); }
.md-p-md { padding: var(--md-spacing-md); }
.md-p-lg { padding: var(--md-spacing-lg); }
.md-p-xl { padding: var(--md-spacing-xl); }

.md-px-xs { padding-left: var(--md-spacing-xs); padding-right: var(--md-spacing-xs); }
.md-px-sm { padding-left: var(--md-spacing-sm); padding-right: var(--md-spacing-sm); }
.md-px-md { padding-left: var(--md-spacing-md); padding-right: var(--md-spacing-md); }
.md-px-lg { padding-left: var(--md-spacing-lg); padding-right: var(--md-spacing-lg); }
.md-px-xl { padding-left: var(--md-spacing-xl); padding-right: var(--md-spacing-xl); }

.md-py-xs { padding-top: var(--md-spacing-xs); padding-bottom: var(--md-spacing-xs); }
.md-py-sm { padding-top: var(--md-spacing-sm); padding-bottom: var(--md-spacing-sm); }
.md-py-md { padding-top: var(--md-spacing-md); padding-bottom: var(--md-spacing-md); }
.md-py-lg { padding-top: var(--md-spacing-lg); padding-bottom: var(--md-spacing-lg); }
.md-py-xl { padding-top: var(--md-spacing-xl); padding-bottom: var(--md-spacing-xl); }

/* Text Alignment */
.md-text-left { text-align: left; }
.md-text-center { text-align: center; }
.md-text-right { text-align: right; }

/* Text Colors */
.md-text-primary { color: var(--md-text-primary); }
.md-text-secondary { color: var(--md-text-secondary); }
.md-text-disabled { color: var(--md-text-disabled); }
.md-text-google-blue { color: var(--google-blue); }
.md-text-google-red { color: var(--google-red); }
.md-text-google-green { color: var(--google-green); }
.md-text-google-yellow { color: var(--google-yellow); }

/* ============================================
   MATERIAL DESIGN COMPONENTS
   ============================================ */

/* ============================================
   MODERN CARD SYSTEM
   ============================================ */

.md-card {
  background-color: var(--md-surface);
  border-radius: var(--md-radius-xl);
  box-shadow: var(--md-elevation-1);
  padding: var(--md-spacing-xl);
  border: 1px solid var(--md-outline-variant);
  transition: all var(--md-motion-duration-medium) var(--md-motion-easing-standard);
  position: relative;
  overflow: hidden;
}

.md-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--md-primary), transparent);
  opacity: 0;
  transition: opacity var(--md-motion-duration-medium) var(--md-motion-easing-standard);
}

.md-card:hover {
  box-shadow: var(--md-elevation-3);
  transform: translateY(-2px);
  border-color: var(--md-outline);
}

.md-card:hover::before {
  opacity: 0.6;
}

.md-card-outlined {
  border: 1.5px solid var(--md-outline);
  box-shadow: none;
  background-color: var(--md-surface-bright);
}

.md-card-outlined:hover {
  border-color: var(--md-primary);
  box-shadow: var(--md-elevation-1);
}

.md-card-filled {
  background-color: var(--md-surface-container-high);
  border-color: transparent;
}

.md-card-elevated {
  box-shadow: var(--md-elevation-3);
  border: none;
}

.md-card-elevated:hover {
  box-shadow: var(--md-elevation-4);
}

.md-card-compact {
  padding: var(--md-spacing-lg);
  border-radius: var(--md-radius-lg);
}

.md-card-interactive {
  cursor: pointer;
  user-select: none;
}

.md-card-interactive:active {
  transform: translateY(0);
  box-shadow: var(--md-elevation-1);
}

/* ============================================
   MODERN BUTTON SYSTEM
   ============================================ */

.md-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-spacing-sm);
  padding: 10px 24px;
  border-radius: var(--md-radius-lg);
  font-family: 'Google Sans', 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  letter-spacing: 0.1px;
  text-decoration: none;
  cursor: pointer;
  border: none;
  transition: all var(--md-motion-duration-short) var(--md-motion-easing-standard);
  min-height: 40px;
  outline: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.md-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: currentColor;
  opacity: 0;
  transition: opacity var(--md-motion-duration-short) var(--md-motion-easing-standard);
}

.md-btn:hover::before {
  opacity: 0.08;
}

.md-btn:focus-visible::before {
  opacity: 0.12;
}

.md-btn:active::before {
  opacity: 0.16;
}

.md-btn:disabled {
  opacity: 0.38;
  cursor: not-allowed;
  pointer-events: none;
}

/* Filled Button - Primary */
.md-btn-filled {
  background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
  color: var(--md-on-primary);
  box-shadow: var(--md-elevation-1);
}

.md-btn-filled:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--md-primary-dark) 0%, var(--md-primary) 100%);
  box-shadow: var(--md-elevation-2);
  transform: translateY(-1px);
}

.md-btn-filled:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--md-elevation-1);
}

/* Outlined Button */
.md-btn-outlined {
  background-color: transparent;
  color: var(--md-primary);
  border: 1.5px solid var(--md-outline);
  box-shadow: none;
}

.md-btn-outlined:hover:not(:disabled) {
  background-color: var(--md-primary-container-light);
  border-color: var(--md-primary);
  box-shadow: var(--md-elevation-1);
}

.md-btn-outlined:focus-visible {
  border-color: var(--md-primary);
  box-shadow: var(--md-elevation-focus);
}

/* Text Button */
.md-btn-text {
  background-color: transparent;
  color: var(--md-primary);
  padding: 8px 16px;
  min-height: 36px;
}

.md-btn-text:hover:not(:disabled) {
  background-color: var(--md-primary-container-light);
}

/* Secondary Button */
.md-btn-secondary {
  background: linear-gradient(135deg, var(--md-secondary) 0%, var(--md-secondary-light) 100%);
  color: var(--md-on-secondary);
  box-shadow: var(--md-elevation-1);
}

.md-btn-secondary:hover:not(:disabled) {
  background: var(--md-secondary);
  box-shadow: var(--md-elevation-2);
  transform: translateY(-1px);
}

/* Success Button */
.md-btn-success {
  background: linear-gradient(135deg, var(--md-success) 0%, var(--md-success-light) 100%);
  color: var(--md-on-success);
  box-shadow: var(--md-elevation-1);
}

.md-btn-success:hover:not(:disabled) {
  background: var(--md-success);
  box-shadow: var(--md-elevation-2);
  transform: translateY(-1px);
}

/* Error Button */
.md-btn-error {
  background: linear-gradient(135deg, var(--md-error) 0%, var(--md-error-light) 100%);
  color: var(--md-on-error);
  box-shadow: var(--md-elevation-1);
}

.md-btn-error:hover:not(:disabled) {
  background: var(--md-error);
  box-shadow: var(--md-elevation-2);
  transform: translateY(-1px);
}

/* Ghost Button */
.md-btn-ghost {
  background-color: transparent;
  color: var(--md-text-secondary);
  border: 1px solid var(--md-outline-light);
}

.md-btn-ghost:hover:not(:disabled) {
  background-color: var(--md-surface-container-low);
  border-color: var(--md-outline);
  color: var(--md-text-primary);
}

/* Gap utility */
.md-gap-md {
  gap: var(--md-spacing-md);
}

/* ============================================
   MODERN TOP APP BAR
   ============================================ */

.md-top-app-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  background: linear-gradient(135deg, var(--md-surface) 0%, var(--md-surface-bright) 100%);
  border-bottom: 1px solid var(--md-outline-variant);
  box-shadow: var(--md-elevation-2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all var(--md-motion-duration-medium) var(--md-motion-easing-standard);
}

.md-top-app-bar-title {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-md);
  font-size: 20px;
  font-weight: 600;
  color: var(--md-text-primary);
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-top-app-bar-title .material-icons {
  font-size: 28px;
  color: var(--md-primary);
}

.md-top-app-bar-actions {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-md);
}

/* ============================================
   MODERN CHIPS
   ============================================ */

.md-chip {
  display: inline-flex;
  align-items: center;
  gap: var(--md-spacing-xs);
  padding: 8px 16px;
  background-color: var(--md-surface-container);
  color: var(--md-on-surface);
  border-radius: var(--md-radius-full);
  font-size: 14px;
  font-weight: 500;
  border: 1px solid var(--md-outline-variant);
  transition: all var(--md-motion-duration-short) var(--md-motion-easing-standard);
  cursor: default;
  user-select: none;
}

.md-chip:hover {
  background-color: var(--md-surface-container-high);
  border-color: var(--md-outline);
}

.md-chip-selected {
  background: linear-gradient(135deg, var(--md-primary-container) 0%, var(--md-primary-container-light) 100%);
  color: var(--md-on-primary-container);
  border-color: var(--md-primary);
}

.md-chip-outlined {
  background-color: transparent;
  border: 1.5px solid var(--md-outline);
}

.md-chip-outlined:hover {
  background-color: var(--md-surface-container-low);
}

.md-primary-container {
  background: linear-gradient(135deg, var(--md-primary-container) 0%, var(--md-primary-container-light) 100%);
  color: var(--md-on-primary-container);
  border-color: var(--md-primary);
}

/* Button Size Variants */
.md-btn-sm {
  padding: 6px 16px;
  font-size: 12px;
  min-height: 32px;
  border-radius: var(--md-radius-md);
  gap: 6px;
}

.md-btn-lg {
  padding: 14px 32px;
  font-size: 16px;
  min-height: 48px;
  border-radius: var(--md-radius-xl);
  gap: 10px;
  font-weight: 600;
}

.md-btn-xl {
  padding: 18px 40px;
  font-size: 18px;
  min-height: 56px;
  border-radius: var(--md-radius-xl);
  gap: 12px;
  font-weight: 600;
}

/* Button Width Variants */
.md-w-full {
  width: 100%;
}

.md-w-auto {
  width: auto;
}

/* Icon Button */
.md-btn-icon {
  padding: 8px;
  min-width: 40px;
  min-height: 40px;
  border-radius: var(--md-radius-full);
}

.md-btn-icon.md-btn-sm {
  padding: 6px;
  min-width: 32px;
  min-height: 32px;
}

.md-btn-icon.md-btn-lg {
  padding: 12px;
  min-width: 48px;
  min-height: 48px;
}

/* ============================================
   MODERN FORM ELEMENTS
   ============================================ */

.md-form-group {
  margin-bottom: var(--md-spacing-lg);
  position: relative;
}

.md-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--md-text-primary);
  margin-bottom: var(--md-spacing-sm);
  letter-spacing: 0.1px;
  font-family: 'Google Sans', 'Roboto', sans-serif;
}

.md-input,
.md-textarea {
  width: 100%;
  padding: 14px 16px;
  border: 1.5px solid var(--md-outline);
  border-radius: var(--md-radius-md);
  font-size: 16px;
  font-family: inherit;
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  transition: all var(--md-motion-duration-short) var(--md-motion-easing-standard);
  outline: none;
  resize: none;
}

.md-input:hover:not(:focus):not(:disabled),
.md-textarea:hover:not(:focus):not(:disabled) {
  border-color: var(--md-text-primary);
  background-color: var(--md-surface-tint);
}

.md-input:focus,
.md-textarea:focus {
  border-color: var(--md-primary);
  box-shadow: var(--md-elevation-focus);
  background-color: var(--md-surface-bright);
}

.md-input::placeholder,
.md-textarea::placeholder {
  color: var(--md-text-secondary);
  opacity: 1;
}

.md-input:disabled,
.md-textarea:disabled {
  background-color: var(--md-surface-dim);
  border-color: var(--md-outline-variant);
  color: var(--md-text-disabled);
  cursor: not-allowed;
}

.md-textarea {
  min-height: 80px;
  resize: vertical;
}

/* Input with error state */
.md-input.md-error,
.md-textarea.md-error {
  border-color: var(--md-error);
}

.md-input.md-error:focus,
.md-textarea.md-error:focus {
  box-shadow: 0 0 0 3px rgba(217, 48, 37, 0.2);
}

/* Text error styling */
.md-text-error {
  color: var(--md-error);
  font-size: 12px;
  margin-top: var(--md-spacing-xs);
}

/* Tabs */
.md-tabs {
  display: flex;
  border-bottom: 1px solid var(--md-outline-variant);
  margin-bottom: var(--md-spacing-lg);
}

.md-tab {
  flex: 1;
  padding: 16px 12px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: var(--md-text-secondary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.md-tab:hover {
  background-color: var(--md-surface-container-low);
  color: var(--md-text-primary);
}

.md-tab.active {
  color: var(--md-primary);
}

.md-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--md-primary);
}

.md-tab-content {
  display: none;
}

.md-tab-content.active {
  display: block;
}

/* Status Messages */
.md-status-success {
  background-color: var(--md-success-container);
  color: var(--md-on-success-container);
  border-left: 4px solid var(--md-success);
}

.md-status-error {
  background-color: var(--md-error-container);
  color: var(--md-on-error-container);
  border-left: 4px solid var(--md-error);
}

.md-status-info {
  background-color: var(--md-primary-container);
  color: var(--md-on-primary-container);
  border-left: 4px solid var(--md-primary);
}

.md-status-warning {
  background-color: var(--md-warning-container);
  color: var(--md-on-warning-container);
  border-left: 4px solid var(--md-warning);
}

/* Navigation */
.md-nav {
  background-color: var(--md-surface);
  border-right: 1px solid var(--md-outline-variant);
  padding: var(--md-spacing-lg);
}

.md-nav-item {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-md);
  padding: 12px 16px;
  color: var(--md-text-primary);
  text-decoration: none;
  border-radius: var(--md-radius-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: var(--md-spacing-xs);
}

.md-nav-item:hover {
  background-color: var(--md-surface-container-low);
}

.md-nav-item.active {
  background-color: var(--md-primary-container);
  color: var(--md-on-primary-container);
}

/* ============================================
   MODERN CHARTS & DATA VISUALIZATION
   ============================================ */

.md-chart-container {
  background: linear-gradient(135deg, var(--md-surface) 0%, var(--md-surface-bright) 100%);
  border-radius: var(--md-radius-xl);
  padding: var(--md-spacing-xl);
  border: 1px solid var(--md-outline-variant);
  box-shadow: var(--md-elevation-1);
  position: relative;
  overflow: hidden;
  transition: all var(--md-motion-duration-medium) var(--md-motion-easing-standard);
}

.md-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--google-blue-light), var(--google-green-light), var(--google-yellow-light), var(--google-red-light));
  opacity: 0.8;
}

.md-chart-container:hover {
  box-shadow: var(--md-elevation-2);
  transform: translateY(-1px);
}

.md-chart-bar {
  background: linear-gradient(135deg, var(--google-blue-light) 0%, var(--google-green-light) 100%);
  border-radius: var(--md-radius-sm);
  transition: all var(--md-motion-duration-medium) var(--md-motion-easing-standard);
  position: relative;
  overflow: hidden;
}

.md-chart-bar::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform var(--md-motion-duration-long) var(--md-motion-easing-standard);
}

.md-chart-bar:hover {
  transform: scaleY(1.05);
  filter: brightness(1.1);
  box-shadow: var(--md-elevation-1);
}

.md-chart-bar:hover::before {
  transform: translateX(100%);
}

/* Chart Scale and Labels */
.md-chart-scale {
  position: relative;
  height: 40px;
  margin: var(--md-spacing-md) 0;
  border-bottom: 2px solid var(--md-outline);
}

.md-scale-label {
  position: absolute;
  bottom: -30px;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--md-text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.md-scale-label::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 6px;
  background-color: var(--md-outline);
}

/* Chart Bars and Data Points */
.md-chart-data-point {
  position: absolute;
  bottom: 0;
  min-height: 4px;
  background: linear-gradient(135deg, var(--google-blue), var(--google-green));
  border-radius: var(--md-radius-xs) var(--md-radius-xs) 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.md-chart-data-point:hover {
  filter: brightness(1.2);
  transform: scaleY(1.1);
  box-shadow: var(--md-elevation-2);
}

/* Chart Tooltips */
.md-chart-tooltip {
  position: absolute;
  background: rgba(32, 33, 36, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: var(--md-radius-sm);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  z-index: 1000;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--md-elevation-3);
}

/* Additional Chart Components */
.md-scale-tick {
  position: absolute;
  width: 1px;
  height: 8px;
  background-color: var(--md-outline);
  bottom: 0;
}

.md-gas-row {
  display: flex;
  align-items: center;
  margin-bottom: var(--md-spacing-md);
  padding: var(--md-spacing-sm);
  border-radius: var(--md-radius-sm);
  transition: background-color 0.2s ease;
}

.md-gas-row:hover {
  background-color: var(--md-surface-container-low);
}

.md-gas-name {
  width: 200px;
  font-weight: 500;
  color: var(--md-text-primary);
  font-size: 14px;
}

.md-bar-area {
  position: relative;
  flex: 1;
  height: 30px;
  background-color: var(--md-surface-container);
  border-radius: var(--md-radius-xs);
  margin: 0 var(--md-spacing-sm);
}

.md-target-bar {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, var(--google-red), var(--google-yellow));
  border-radius: var(--md-radius-xs);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.md-current-bar {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, var(--google-blue), var(--google-green));
  border-radius: var(--md-radius-xs);
  transition: all 0.3s ease;
}

.md-tooltip {
  position: absolute;
  background: rgba(32, 33, 36, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: var(--md-radius-sm);
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-100%);
  margin-top: -8px;
}

.md-gap-notes {
  margin-top: var(--md-spacing-lg);
  padding: var(--md-spacing-md);
  background-color: var(--md-surface-container-low);
  border-radius: var(--md-radius-md);
  border-left: 4px solid var(--google-blue);
}

.md-gap-notes-text {
  color: var(--md-text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* Lists */
.md-list {
  margin: var(--md-spacing-sm) 0;
  padding-left: var(--md-spacing-lg);
}

.md-list li {
  margin-bottom: var(--md-spacing-xs);
  color: var(--md-text-secondary);
}

/* Tables */
.md-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--md-surface);
  border-radius: var(--md-radius-md);
  overflow: hidden;
  border: 1px solid var(--md-outline-variant);
}

.md-table th {
  background-color: var(--md-surface-container-high);
  color: var(--md-text-primary);
  font-weight: 500;
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--md-outline-variant);
}

.md-table td {
  padding: 16px;
  border-bottom: 1px solid var(--md-outline-variant);
}

.md-table tr:hover {
  background-color: var(--md-surface-container-low);
}

/* ============================================
   MODERN ANIMATIONS & EFFECTS
   ============================================ */

@keyframes md-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.md-fade-in {
  animation: md-fade-in var(--md-motion-duration-long) var(--md-motion-easing-decelerate);
}

@keyframes md-slide-up {
  from {
    opacity: 0;
    transform: translateY(32px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.md-slide-up {
  animation: md-slide-up var(--md-motion-duration-medium) var(--md-motion-easing-decelerate);
}

@keyframes md-scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.md-scale-in {
  animation: md-scale-in var(--md-motion-duration-medium) var(--md-motion-easing-decelerate);
}

@keyframes md-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.md-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200px 100%;
  animation: md-shimmer 2s infinite;
}

/* Elevation utilities */
.md-elevation-0 { box-shadow: var(--md-elevation-0); }
.md-elevation-1 { box-shadow: var(--md-elevation-1); }
.md-elevation-2 { box-shadow: var(--md-elevation-2); }
.md-elevation-3 { box-shadow: var(--md-elevation-3); }
.md-elevation-4 { box-shadow: var(--md-elevation-4); }
.md-elevation-5 { box-shadow: var(--md-elevation-5); }

/* ============================================
   MATERIAL ICONS INTEGRATION
   ============================================ */

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  user-select: none;
}

/* ============================================
   RESPONSIVE DESIGN
   ============================================ */

@media (max-width: 1024px) {
  .md-container {
    max-width: 100%;
    padding: 0 var(--md-spacing-lg);
  }

  .md-chart-container {
    padding: var(--md-spacing-lg);
  }
}

@media (max-width: 768px) {
  .md-container {
    padding: 0 var(--md-spacing-md);
  }

  .md-card {
    padding: var(--md-spacing-lg);
    border-radius: var(--md-radius-lg);
  }

  .md-btn-lg {
    padding: 12px 24px;
    min-height: 44px;
    font-size: 15px;
  }

  .md-btn-xl {
    padding: 14px 28px;
    min-height: 48px;
    font-size: 16px;
  }

  .md-top-app-bar {
    padding: 12px var(--md-spacing-md);
  }

  .md-top-app-bar-title {
    font-size: 18px;
  }

  .md-chart-container {
    padding: var(--md-spacing-md);
    border-radius: var(--md-radius-lg);
  }

  .md-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .md-tabs::-webkit-scrollbar {
    display: none;
  }

  .md-tab {
    min-width: 120px;
    flex-shrink: 0;
  }
}

@media (max-width: 480px) {
  .md-container {
    padding: 0 var(--md-spacing-sm);
  }

  .md-card {
    padding: var(--md-spacing-md);
  }

  .md-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .md-top-app-bar {
    padding: 8px var(--md-spacing-sm);
  }

  .md-top-app-bar-title {
    font-size: 16px;
    gap: var(--md-spacing-sm);
  }

  .md-top-app-bar-title .material-icons {
    font-size: 24px;
  }
}

/* ============================================
   ACCESSIBILITY & PREFERENCES
   ============================================ */

/* Enhanced Focus Styles */
.md-btn:focus-visible,
.md-input:focus-visible,
.md-textarea:focus-visible,
.md-tab:focus-visible,
.md-chip:focus-visible {
  outline: 2px solid var(--md-primary);
  outline-offset: 2px;
  border-radius: inherit;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --md-outline: #000000;
    --md-outline-variant: #000000;
    --md-text-secondary: #000000;
    --md-text-tertiary: #000000;
  }

  .md-btn {
    border: 2px solid currentColor;
  }

  .md-card {
    border: 2px solid var(--md-outline);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .md-shimmer {
    animation: none;
  }
}

/* Dark Mode Support (if needed in future) */
@media (prefers-color-scheme: dark) {
  /* Dark mode variables would go here */
  /* Currently using light theme as requested */
}

/* Print Styles */
@media print {
  .md-btn,
  .md-top-app-bar-actions,
  .md-chip {
    display: none !important;
  }

  .md-card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .md-top-app-bar {
    box-shadow: none;
    border-bottom: 2px solid #000;
  }
}