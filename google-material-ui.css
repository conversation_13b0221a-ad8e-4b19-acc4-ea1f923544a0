/* Google Material Design 3 - Clean UI Framework */
/* Authentic Google Material Design with Clean White Aesthetic */

/* ============================================
   MATERIAL DESIGN 3 COLOR SYSTEM - CLEAN
   ============================================ */

:root {
  /* Official Google Brand Colors */
  --google-blue: #4285f4;
  --google-red: #ea4335;
  --google-yellow: #fbbc04;
  --google-green: #34a853;
  
  /* Material Design 3 Clean Color Tokens */
  --md-primary: #4285f4;
  --md-primary-container: #e8f0fe;
  --md-on-primary: #ffffff;
  --md-on-primary-container: #1a73e8;
  
  --md-secondary: #5f6368;
  --md-secondary-container: #f8f9fa;
  --md-on-secondary: #ffffff;
  --md-on-secondary-container: #202124;
  
  --md-tertiary: #34a853;
  --md-tertiary-container: #e6f4ea;
  --md-on-tertiary: #ffffff;
  --md-on-tertiary-container: #137333;
  
  --md-error: #ea4335;
  --md-error-container: #fce8e6;
  --md-on-error: #ffffff;
  --md-on-error-container: #d93025;
  
  --md-warning: #fbbc04;
  --md-warning-container: #fef7e0;
  --md-on-warning: #000000;
  --md-on-warning-container: #ea8600;
  
  --md-success: #34a853;
  --md-success-container: #e6f4ea;
  --md-on-success: #ffffff;
  --md-on-success-container: #137333;
  
  /* Clean White Surface System */
  --md-background: #ffffff;
  --md-on-background: #202124;
  --md-surface: #ffffff;
  --md-surface-dim: #f8f9fa;
  --md-surface-bright: #ffffff;
  --md-surface-container-lowest: #ffffff;
  --md-surface-container-low: #f8f9fa;
  --md-surface-container: #f1f3f4;
  --md-surface-container-high: #e8eaed;
  --md-surface-container-highest: #dadce0;
  --md-on-surface: #202124;
  --md-on-surface-variant: #5f6368;
  
  /* Text Colors */
  --md-text-primary: #202124;
  --md-text-secondary: #5f6368;
  --md-text-disabled: #9aa0a6;
  --md-text-on-primary: #ffffff;
  --md-text-on-secondary: #ffffff;
  
  /* Border Colors */
  --md-outline: #dadce0;
  --md-outline-variant: #e8eaed;
  
  /* Elevation & Shadow */
  --md-elevation-1: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
  --md-elevation-2: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  --md-elevation-3: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px 0 rgba(60, 64, 67, 0.3);
  --md-elevation-4: 0 6px 10px 4px rgba(60, 64, 67, 0.15), 0 2px 3px 0 rgba(60, 64, 67, 0.3);
  --md-elevation-5: 0 8px 12px 6px rgba(60, 64, 67, 0.15), 0 4px 4px 0 rgba(60, 64, 67, 0.3);
  
  /* Spacing Scale */
  --md-spacing-xs: 4px;
  --md-spacing-sm: 8px;
  --md-spacing-md: 16px;
  --md-spacing-lg: 24px;
  --md-spacing-xl: 32px;
  --md-spacing-xxl: 48px;
  
  /* Border Radius */
  --md-radius-xs: 4px;
  --md-radius-sm: 8px;
  --md-radius-md: 12px;
  --md-radius-lg: 16px;
  --md-radius-xl: 24px;
  --md-radius-full: 50%;
}

/* ============================================
   GLOBAL STYLES
   ============================================ */

* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--md-background);
  color: var(--md-on-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ============================================
   TYPOGRAPHY SCALE
   ============================================ */

.md-display-large {
  font-size: 57px;
  line-height: 64px;
  font-weight: 400;
  letter-spacing: -0.25px;
}

.md-display-medium {
  font-size: 45px;
  line-height: 52px;
  font-weight: 400;
  letter-spacing: 0px;
}

.md-display-small {
  font-size: 36px;
  line-height: 44px;
  font-weight: 400;
  letter-spacing: 0px;
}

.md-headline-large {
  font-size: 32px;
  line-height: 40px;
  font-weight: 400;
  letter-spacing: 0px;
}

.md-headline-medium {
  font-size: 28px;
  line-height: 36px;
  font-weight: 400;
  letter-spacing: 0px;
}

.md-headline-small {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
  letter-spacing: 0px;
}

.md-title-large {
  font-size: 22px;
  line-height: 28px;
  font-weight: 500;
  letter-spacing: 0px;
}

.md-title-medium {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  letter-spacing: 0.15px;
}

.md-title-small {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 0.1px;
}

.md-label-large {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 0.1px;
}

.md-label-medium {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.md-label-small {
  font-size: 11px;
  line-height: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.md-body-large {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  letter-spacing: 0.15px;
}

.md-body-medium {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  letter-spacing: 0.25px;
}

.md-body-small {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  letter-spacing: 0.4px;
}

/* ============================================
   LAYOUT & UTILITY CLASSES
   ============================================ */

.md-flex { display: flex; }
.md-inline-flex { display: inline-flex; }
.md-grid { display: grid; }
.md-hidden { display: none; }
.md-block { display: block; }
.md-inline-block { display: inline-block; }

.md-flex-col { flex-direction: column; }
.md-flex-row { flex-direction: row; }
.md-items-center { align-items: center; }
.md-items-start { align-items: flex-start; }
.md-items-end { align-items: flex-end; }
.md-justify-center { justify-content: center; }
.md-justify-between { justify-content: space-between; }
.md-justify-around { justify-content: space-around; }
.md-justify-start { justify-content: flex-start; }
.md-justify-end { justify-content: flex-end; }

.md-flex-1 { flex: 1; }
.md-flex-grow { flex-grow: 1; }
.md-flex-shrink { flex-shrink: 1; }
.md-flex-none { flex: none; }

.md-w-full { width: 100%; }
.md-h-full { height: 100%; }
.md-max-w-xs { max-width: 320px; }
.md-max-w-sm { max-width: 384px; }
.md-max-w-md { max-width: 448px; }
.md-max-w-lg { max-width: 512px; }
.md-max-w-xl { max-width: 576px; }

/* Spacing */
.md-m-0 { margin: 0; }
.md-mt-xs { margin-top: var(--md-spacing-xs); }
.md-mt-sm { margin-top: var(--md-spacing-sm); }
.md-mt-md { margin-top: var(--md-spacing-md); }
.md-mt-lg { margin-top: var(--md-spacing-lg); }
.md-mt-xl { margin-top: var(--md-spacing-xl); }

.md-mb-xs { margin-bottom: var(--md-spacing-xs); }
.md-mb-sm { margin-bottom: var(--md-spacing-sm); }
.md-mb-md { margin-bottom: var(--md-spacing-md); }
.md-mb-lg { margin-bottom: var(--md-spacing-lg); }
.md-mb-xl { margin-bottom: var(--md-spacing-xl); }

.md-ml-xs { margin-left: var(--md-spacing-xs); }
.md-ml-sm { margin-left: var(--md-spacing-sm); }
.md-ml-md { margin-left: var(--md-spacing-md); }
.md-ml-lg { margin-left: var(--md-spacing-lg); }
.md-ml-xl { margin-left: var(--md-spacing-xl); }

.md-mr-xs { margin-right: var(--md-spacing-xs); }
.md-mr-sm { margin-right: var(--md-spacing-sm); }
.md-mr-md { margin-right: var(--md-spacing-md); }
.md-mr-lg { margin-right: var(--md-spacing-lg); }
.md-mr-xl { margin-right: var(--md-spacing-xl); }

.md-p-xs { padding: var(--md-spacing-xs); }
.md-p-sm { padding: var(--md-spacing-sm); }
.md-p-md { padding: var(--md-spacing-md); }
.md-p-lg { padding: var(--md-spacing-lg); }
.md-p-xl { padding: var(--md-spacing-xl); }

.md-px-xs { padding-left: var(--md-spacing-xs); padding-right: var(--md-spacing-xs); }
.md-px-sm { padding-left: var(--md-spacing-sm); padding-right: var(--md-spacing-sm); }
.md-px-md { padding-left: var(--md-spacing-md); padding-right: var(--md-spacing-md); }
.md-px-lg { padding-left: var(--md-spacing-lg); padding-right: var(--md-spacing-lg); }
.md-px-xl { padding-left: var(--md-spacing-xl); padding-right: var(--md-spacing-xl); }

.md-py-xs { padding-top: var(--md-spacing-xs); padding-bottom: var(--md-spacing-xs); }
.md-py-sm { padding-top: var(--md-spacing-sm); padding-bottom: var(--md-spacing-sm); }
.md-py-md { padding-top: var(--md-spacing-md); padding-bottom: var(--md-spacing-md); }
.md-py-lg { padding-top: var(--md-spacing-lg); padding-bottom: var(--md-spacing-lg); }
.md-py-xl { padding-top: var(--md-spacing-xl); padding-bottom: var(--md-spacing-xl); }

/* Text Alignment */
.md-text-left { text-align: left; }
.md-text-center { text-align: center; }
.md-text-right { text-align: right; }

/* Text Colors */
.md-text-primary { color: var(--md-text-primary); }
.md-text-secondary { color: var(--md-text-secondary); }
.md-text-disabled { color: var(--md-text-disabled); }
.md-text-google-blue { color: var(--google-blue); }
.md-text-google-red { color: var(--google-red); }
.md-text-google-green { color: var(--google-green); }
.md-text-google-yellow { color: var(--google-yellow); }

/* ============================================
   MATERIAL DESIGN COMPONENTS
   ============================================ */

/* Cards */
.md-card {
  background-color: var(--md-surface);
  border-radius: var(--md-radius-md);
  box-shadow: var(--md-elevation-1);
  padding: var(--md-spacing-lg);
  border: 1px solid var(--md-outline-variant);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.md-card:hover {
  box-shadow: var(--md-elevation-2);
}

.md-card-outlined {
  border: 1px solid var(--md-outline);
  box-shadow: none;
}

.md-card-filled {
  background-color: var(--md-surface-container-high);
}

.md-card-elevated {
  box-shadow: var(--md-elevation-3);
}

.md-card-compact {
  padding: var(--md-spacing-md);
}

/* Buttons */
.md-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-spacing-sm);
  padding: 12px 24px;
  border-radius: var(--md-radius-full);
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  text-decoration: none;
  cursor: pointer;
  border: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 40px;
  outline: none;
  position: relative;
  overflow: hidden;
}

.md-btn:disabled {
  opacity: 0.38;
  cursor: not-allowed;
}

/* Filled Button */
.md-btn-filled {
  background-color: var(--md-primary);
  color: var(--md-on-primary);
}

.md-btn-filled:hover:not(:disabled) {
  background-color: var(--google-blue);
  box-shadow: var(--md-elevation-1);
  transform: translateY(-1px);
}

.md-btn-filled:active {
  transform: translateY(0);
}

/* Outlined Button */
.md-btn-outlined {
  background-color: transparent;
  color: var(--md-primary);
  border: 1px solid var(--md-outline);
}

.md-btn-outlined:hover:not(:disabled) {
  background-color: var(--md-primary-container);
  border-color: var(--md-primary);
}

/* Text Button */
.md-btn-text {
  background-color: transparent;
  color: var(--md-primary);
  padding: 8px 12px;
}

.md-btn-text:hover:not(:disabled) {
  background-color: var(--md-primary-container);
}

/* Secondary Buttons */
.md-btn-secondary {
  background-color: var(--md-secondary);
  color: var(--md-on-secondary);
}

.md-btn-secondary:hover:not(:disabled) {
  background-color: var(--md-text-secondary);
  box-shadow: var(--md-elevation-1);
}

/* Success Buttons */
.md-btn-success {
  background-color: var(--md-success);
  color: var(--md-on-success);
}

.md-btn-success:hover:not(:disabled) {
  background-color: var(--google-green);
  box-shadow: var(--md-elevation-1);
  transform: translateY(-1px);
}

/* Ghost Buttons */
.md-btn-ghost {
  background-color: transparent;
  color: var(--md-text-secondary);
  border: 1px solid var(--md-outline-variant);
}

.md-btn-ghost:hover:not(:disabled) {
  background-color: var(--md-surface-container-low);
  border-color: var(--md-outline);
}

/* Gap utility */
.md-gap-md {
  gap: var(--md-spacing-md);
}

/* Top App Bar */
.md-top-app-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: var(--md-surface);
  border-bottom: 1px solid var(--md-outline-variant);
  box-shadow: var(--md-elevation-1);
}

.md-top-app-bar-title {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-sm);
  font-size: 22px;
  font-weight: 500;
  color: var(--md-text-primary);
}

.md-top-app-bar-actions {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-md);
}

/* Chips */
.md-chip {
  display: inline-flex;
  align-items: center;
  gap: var(--md-spacing-xs);
  padding: 8px 16px;
  background-color: var(--md-surface-container);
  color: var(--md-on-surface);
  border-radius: var(--md-radius-full);
  font-size: 14px;
  font-weight: 500;
  border: 1px solid var(--md-outline-variant);
}

/* Button Sizes */
.md-btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  min-height: 32px;
}

.md-btn-lg {
  padding: 16px 32px;
  font-size: 16px;
  min-height: 48px;
}

.md-w-full {
  width: 100%;
}

/* Form Elements */
.md-form-group {
  margin-bottom: var(--md-spacing-lg);
}

.md-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--md-text-primary);
  margin-bottom: var(--md-spacing-sm);
  letter-spacing: 0.1px;
}

.md-input {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--md-outline);
  border-radius: var(--md-radius-xs);
  font-size: 16px;
  font-family: inherit;
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.md-input:focus {
  border-color: var(--md-primary);
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.md-input:hover:not(:focus) {
  border-color: var(--md-on-surface);
}

.md-input::placeholder {
  color: var(--md-text-secondary);
}

/* Tabs */
.md-tabs {
  display: flex;
  border-bottom: 1px solid var(--md-outline-variant);
  margin-bottom: var(--md-spacing-lg);
}

.md-tab {
  flex: 1;
  padding: 16px 12px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: var(--md-text-secondary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.md-tab:hover {
  background-color: var(--md-surface-container-low);
  color: var(--md-text-primary);
}

.md-tab.active {
  color: var(--md-primary);
}

.md-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--md-primary);
}

.md-tab-content {
  display: none;
}

.md-tab-content.active {
  display: block;
}

/* Status Messages */
.md-status-success {
  background-color: var(--md-success-container);
  color: var(--md-on-success-container);
  border-left: 4px solid var(--md-success);
}

.md-status-error {
  background-color: var(--md-error-container);
  color: var(--md-on-error-container);
  border-left: 4px solid var(--md-error);
}

.md-status-info {
  background-color: var(--md-primary-container);
  color: var(--md-on-primary-container);
  border-left: 4px solid var(--md-primary);
}

.md-status-warning {
  background-color: var(--md-warning-container);
  color: var(--md-on-warning-container);
  border-left: 4px solid var(--md-warning);
}

/* Navigation */
.md-nav {
  background-color: var(--md-surface);
  border-right: 1px solid var(--md-outline-variant);
  padding: var(--md-spacing-lg);
}

.md-nav-item {
  display: flex;
  align-items: center;
  gap: var(--md-spacing-md);
  padding: 12px 16px;
  color: var(--md-text-primary);
  text-decoration: none;
  border-radius: var(--md-radius-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: var(--md-spacing-xs);
}

.md-nav-item:hover {
  background-color: var(--md-surface-container-low);
}

.md-nav-item.active {
  background-color: var(--md-primary-container);
  color: var(--md-on-primary-container);
}

/* Charts & Data Visualization */
.md-chart-container {
  background-color: var(--md-surface);
  border-radius: var(--md-radius-md);
  padding: var(--md-spacing-lg);
  border: 1px solid var(--md-outline-variant);
}

.md-chart-bar {
  background: linear-gradient(135deg, var(--google-blue), var(--google-green));
  border-radius: var(--md-radius-xs);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.md-chart-bar:hover {
  transform: scaleY(1.05);
  filter: brightness(1.1);
}

/* Chart Scale and Labels */
.md-chart-scale {
  position: relative;
  height: 40px;
  margin: var(--md-spacing-md) 0;
  border-bottom: 2px solid var(--md-outline);
}

.md-scale-label {
  position: absolute;
  bottom: -30px;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--md-text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.md-scale-label::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 6px;
  background-color: var(--md-outline);
}

/* Chart Bars and Data Points */
.md-chart-data-point {
  position: absolute;
  bottom: 0;
  min-height: 4px;
  background: linear-gradient(135deg, var(--google-blue), var(--google-green));
  border-radius: var(--md-radius-xs) var(--md-radius-xs) 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.md-chart-data-point:hover {
  filter: brightness(1.2);
  transform: scaleY(1.1);
  box-shadow: var(--md-elevation-2);
}

/* Chart Tooltips */
.md-chart-tooltip {
  position: absolute;
  background: rgba(32, 33, 36, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: var(--md-radius-sm);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  z-index: 1000;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--md-elevation-3);
}

/* Additional Chart Components */
.md-scale-tick {
  position: absolute;
  width: 1px;
  height: 8px;
  background-color: var(--md-outline);
  bottom: 0;
}

.md-gas-row {
  display: flex;
  align-items: center;
  margin-bottom: var(--md-spacing-md);
  padding: var(--md-spacing-sm);
  border-radius: var(--md-radius-sm);
  transition: background-color 0.2s ease;
}

.md-gas-row:hover {
  background-color: var(--md-surface-container-low);
}

.md-gas-name {
  width: 200px;
  font-weight: 500;
  color: var(--md-text-primary);
  font-size: 14px;
}

.md-bar-area {
  position: relative;
  flex: 1;
  height: 30px;
  background-color: var(--md-surface-container);
  border-radius: var(--md-radius-xs);
  margin: 0 var(--md-spacing-sm);
}

.md-target-bar {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, var(--google-red), var(--google-yellow));
  border-radius: var(--md-radius-xs);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.md-current-bar {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, var(--google-blue), var(--google-green));
  border-radius: var(--md-radius-xs);
  transition: all 0.3s ease;
}

.md-tooltip {
  position: absolute;
  background: rgba(32, 33, 36, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: var(--md-radius-sm);
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-100%);
  margin-top: -8px;
}

.md-gap-notes {
  margin-top: var(--md-spacing-lg);
  padding: var(--md-spacing-md);
  background-color: var(--md-surface-container-low);
  border-radius: var(--md-radius-md);
  border-left: 4px solid var(--google-blue);
}

.md-gap-notes-text {
  color: var(--md-text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* Lists */
.md-list {
  margin: var(--md-spacing-sm) 0;
  padding-left: var(--md-spacing-lg);
}

.md-list li {
  margin-bottom: var(--md-spacing-xs);
  color: var(--md-text-secondary);
}

/* Tables */
.md-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--md-surface);
  border-radius: var(--md-radius-md);
  overflow: hidden;
  border: 1px solid var(--md-outline-variant);
}

.md-table th {
  background-color: var(--md-surface-container-high);
  color: var(--md-text-primary);
  font-weight: 500;
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--md-outline-variant);
}

.md-table td {
  padding: 16px;
  border-bottom: 1px solid var(--md-outline-variant);
}

.md-table tr:hover {
  background-color: var(--md-surface-container-low);
}

/* Animations */
@keyframes md-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.md-fade-in {
  animation: md-fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes md-slide-up {
  from {
    opacity: 0;
    transform: translateY(32px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.md-slide-up {
  animation: md-slide-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Material Icons Integration */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Responsive Design */
@media (max-width: 768px) {
  .md-card {
    padding: var(--md-spacing-md);
  }
  
  .md-btn-lg {
    padding: 14px 24px;
    min-height: 44px;
  }
  
  .md-tabs {
    overflow-x: auto;
  }
  
  .md-tab {
    min-width: 120px;
    flex-shrink: 0;
  }
}

/* Focus Styles for Accessibility */
.md-btn:focus-visible,
.md-input:focus-visible,
.md-tab:focus-visible {
  outline: 2px solid var(--md-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --md-outline: #000000;
    --md-text-secondary: #000000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}