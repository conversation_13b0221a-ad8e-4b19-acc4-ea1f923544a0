<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Analysis Tool (Guest Mode)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="guestLoading" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 text-center max-w-sm mx-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p class="text-gray-600 mb-4">Loading Alpha Gas Solution...</p>
            <div class="text-xs text-gray-500">
                Guest Mode - No account required
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="hidden">
        <!-- Guest Menu Toggle -->
        <button id="guestMenuToggle" class="fixed top-4 right-4 z-30 bg-gray-500 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-gray-600 transition-colors">
            <span>G</span>
        </button>

        <!-- Guest Menu -->
        <div id="guestMenu" class="hidden fixed top-4 right-4 z-40">
            <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-[200px]">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white font-medium mr-3">
                        <span>G</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 text-sm">Guest User</p>
                        <p class="text-gray-500 text-xs">Limited data persistence</p>
                    </div>
                </div>
                <hr class="my-2">
                <a href="./direct-login-simple.html" class="block w-full text-left px-2 py-1 text-blue-600 hover:bg-blue-50 rounded text-sm">
                    Create Account
                </a>
                <a href="./direct-login-simple.html" class="block w-full text-left px-2 py-1 text-green-600 hover:bg-green-50 rounded text-sm">
                    Sign In
                </a>
            </div>
        </div>

        <div class="chart-container">
            <h1 class="header-title">Alpha Gas Solution</h1>
            <p class="header-subtitle">Analysis Range Visualization & AI Calibration Check</p>
            <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <span class="text-green-600 mr-2">🎯</span>
                    <span class="text-green-800 font-medium">Guest Mode:</span>
                    <span class="text-green-700 ml-1">Full functionality, data saves locally</span>
                </div>
            </div>

            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4299e1;"></div>
                    <span>Current Capability</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #a0aec0; opacity: 0.5;"></div>
                    <span>Target Range (All-Rounder)</span>
                </div>
            </div>

            <div style="position: relative; height: 20px; margin-bottom: 25px; border-bottom: 1px solid #cbd5e0;">
                <div id="scaleContainer">
                    <!-- Scale labels will be added here -->
                </div>
            </div>

            <div id="chart">
                <!-- Gas rows will be dynamically added here -->
            </div>
        </div>

        <div class="customization-form-container">
            <h2 class="form-title">Add New Analyte to Chart</h2>
            <form id="addAnalyteForm">
                <div class="form-section">
                    <label for="analyteName" class="form-label">Analyte Name:</label>
                    <input type="text" id="analyteName" class="saas-input" required>
                    <div id="analyteNameError" class="error-message" style="display: none;"></div>
                </div>

                <div class="form-section">
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Current Capabilities</h3>
                    <div id="currentRangesContainer" class="dynamic-ranges-container">
                        <!-- Current range inputs will be added here -->
                    </div>
                    <button type="button" class="add-range-btn" onclick="addRangeInput('current')">Add Current Range</button>
                </div>

                <div class="form-section">
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Target Ranges</h3>
                    <div id="targetRangesContainer" class="dynamic-ranges-container">
                        <!-- Target range inputs will be added here -->
                    </div>
                    <button type="button" class="add-range-btn" onclick="addRangeInput('target')">Add Target Range</button>
                </div>
                
                <div class="form-section">
                    <label for="gapNotes" class="form-label">Gap Notes / Remarks:</label>
                    <textarea id="gapNotes" class="saas-textarea"></textarea>
                </div>

                <!-- Save Status and Controls -->
                <div class="form-section" id="saveControlsSection">
                    <div class="save-status-container" id="saveStatusContainer">
                        <div class="auth-status guest" id="authStatus">
                            <span class="status-indicator" id="authIndicator">👤</span>
                            <span id="authStatusText">Guest Mode - Local storage only</span>
                        </div>
                        <div class="save-status" id="saveStatus" style="display: none;">
                            <span class="status-indicator" id="saveIndicator">💾</span>
                            <span id="saveStatusText">Ready to save</span>
                        </div>
                    </div>
                    
                    <div class="form-buttons-row">
                        <button type="submit" class="submit-btn" id="addAnalyteBtn">📊 Add to Chart (Local)</button>
                        <button type="button" class="save-btn" id="confirmSaveBtn" title="Save data locally (guest mode)">
                            <span id="saveButtonText">💾 Save Locally (Guest Mode)</span>
                        </button>
                    </div>
                    
                    <div class="save-info" id="saveInfo">
                        <p class="text-sm text-gray-600">
                            <strong>Guest Mode:</strong> Data saved locally only (lost on refresh)<br>
                            <strong>Want persistence?</strong> <a href="direct-login.html" class="text-blue-600 hover:underline">Sign in for database sync</a>
                        </p>
                    </div>
                </div>
            </form>
        </div>

        <!-- AI Calibration Gas Analyzer Section -->
        <div class="calibration-analyzer-container">
            <h2 class="analyzer-title">AI Calibration Gas Capability Analysis</h2>
            <div class="analyzer-section">
                <div>
                    <label for="calibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                    <input type="text" id="calibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix">
                </div>
                
                <h3 class="text-lg font-medium text-gray-700 mt-4 mb-2">Components:</h3>
                <div id="calibrationCompoundsContainer">
                    <!-- Compound input rows will be added here -->
                </div>
                <button type="button" id="addCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
                <div id="calibrationInputError" class="error-message" style="display: none;"></div>
                <button type="button" id="analyzeCalibrationBtn" class="analyzer-btn">🔬 Analyze Calibration Standard</button>
            </div>
            <div id="calibrationLoading" class="text-sm text-gray-600 my-2" style="display: none;">Analyzing, please wait...</div>
            <div id="calibrationResults">
                <!-- AI analysis results will be displayed here -->
            </div>
        </div>
    </div>

    <!-- Load scripts in order -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script type="module" src="role-manager.js"></script>
    <script src="chart.js"></script>
    <script src="form-handler.js"></script>
    <script src="ai-analyzer.js"></script>
    <script src="app.js"></script>
    
    <script type="module">
        // Guest mode initialization with role management
        import { roleManager } from './role-manager.js';
        
        console.log('🎯 Starting Alpha Gas Solution in Guest Mode');
        
        // Initialize role manager for guest mode
        async function initializeGuestMode() {
            try {
                // Initialize as guest
                roleManager.currentRole = 'guest';
                roleManager.currentStatus = 'guest';
                roleManager.isInitialized = true;
                
                // Setup guest UI
                roleManager.setupUI();
                
                // Setup guest menu
                setupGuestMenu();
                
                console.log('✅ Guest mode role management initialized');
            } catch (error) {
                console.error('Error initializing guest role:', error);
            }
        }
        console.log('🎯 Starting Alpha Gas Solution in Guest Mode');
        
        // Setup guest menu
        function setupGuestMenu() {
            const guestMenuToggle = document.getElementById('guestMenuToggle');
            const guestMenu = document.getElementById('guestMenu');

            guestMenuToggle.addEventListener('click', () => {
                guestMenu.classList.toggle('hidden');
            });

            document.addEventListener('click', (e) => {
                if (!guestMenu.contains(e.target) && !guestMenuToggle.contains(e.target)) {
                    guestMenu.classList.add('hidden');
                }
            });
        }

        // Guest Mode Save Confirmation
        function initializeGuestSaveConfirmation() {
            const confirmSaveBtn = document.getElementById('confirmSaveBtn');
            const saveStatus = document.getElementById('saveStatus');
            const saveIndicator = document.getElementById('saveIndicator');
            const saveStatusText = document.getElementById('saveStatusText');
            const saveButtonText = document.getElementById('saveButtonText');
            
            if (confirmSaveBtn) {
                confirmSaveBtn.addEventListener('click', async () => {
                    // Update button state
                    confirmSaveBtn.classList.add('saving');
                    saveButtonText.textContent = '⏳ Saving...';
                    confirmSaveBtn.disabled = true;
                    
                    // Show save status
                    saveStatus.style.display = 'flex';
                    saveStatus.className = 'save-status pending';
                    saveIndicator.textContent = '⏳';
                    saveStatusText.textContent = 'Saving to local storage...';
                    
                    try {
                        // Save to localStorage (guest mode)
                        localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
                        console.log('💾 Data saved to localStorage in guest mode');
                        
                        // Success state
                        setTimeout(() => {
                            confirmSaveBtn.classList.remove('saving');
                            confirmSaveBtn.classList.add('success');
                            saveButtonText.textContent = '✅ Saved Locally';
                            
                            saveStatus.className = 'save-status success';
                            saveIndicator.textContent = '✅';
                            saveStatusText.textContent = 'Data saved locally (lost on refresh)';
                            
                            // Reset after delay
                            setTimeout(() => {
                                confirmSaveBtn.classList.remove('success');
                                saveButtonText.textContent = '💾 Save Locally (Guest Mode)';
                                confirmSaveBtn.disabled = false;
                                saveStatus.style.display = 'none';
                            }, 3000);
                        }, 1000);
                        
                    } catch (error) {
                        console.error('❌ Error saving to localStorage:', error);
                        
                        // Error state
                        confirmSaveBtn.classList.remove('saving');
                        confirmSaveBtn.classList.add('error');
                        saveButtonText.textContent = '❌ Save Failed';
                        
                        saveStatus.className = 'save-status error';
                        saveIndicator.textContent = '❌';
                        saveStatusText.textContent = `Save failed: ${error.message}`;
                        
                        // Reset after delay
                        setTimeout(() => {
                            confirmSaveBtn.classList.remove('error');
                            saveButtonText.textContent = '💾 Save Locally (Guest Mode)';
                            confirmSaveBtn.disabled = false;
                            saveStatus.style.display = 'none';
                        }, 5000);
                    }
                });
            }
        }

        // Initialize the app after loading
        setTimeout(async () => {
            try {
                console.log('🚀 Initializing guest mode application');
                
                // Initialize guest role management
                await initializeGuestMode();
                
                // Hide loading screen
                document.getElementById('guestLoading').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                
                // Initialize app components (with error handling)
                try {
                    if (typeof loadDataFromLocalStorage === 'function') {
                        loadDataFromLocalStorage();
                    }
                } catch (e) {
                    console.log('Local storage not available, continuing...');
                }
                
                try {
                    if (typeof drawScaleLabels === 'function') {
                        drawScaleLabels();
                    }
                } catch (e) {
                    console.log('Scale labels not available, continuing...');
                }
                
                try {
                    if (typeof renderChart === 'function') {
                        renderChart();
                    }
                } catch (e) {
                    console.log('Chart rendering not available, continuing...');
                }
                
                try {
                    if (typeof initializeFormHandler === 'function') {
                        initializeFormHandler();
                    }
                } catch (e) {
                    console.log('Form handler not available, continuing...');
                }
                
                try {
                    if (typeof initializeAIAnalyzer === 'function') {
                        initializeAIAnalyzer();
                    }
                } catch (e) {
                    console.log('AI analyzer not available, continuing...');
                }
                
                try {
                    if (typeof addDataManagementControls === 'function') {
                        addDataManagementControls();
                    }
                } catch (e) {
                    console.log('Data management controls not available, continuing...');
                }
                
                try {
                    if (typeof addRangeInput === 'function') {
                        addRangeInput('current'); 
                        addRangeInput('target'); 
                    }
                } catch (e) {
                    console.log('Range inputs not available, continuing...');
                }
                
                try {
                    if (typeof addCalibrationCompoundRow === 'function') {
                        addCalibrationCompoundRow();
                    }
                } catch (e) {
                    console.log('Calibration compound row not available, continuing...');
                }
                
                // Initialize guest-specific save confirmation
                initializeGuestSaveConfirmation();
                
                console.log('✅ Guest mode application initialized successfully');
                
            } catch (error) {
                console.error('Error initializing guest mode:', error);
                // Show basic interface anyway
                document.getElementById('guestLoading').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
            }
        }, 800);
    </script>
</body>
</html>
