<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Analysis Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Skip Authentication - Direct Access -->
    <div id="bypassMessage" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 text-center max-w-sm mx-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600 mb-4">Launching Alpha Gas Solution...</p>
            <div class="text-xs text-gray-500">
                Loading analysis tool in guest mode
            </div>
        </div>
    </div>

    <!-- Main Application Content (Hidden until auth bypass is complete) -->
    <div id="mainApp" class="hidden">
        <!-- User Menu Toggle (Guest Mode) -->
        <button id="guestMenuToggle" class="fixed top-4 right-4 z-30 bg-gray-500 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-gray-600 transition-colors">
            <span>G</span>
        </button>

        <!-- Guest Menu -->
        <div id="guestMenu" class="hidden fixed top-4 right-4 z-40">
            <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-[200px]">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white font-medium mr-3">
                        <span>G</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 text-sm">Guest User</p>
                        <p class="text-gray-500 text-xs">Local Storage Only</p>
                    </div>
                </div>
                <hr class="my-2">
                <button id="createAccountFromGuest" class="w-full text-left px-2 py-1 text-blue-600 hover:bg-blue-50 rounded text-sm mb-1">
                    Create Account
                </button>
                <button id="signInFromGuest" class="w-full text-left px-2 py-1 text-green-600 hover:bg-green-50 rounded text-sm">
                    Sign In
                </button>
            </div>
        </div>

        <!-- Include the main app content -->
        <div id="mainContent">
            <!-- This will be loaded from the main index.html -->
        </div>
    </div>

    <script type="module">
        // Guest mode initialization - bypass all authentication
        let guestMode = true;
        
        // Show loading for 500ms then load the app
        setTimeout(() => {
            document.getElementById('bypassMessage').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            initializeGuestMode();
        }, 500);

        function initializeGuestMode() {
            console.log('🎯 Initializing Alpha Gas Solution in Guest Mode');
            
            // Load the main application content
            loadMainAppContent();
            
            // Setup guest menu
            setupGuestMenu();
            
            // Initialize the app with guest mode
            initializeApp();
        }

        async function loadMainAppContent() {
            try {
                // Fetch the main content from index.html
                const response = await fetch('./index.html');
                const html = await response.text();
                
                // Extract the main content (skip auth loading screen)
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // Get all content after the auth loading screen
                const mainContent = doc.querySelector('#mainContent, .chart-container, main, .container');
                if (mainContent) {
                    document.getElementById('mainContent').innerHTML = mainContent.outerHTML;
                } else {
                    // If no specific main content, get the body content excluding auth screens
                    const bodyContent = Array.from(doc.body.children)
                        .filter(el => !el.id || !el.id.includes('auth'))
                        .map(el => el.outerHTML)
                        .join('');
                    document.getElementById('mainContent').innerHTML = bodyContent;
                }
                
                // Load and execute the app scripts
                await loadAppScripts();
                
            } catch (error) {
                console.error('Error loading main app content:', error);
                // Fallback: redirect to the direct login page
                window.location.href = './direct-login.html';
            }
        }

        async function loadAppScripts() {
            try {
                // Load required modules
                const { initializeFormHandler } = await import('./form-handler.js');
                const { initializeAIAnalyzer } = await import('./ai-analyzer.js');
                const { drawScaleLabels, renderChart } = await import('./chart.js');
                const { addRangeInput, addCalibrationCompoundRow } = await import('./app.js');
                
                // Initialize all components in guest mode
                if (typeof drawScaleLabels === 'function') drawScaleLabels();
                if (typeof renderChart === 'function') renderChart();
                if (typeof initializeFormHandler === 'function') initializeFormHandler();
                if (typeof initializeAIAnalyzer === 'function') initializeAIAnalyzer();
                
                // Add data management controls (guest mode)
                addGuestDataManagementControls();
                
                // Add initial range inputs
                if (typeof addRangeInput === 'function') {
                    addRangeInput('current'); 
                    addRangeInput('target'); 
                }
                
                // Add initial compound row for calibration analyzer
                if (typeof addCalibrationCompoundRow === 'function') {
                    addCalibrationCompoundRow();
                }
                
                console.log('✅ Alpha Gas Solution initialized successfully in Guest Mode');
                
            } catch (error) {
                console.error('Error loading app scripts:', error);
                // Show basic functionality message
                document.getElementById('mainContent').innerHTML = `
                    <div class="p-8 text-center">
                        <h1 class="text-3xl font-bold text-gray-800 mb-4">Alpha Gas Solution</h1>
                        <p class="text-gray-600 mb-4">Analysis Range Visualization & AI Calibration</p>
                        <p class="text-sm text-gray-500">Guest Mode - Basic functionality available</p>
                        <div class="mt-8">
                            <button onclick="window.location.href='./direct-login.html'" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                Access Full Features
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function setupGuestMenu() {
            const guestMenuToggle = document.getElementById('guestMenuToggle');
            const guestMenu = document.getElementById('guestMenu');
            const createAccountFromGuest = document.getElementById('createAccountFromGuest');
            const signInFromGuest = document.getElementById('signInFromGuest');

            // Toggle menu
            guestMenuToggle.addEventListener('click', () => {
                guestMenu.classList.toggle('hidden');
            });

            // Hide menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!guestMenu.contains(e.target) && !guestMenuToggle.contains(e.target)) {
                    guestMenu.classList.add('hidden');
                }
            });

            // Menu actions
            createAccountFromGuest.addEventListener('click', () => {
                window.location.href = './direct-login.html';
            });

            signInFromGuest.addEventListener('click', () => {
                window.location.href = './direct-login.html';
            });
        }

        function addGuestDataManagementControls() {
            // Add guest-specific data management message
            const container = document.querySelector('.container, main, body');
            if (container) {
                const guestNotice = document.createElement('div');
                guestNotice.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4';
                guestNotice.innerHTML = `
                    <div class="flex items-center">
                        <div class="text-blue-600 mr-3">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-blue-800 font-medium">Guest Mode Active</h4>
                            <p class="text-blue-600 text-sm">Data saves locally only. <a href="./direct-login.html" class="underline hover:text-blue-800">Create account</a> for cloud sync.</p>
                        </div>
                    </div>
                `;
                container.insertBefore(guestNotice, container.firstChild);
            }
        }

        async function initializeApp() {
            // Guest mode initialization complete
            console.log('🎉 Guest mode ready - full functionality available');
        }
    </script>
</body>
</html>
