<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Fast Auth Check</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Minimal Loading Screen -->
    <div id="authLoading" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Checking authentication...</p>
            <p class="text-sm text-gray-400 mt-2" id="loadingStatus">Initializing...</p>
        </div>
    </div>

    <!-- Rest of the app (hidden until auth complete) -->
    <div id="mainApp" class="hidden">
        <div class="chart-container">
            <h1 class="header-title">Alpha Gas Solution</h1>
            <p class="header-subtitle">Analysis Range Visualization & AI Calibration Check</p>
            <p class="text-center text-green-600 font-semibold">✅ Authentication successful! Loading main application...</p>
        </div>
    </div>

    <!-- Fast Authentication Script -->
    <script type="module">
        // Fast auth check with timeout
        const TIMEOUT_MS = 3000; // 3 second timeout
        let authCheckComplete = false;

        function updateStatus(message) {
            const statusEl = document.getElementById('loadingStatus');
            if (statusEl) statusEl.textContent = message;
            console.log(`[FAST-AUTH] ${message}`);
        }

        function redirectToLogin(reason = 'Not authenticated') {
            console.log(`[FAST-AUTH] Redirecting to login: ${reason}`);
            window.location.href = './login.html';
        }

        function showMainApp() {
            document.getElementById('authLoading').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            console.log('[FAST-AUTH] Showing main application');
        }

        async function fastAuthCheck() {
            const startTime = Date.now();
            updateStatus('Checking authentication...');

            try {
                // Set a timeout to prevent hanging
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Authentication timeout')), TIMEOUT_MS);
                });

                // Import and check auth
                const authPromise = (async () => {
                    updateStatus('Loading auth module...');
                    const { authManager } = await import('./auth.js');
                    
                    updateStatus('Checking session...');
                    const user = await authManager.init();
                    
                    return user;
                })();

                // Race between auth check and timeout
                const user = await Promise.race([authPromise, timeoutPromise]);
                
                const elapsed = Date.now() - startTime;
                console.log(`[FAST-AUTH] Auth check completed in ${elapsed}ms`);

                if (!user) {
                    updateStatus('No user found, redirecting...');
                    setTimeout(() => redirectToLogin('No authenticated user'), 500);
                } else {
                    updateStatus(`Welcome ${user.email}!`);
                    console.log(`[FAST-AUTH] User authenticated: ${user.email}`);
                    setTimeout(showMainApp, 500);
                }

            } catch (error) {
                const elapsed = Date.now() - startTime;
                console.error(`[FAST-AUTH] Auth failed after ${elapsed}ms:`, error);
                
                if (error.message === 'Authentication timeout') {
                    updateStatus('Authentication taking too long, redirecting...');
                    setTimeout(() => redirectToLogin('Authentication timeout'), 1000);
                } else {
                    updateStatus('Authentication error, redirecting...');
                    setTimeout(() => redirectToLogin(`Auth error: ${error.message}`), 1000);
                }
            } finally {
                authCheckComplete = true;
            }
        }

        // Fallback timeout - if nothing happens in 5 seconds, redirect
        setTimeout(() => {
            if (!authCheckComplete) {
                console.error('[FAST-AUTH] Fallback timeout - forcing redirect');
                redirectToLogin('Fallback timeout');
            }
        }, 5000);

        // Start auth check immediately
        fastAuthCheck();

        // Also provide a manual redirect option
        window.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                console.log('[FAST-AUTH] Manual redirect triggered (ESC key)');
                redirectToLogin('Manual redirect');
            }
        });
    </script>

    <style>
        .chart-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        .header-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        .header-subtitle {
            font-size: 1.25rem;
            color: #6b7280;
            margin-bottom: 2rem;
        }
    </style>
</body>
</html>
