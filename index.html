<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Customizable Analysis Range & AI Calibration Check</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
</head>
<body>
    <!-- Authentication Loading Screen -->
    <div id="authLoading" class="md-flex md-items-center md-justify-center" style="position: fixed; inset: 0; background: rgba(0,0,0,0.5); z-index: 50;">
        <div class="md-card md-max-w-sm md-text-center">
            <div class="md-loading-spinner" style="margin: 0 auto 16px;"></div>
            <p id="authStatus" class="md-text-secondary md-mb-md">Checking for existing account...</p>
            <div class="md-body-small md-text-secondary md-mb-md">
                <span id="authTimer">0s</span> elapsed
            </div>
            <button id="skipAuthBtn" class="md-btn md-btn-filled md-mb-sm">
                <span class="material-icons">rocket_launch</span>
                Skip to Quick Access
            </button>
            <div class="md-body-small md-text-secondary md-mt-sm">
                Get instant access to the app
            </div>
        </div>
    </div>

    <!-- User Menu -->
    <div id="userMenu" class="md-md-hidden md-card md-elevation-3" style="position: fixed; top: 16px; right: 16px; z-index: 40; min-width: 200px;">
        <div class="md-flex md-items-center md-mb-md">
            <div style="width: 32px; height: 32px; background: var(--md-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 500; margin-right: 12px;">
                <span id="userInitial">U</span>
            </div>
            <div>
                <p id="userName" class="md-body-medium">Loading...</p>
                <p id="userEmail" class="md-body-small md-text-secondary">Loading...</p>
            </div>
        </div>
            <hr class="my-2">
            <button id="signOutBtn" class="w-full text-left px-2 py-1 text-red-600 hover:bg-red-50 rounded text-sm">
                Sign Out
            </button>
        </div>
    </div>

    <!-- User Menu Toggle -->
    <button id="userMenuToggle" class="md-hidden fixed top-4 right-4 z-30 bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-blue-600 transition-colors">
        <span id="userMenuInitial">U</span>
    </button>

    <div class="chart-container">
        <h1 class="header-title">Alpha Gas Solution</h1>
        <p class="header-subtitle">Analysis Range Visualization & AI Calibration Check</p>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4299e1;"></div>
                <span>Current Capability</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #a0aec0; opacity: 0.5;"></div>
                <span>Target Range (All-Rounder)</span>
            </div>
        </div>

        <div style="position: relative; height: 20px; margin-bottom: 25px; border-bottom: 1px solid #cbd5e0;">
            <!-- Scale labels will be dynamically added here by JavaScript -->
        </div>

        <div id="chart">
            <!-- Gas rows will be dynamically added here -->
        </div>
    </div>

    <div class="customization-form-container">
        <h2 class="form-title">Add New Analyte to Chart</h2>
        <form id="addAnalyteForm">
            <div class="form-section">
                <label for="analyteName" class="form-label">Analyte Name:</label>
                <input type="text" id="analyteName" class="saas-input" required>
                <div id="analyteNameError" class="error-message" style="display: none;"></div>
            </div>

            <div class="form-section">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Current Capabilities</h3>
                <div id="currentRangesContainer" class="dynamic-ranges-container">
                    <!-- Current range inputs will be added here -->
                </div>
                <button type="button" class="add-range-btn" onclick="addRangeInput('current')">Add Current Range</button>
            </div>

            <div class="form-section">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Target Ranges</h3>
                <div id="targetRangesContainer" class="dynamic-ranges-container">
                    <!-- Target range inputs will be added here -->
                </div>
                <button type="button" class="add-range-btn" onclick="addRangeInput('target')">Add Target Range</button>
            </div>
            
            <div class="form-section">
                <label for="gapNotes" class="form-label">Gap Notes / Remarks:</label>
                <textarea id="gapNotes" class="saas-textarea"></textarea>
            </div>

            <button type="submit" class="submit-btn">Add Analyte to Chart</button>
        </form>
    </div>

    <!-- AI Calibration Gas Analyzer Section -->
    <div class="calibration-analyzer-container">
        <h2 class="analyzer-title">AI Calibration Gas Capability Analysis</h2>
        <div class="analyzer-section">
            <div>
                <label for="calibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                <input type="text" id="calibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix">
            </div>
            
            <h3 class="text-lg font-medium text-gray-700 mt-4 mb-2">Components:</h3>
            <div id="calibrationCompoundsContainer">
                <!-- Compound input rows will be added here -->
            </div>
            <button type="button" id="addCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
            <div id="calibrationInputError" class="error-message" style="display: none;"></div>
            <button type="button" id="analyzeCalibrationBtn" class="analyzer-btn">🔬 Analyze Calibration Standard</button>
        </div>
        <div id="calibrationLoading" class="text-sm text-gray-600 my-2" style="display: none;">Analyzing, please wait...</div>
        <div id="calibrationResults">
            <!-- AI analysis results will be displayed here -->
        </div>
    </div>

    <script src="config.js"></script>
    <script src="data.js"></script>
    <script type="module" src="supabase-client.js"></script>
    <script type="module" src="storage.js"></script>
    <script src="chart.js"></script>
    <script src="form-handler.js"></script>
    <script src="ai-analyzer.js"></script>
    <!-- app.js will be loaded dynamically after authentication -->
    
    <!-- Authentication Check and Smart Redirect -->
    <script type="module">
        import { supabase } from './supabase-client.js';
        
        let authTimer = 0;
        const authTimerElement = document.getElementById('authTimer');
        
        // Update timer display
        const timerInterval = setInterval(() => {
            authTimer++;
            if (authTimerElement) {
                authTimerElement.textContent = `${authTimer}s`;
            }
        }, 1000);
        
        // Quick skip button functionality
        document.getElementById('skipAuthBtn').addEventListener('click', () => {
            clearInterval(timerInterval);
            console.log('🚀 User chose quick access - redirecting to guest mode');
            window.location.href = './guest-mode-simple.html';
        });
        
        // Check for existing session
        async function checkAuthentication() {
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (session && !error) {
                    // User is authenticated - go to main app
                    console.log('✅ User authenticated, redirecting to app');
                    document.getElementById('authStatus').textContent = 'Account found! Redirecting to dashboard...';
                    clearInterval(timerInterval);
                    setTimeout(() => {
                        window.location.href = './app.html';
                    }, 1500);
                } else {
                    // No session - show options after delay
                    setTimeout(() => {
                        document.getElementById('authStatus').textContent = 'No existing session found';
                        setTimeout(() => {
                            clearInterval(timerInterval);
                            window.location.href = './direct-login-simple.html';
                        }, 2000);
                    }, 3000);
                }
            } catch (error) {
                console.error('Auth check failed:', error);
                setTimeout(() => {
                    clearInterval(timerInterval);
                    window.location.href = './direct-login-simple.html';
                }, 2000);
            }
        }
        
        // Start authentication check
        checkAuthentication();
    </script>
</body>
</html>
