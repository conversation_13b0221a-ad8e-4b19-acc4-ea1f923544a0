<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live RBAC System Validation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-4">🔍 Live RBAC System Validation Report</h1>
        
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
            <h2 class="font-semibold text-blue-800 mb-2">Testing Progress</h2>
            <div id="progress" class="w-full bg-blue-200 rounded-full h-2">
                <div id="progressBar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
            <div id="progressText" class="text-sm text-blue-600 mt-1">Initializing...</div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Test Results -->
            <div class="space-y-4">
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <span id="moduleIcon" class="mr-2">⏳</span>Module Loading
                    </h3>
                    <div id="moduleResults" class="space-y-1 text-sm"></div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <span id="dbIcon" class="mr-2">⏳</span>Database Connectivity
                    </h3>
                    <div id="dbResults" class="space-y-1 text-sm"></div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <span id="authIcon" class="mr-2">⏳</span>Authentication System
                    </h3>
                    <div id="authResults" class="space-y-1 text-sm"></div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <span id="roleIcon" class="mr-2">⏳</span>Role Management
                    </h3>
                    <div id="roleResults" class="space-y-1 text-sm"></div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <span id="dataIcon" class="mr-2">⏳</span>Data Persistence
                    </h3>
                    <div id="dataResults" class="space-y-1 text-sm"></div>
                </div>
            </div>
            
            <!-- System Status & Actions -->
            <div class="space-y-4">
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Overall System Status</h3>
                    <div id="overallStatus" class="p-3 rounded text-center">
                        <div class="text-lg font-semibold">Checking...</div>
                        <div class="text-sm">Please wait while we validate the system</div>
                    </div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Current User Status</h3>
                    <div id="userStatus" class="space-y-2 text-sm"></div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">System Metrics</h3>
                    <div id="systemMetrics" class="space-y-2 text-sm"></div>
                </div>
                
                <div class="p-4 border rounded">
                    <h3 class="font-semibold mb-2">Actions</h3>
                    <div class="space-y-2">
                        <button id="retestBtn" class="w-full bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50">
                            Rerun All Tests
                        </button>
                        <button id="exportBtn" class="w-full bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50">
                            Export Test Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        let testResults = {
            modules: [],
            database: [],
            auth: [],
            roles: [],
            data: [],
            overall: 'pending'
        };

        let currentStep = 0;
        const totalSteps = 5;

        function updateProgress(step, text) {
            currentStep = step;
            const percentage = (step / totalSteps) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        function updateIcon(iconId, status) {
            const icon = document.getElementById(iconId);
            const icons = {
                pending: '⏳',
                success: '✅',
                warning: '⚠️',
                error: '❌'
            };
            icon.textContent = icons[status] || '⏳';
        }

        function addResult(sectionId, message, status = 'info') {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            const colors = {
                success: 'text-green-600',
                error: 'text-red-600',
                warning: 'text-yellow-600',
                info: 'text-blue-600'
            };
            div.className = colors[status] || 'text-gray-600';
            div.textContent = message;
            section.appendChild(div);
            
            // Store result for export
            const category = sectionId.replace('Results', '');
            if (!testResults[category]) testResults[category] = [];
            testResults[category].push({ message, status, timestamp: new Date().toISOString() });
        }

        async function testModuleLoading() {
            updateProgress(1, 'Testing module loading...');
            updateIcon('moduleIcon', 'pending');
            
            try {
                addResult('moduleResults', 'Starting module loading tests...', 'info');
                
                // Test auth.js
                const authModule = await import('./auth.js');
                if (authModule.authManager) {
                    addResult('moduleResults', '✓ auth.js loaded successfully', 'success');
                } else {
                    addResult('moduleResults', '✗ authManager not found in auth.js', 'error');
                    updateIcon('moduleIcon', 'error');
                    return false;
                }

                // Test role-manager.js
                const roleModule = await import('./role-manager.js');
                if (roleModule.roleManager) {
                    addResult('moduleResults', '✓ role-manager.js loaded successfully', 'success');
                } else {
                    addResult('moduleResults', '✗ roleManager not found in role-manager.js', 'error');
                    updateIcon('moduleIcon', 'error');
                    return false;
                }

                // Test other critical modules
                try {
                    const storageModule = await import('./storage.js');
                    addResult('moduleResults', '✓ storage.js loaded successfully', 'success');
                } catch (e) {
                    addResult('moduleResults', '✗ storage.js failed to load', 'error');
                }

                try {
                    const formModule = await import('./form-handler.js');
                    addResult('moduleResults', '✓ form-handler.js loaded successfully', 'success');
                } catch (e) {
                    addResult('moduleResults', '✗ form-handler.js failed to load', 'error');
                }

                updateIcon('moduleIcon', 'success');
                return true;
            } catch (error) {
                addResult('moduleResults', `✗ Module loading failed: ${error.message}`, 'error');
                updateIcon('moduleIcon', 'error');
                return false;
            }
        }

        async function testDatabaseConnectivity() {
            updateProgress(2, 'Testing database connectivity...');
            updateIcon('dbIcon', 'pending');
            
            try {
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

                addResult('dbResults', 'Testing basic connection...', 'info');

                // Test user_profiles table
                const { data: profiles, error: profileError } = await supabase
                    .from('user_profiles')
                    .select('count')
                    .limit(1);

                if (profileError) {
                    addResult('dbResults', `✗ user_profiles table: ${profileError.message}`, 'error');
                    updateIcon('dbIcon', 'error');
                    return false;
                } else {
                    addResult('dbResults', '✓ user_profiles table accessible', 'success');
                }

                // Test gas_analytes table
                const { data: analytes, error: analyteError } = await supabase
                    .from('gas_analytes')
                    .select('count')
                    .limit(1);

                if (analyteError) {
                    addResult('dbResults', `✗ gas_analytes table: ${analyteError.message}`, 'error');
                    updateIcon('dbIcon', 'error');
                    return false;
                } else {
                    addResult('dbResults', '✓ gas_analytes table accessible', 'success');
                }

                // Test admin_audit_log table
                const { data: logs, error: logError } = await supabase
                    .from('admin_audit_log')
                    .select('count')
                    .limit(1);

                if (logError) {
                    addResult('dbResults', `✗ admin_audit_log table: ${logError.message}`, 'error');
                    updateIcon('dbIcon', 'warning');
                } else {
                    addResult('dbResults', '✓ admin_audit_log table accessible', 'success');
                }

                // Test RLS policies
                const { data: currentUser } = await supabase.auth.getUser();
                if (currentUser.user) {
                    addResult('dbResults', '✓ RLS policies active with authenticated user', 'success');
                } else {
                    addResult('dbResults', '⚠ Testing RLS policies without authentication', 'warning');
                }

                updateIcon('dbIcon', 'success');
                return true;
            } catch (error) {
                addResult('dbResults', `✗ Database connectivity failed: ${error.message}`, 'error');
                updateIcon('dbIcon', 'error');
                return false;
            }
        }

        async function testAuthenticationSystem() {
            updateProgress(3, 'Testing authentication system...');
            updateIcon('authIcon', 'pending');
            
            try {
                const authModule = await import('./auth.js');
                const authManager = authModule.authManager;

                addResult('authResults', 'Testing authentication functions...', 'info');

                // Test getCurrentUser
                const currentUser = authManager.getCurrentUser();
                if (currentUser) {
                    addResult('authResults', `✓ Current user: ${currentUser.email}`, 'success');
                    
                    // Test getUserProfile
                    try {
                        const profile = await authManager.getUserProfile();
                        if (profile) {
                            addResult('authResults', `✓ User profile: ${profile.role} (${profile.status})`, 'success');
                            
                            // Update user status section
                            const userStatus = document.getElementById('userStatus');
                            userStatus.innerHTML = `
                                <div><strong>Email:</strong> ${currentUser.email}</div>
                                <div><strong>Role:</strong> ${profile.role}</div>
                                <div><strong>Status:</strong> ${profile.status}</div>
                                <div><strong>Created:</strong> ${new Date(profile.created_at).toLocaleDateString()}</div>
                            `;
                        } else {
                            addResult('authResults', '⚠ User profile not found in database', 'warning');
                        }
                    } catch (e) {
                        addResult('authResults', `✗ getUserProfile failed: ${e.message}`, 'error');
                    }

                    // Test role-based functions
                    try {
                        const isAdmin = await authManager.isAdmin();
                        const isApproved = await authManager.isApproved();
                        const userRole = await authManager.getUserRole();
                        
                        addResult('authResults', `✓ Role checks: Admin(${isAdmin}), Approved(${isApproved}), Role(${userRole})`, 'success');
                    } catch (e) {
                        addResult('authResults', `✗ Role function tests failed: ${e.message}`, 'error');
                    }
                } else {
                    addResult('authResults', '⚠ No user currently authenticated', 'warning');
                    
                    // Update user status for guest
                    const userStatus = document.getElementById('userStatus');
                    userStatus.innerHTML = `
                        <div><strong>Status:</strong> Guest (Not authenticated)</div>
                        <div><strong>Role:</strong> guest</div>
                        <div class="text-sm text-gray-600 mt-2">
                            To test full authentication features, please log in through the main application.
                        </div>
                    `;
                }

                updateIcon('authIcon', 'success');
                return true;
            } catch (error) {
                addResult('authResults', `✗ Authentication system failed: ${error.message}`, 'error');
                updateIcon('authIcon', 'error');
                return false;
            }
        }

        async function testRoleManagement() {
            updateProgress(4, 'Testing role management system...');
            updateIcon('roleIcon', 'pending');
            
            try {
                const roleModule = await import('./role-manager.js');
                const roleManager = roleModule.roleManager;

                addResult('roleResults', 'Initializing role manager...', 'info');

                // Initialize role manager
                await roleManager.init();
                addResult('roleResults', '✓ Role manager initialized successfully', 'success');

                // Test basic role functions
                const currentRole = roleManager.getRole();
                const currentStatus = roleManager.getStatus();
                addResult('roleResults', `✓ Current role: ${currentRole}, Status: ${currentStatus}`, 'success');

                // Test role checks
                const roleChecks = {
                    admin: roleManager.hasRole('admin'),
                    user: roleManager.hasRole('user'),
                    guest: roleManager.hasRole('guest')
                };
                addResult('roleResults', `✓ Role checks - Admin: ${roleChecks.admin}, User: ${roleChecks.user}, Guest: ${roleChecks.guest}`, 'success');

                // Test UI restrictions
                const restrictions = roleManager.getUIRestrictions();
                addResult('roleResults', `✓ UI restrictions configured: ${Object.keys(restrictions).length} settings`, 'success');

                // Test permissions
                const permissions = roleManager.getPermissions();
                addResult('roleResults', `✓ Permissions configured: ${Object.keys(permissions).length} permissions`, 'success');

                // Test UI setup (non-invasive)
                try {
                    const uiInfo = roleManager.setupUI();
                    addResult('roleResults', `✓ UI setup ready: ${JSON.stringify(uiInfo)}`, 'success');
                } catch (e) {
                    addResult('roleResults', `⚠ UI setup test skipped: ${e.message}`, 'warning');
                }

                updateIcon('roleIcon', 'success');
                return true;
            } catch (error) {
                addResult('roleResults', `✗ Role management failed: ${error.message}`, 'error');
                updateIcon('roleIcon', 'error');
                return false;
            }
        }

        async function testDataPersistence() {
            updateProgress(5, 'Testing data persistence...');
            updateIcon('dataIcon', 'pending');
            
            try {
                addResult('dataResults', 'Testing hybrid data system...', 'info');

                // Test form-handler integration
                try {
                    const formModule = await import('./form-handler.js');
                    addResult('dataResults', '✓ form-handler.js integration ready', 'success');
                } catch (e) {
                    addResult('dataResults', `✗ form-handler.js integration failed: ${e.message}`, 'error');
                }

                // Test storage system
                try {
                    const storageModule = await import('./storage.js');
                    addResult('dataResults', '✓ storage.js integration ready', 'success');
                } catch (e) {
                    addResult('dataResults', `✗ storage.js integration failed: ${e.message}`, 'error');
                }

                // Test database query capabilities
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

                // Test analyte data access with RLS
                const { data: systemAnalytes, error: systemError } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('data_type', 'system')
                    .limit(5);

                if (systemError) {
                    addResult('dataResults', `⚠ System analytes query: ${systemError.message}`, 'warning');
                } else {
                    addResult('dataResults', `✓ System analytes accessible: ${systemAnalytes.length} records`, 'success');
                }

                // Test user-specific data access
                const { data: userAnalytes, error: userError } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('data_type', 'user_target')
                    .limit(5);

                if (userError) {
                    addResult('dataResults', `⚠ User analytes query: ${userError.message}`, 'warning');
                } else {
                    addResult('dataResults', `✓ User analytes accessible: ${userAnalytes.length} records`, 'success');
                }

                updateIcon('dataIcon', 'success');
                return true;
            } catch (error) {
                addResult('dataResults', `✗ Data persistence test failed: ${error.message}`, 'error');
                updateIcon('dataIcon', 'error');
                return false;
            }
        }

        async function updateSystemMetrics() {
            const metricsDiv = document.getElementById('systemMetrics');
            
            try {
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

                // Get user counts by role
                const { data: roleStats } = await supabase
                    .from('user_profiles')
                    .select('role, status')
                    .then(result => result);

                if (roleStats && roleStats.data) {
                    const stats = roleStats.data.reduce((acc, user) => {
                        acc[user.role] = (acc[user.role] || 0) + 1;
                        acc[`${user.status}_users`] = (acc[`${user.status}_users`] || 0) + 1;
                        return acc;
                    }, {});

                    metricsDiv.innerHTML = `
                        <div><strong>Total Users:</strong> ${roleStats.data.length}</div>
                        <div><strong>Admins:</strong> ${stats.admin || 0}</div>
                        <div><strong>Users:</strong> ${stats.user || 0}</div>
                        <div><strong>Guests:</strong> ${stats.guest || 0}</div>
                        <div><strong>Pending:</strong> ${stats.pending_users || 0}</div>
                        <div><strong>Approved:</strong> ${stats.approved_users || 0}</div>
                    `;
                } else {
                    metricsDiv.innerHTML = '<div class="text-gray-500">Metrics unavailable</div>';
                }
            } catch (error) {
                metricsDiv.innerHTML = '<div class="text-red-500">Failed to load metrics</div>';
            }
        }

        function updateOverallStatus() {
            const statusDiv = document.getElementById('overallStatus');
            const categories = ['modules', 'database', 'auth', 'roles', 'data'];
            
            let successCount = 0;
            let errorCount = 0;
            let warningCount = 0;

            categories.forEach(category => {
                const results = testResults[category];
                if (results && results.length > 0) {
                    const hasError = results.some(r => r.status === 'error');
                    const hasWarning = results.some(r => r.status === 'warning');
                    
                    if (hasError) {
                        errorCount++;
                    } else if (hasWarning) {
                        warningCount++;
                    } else {
                        successCount++;
                    }
                }
            });

            let statusClass, statusText, statusDetails;
            
            if (errorCount > 0) {
                statusClass = 'bg-red-100 text-red-800 border-red-200';
                statusText = '❌ System Issues Detected';
                statusDetails = `${errorCount} critical issues found. System may not function properly.`;
            } else if (warningCount > 0) {
                statusClass = 'bg-yellow-100 text-yellow-800 border-yellow-200';
                statusText = '⚠️ System Partially Ready';
                statusDetails = `${warningCount} warnings detected. Some features may be limited.`;
            } else {
                statusClass = 'bg-green-100 text-green-800 border-green-200';
                statusText = '✅ System Ready';
                statusDetails = 'All RBAC components are functioning correctly.';
            }

            statusDiv.className = `p-3 rounded text-center border ${statusClass}`;
            statusDiv.innerHTML = `
                <div class="text-lg font-semibold">${statusText}</div>
                <div class="text-sm">${statusDetails}</div>
                <div class="text-xs mt-2">
                    Tests: ${successCount} passed, ${warningCount} warnings, ${errorCount} errors
                </div>
            `;
        }

        async function runAllTests() {
            document.getElementById('retestBtn').disabled = true;
            
            // Clear previous results
            Object.keys(testResults).forEach(key => {
                if (Array.isArray(testResults[key])) {
                    testResults[key] = [];
                }
                const section = document.getElementById(key + 'Results');
                if (section) section.innerHTML = '';
            });

            // Reset icons
            ['moduleIcon', 'dbIcon', 'authIcon', 'roleIcon', 'dataIcon'].forEach(id => {
                updateIcon(id, 'pending');
            });

            try {
                await testModuleLoading();
                await testDatabaseConnectivity();
                await testAuthenticationSystem();
                await testRoleManagement();
                await testDataPersistence();
                
                updateProgress(5, 'All tests completed');
                await updateSystemMetrics();
                updateOverallStatus();
                
            } catch (error) {
                console.error('Test suite error:', error);
                updateProgress(5, 'Tests completed with errors');
            }

            document.getElementById('retestBtn').disabled = false;
        }

        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total_tests: totalSteps,
                    completed: currentStep
                },
                results: testResults,
                system_info: {
                    user_agent: navigator.userAgent,
                    url: window.location.href
                }
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rbac-test-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Event listeners
        document.getElementById('retestBtn').addEventListener('click', runAllTests);
        document.getElementById('exportBtn').addEventListener('click', exportTestReport);

        // Auto-run tests on load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });

        // Handle errors gracefully
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>
