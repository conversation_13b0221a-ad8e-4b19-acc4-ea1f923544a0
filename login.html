<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="login-container">
        <div class="glass-effect rounded-2xl p-8 w-full max-w-md mx-4">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-white mb-2">Alpha Gas Solution</h1>
                <p class="text-gray-200">Analysis Range Visualization & AI Calibration</p>
            </div>

            <!-- Loading state -->
            <div id="loadingState" class="text-center text-white">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                <p>Initializing authentication...</p>
            </div>

            <!-- Login/Signup Form -->
            <div id="authForm" class="hidden">
                <!-- Tab switcher -->
                <div class="flex mb-6 bg-black bg-opacity-20 rounded-lg p-1">
                    <button id="loginTab" class="flex-1 py-2 px-4 rounded-md text-white font-medium transition-all auth-tab active">
                        Sign In
                    </button>
                    <button id="signupTab" class="flex-1 py-2 px-4 rounded-md text-gray-300 font-medium transition-all auth-tab">
                        Sign Up
                    </button>
                </div>

                <!-- Error message -->
                <div id="errorMessage" class="hidden bg-red-500 bg-opacity-20 border border-red-400 text-red-100 px-4 py-3 rounded-lg mb-4">
                </div>

                <!-- Success message -->
                <div id="successMessage" class="hidden bg-green-500 bg-opacity-20 border border-green-400 text-green-100 px-4 py-3 rounded-lg mb-4">
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginEmail" class="block text-sm font-medium text-gray-200 mb-2">Email</label>
                        <input type="email" id="loginEmail" required 
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Enter your email">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-200 mb-2">Password</label>
                        <input type="password" id="loginPassword" required
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Enter your password">
                    </div>
                    <button type="submit" id="loginBtn"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent">
                        Sign In
                    </button>
                </form>

                <!-- Signup Form -->
                <form id="signupForm" class="space-y-4 hidden">
                    <div>
                        <label for="signupName" class="block text-sm font-medium text-gray-200 mb-2">Full Name</label>
                        <input type="text" id="signupName" required
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Enter your full name">
                    </div>
                    <div>
                        <label for="signupEmail" class="block text-sm font-medium text-gray-200 mb-2">Email</label>
                        <input type="email" id="signupEmail" required
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Enter your email">
                    </div>
                    <div>
                        <label for="signupPassword" class="block text-sm font-medium text-gray-200 mb-2">Password</label>
                        <input type="password" id="signupPassword" required minlength="6"
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Create a password (min 6 characters)">
                    </div>
                    <div>
                        <label for="signupPasswordConfirm" class="block text-sm font-medium text-gray-200 mb-2">Confirm Password</label>
                        <input type="password" id="signupPasswordConfirm" required
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Confirm your password">
                    </div>
                    <button type="submit" id="signupBtn"
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-transparent">
                        Create Account
                    </button>
                </form>

                <!-- Divider -->
                <div class="flex items-center my-6">
                    <div class="flex-1 border-t border-gray-300 border-opacity-30"></div>
                    <span class="px-4 text-gray-300 text-sm">or</span>
                    <div class="flex-1 border-t border-gray-300 border-opacity-30"></div>
                </div>

                <!-- Google Sign In -->
                <button id="googleSignInBtn"
                        class="w-full bg-white text-gray-800 font-medium py-3 px-4 rounded-lg transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-transparent flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Continue with Google
                </button>

                <!-- Forgot Password -->
                <div class="text-center mt-4">
                    <button id="forgotPasswordBtn" class="text-gray-300 hover:text-white text-sm underline">
                        Forgot your password?
                    </button>
                </div>
            </div>

            <!-- Forgot Password Form -->
            <div id="forgotPasswordForm" class="hidden">
                <div class="text-center mb-6">
                    <h2 class="text-xl font-semibold text-white mb-2">Reset Password</h2>
                    <p class="text-gray-300 text-sm">Enter your email to receive a password reset link</p>
                </div>
                
                <form id="resetForm" class="space-y-4">
                    <div>
                        <label for="resetEmail" class="block text-sm font-medium text-gray-200 mb-2">Email</label>
                        <input type="email" id="resetEmail" required
                               class="w-full px-4 py-3 rounded-lg bg-white bg-opacity-20 border border-gray-300 border-opacity-30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               placeholder="Enter your email">
                    </div>
                    <button type="submit" id="resetBtn"
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent">
                        Send Reset Link
                    </button>
                </form>
                
                <div class="text-center mt-4">
                    <button id="backToLoginBtn" class="text-gray-300 hover:text-white text-sm underline">
                        Back to sign in
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { authManager } from './auth.js';

        let isLoading = false;

        // DOM elements
        const loadingState = document.getElementById('loadingState');
        const authForm = document.getElementById('authForm');
        const loginTab = document.getElementById('loginTab');
        const signupTab = document.getElementById('signupTab');
        const loginForm = document.getElementById('loginForm');
        const signupForm = document.getElementById('signupForm');
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // Initialize
        async function init() {
            console.log('🔐 Initializing authentication...');
            
            try {
                const user = await authManager.init();
                
                if (user) {
                    console.log('✅ User already authenticated:', user.email);
                    redirectToApp();
                } else {
                    console.log('📝 No authenticated user, showing login form');
                    showAuthForm();
                }
            } catch (error) {
                console.error('❌ Auth initialization failed:', error);
                showError('Failed to initialize authentication. Please refresh the page.');
                showAuthForm();
            }
        }

        function showAuthForm() {
            loadingState.classList.add('hidden');
            authForm.classList.remove('hidden');
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.remove('hidden');
            successMessage.classList.add('hidden');
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.classList.remove('hidden');
            errorMessage.classList.add('hidden');
        }

        function hideMessages() {
            errorMessage.classList.add('hidden');
            successMessage.classList.add('hidden');
        }

        function setLoading(loading) {
            isLoading = loading;
            const buttons = document.querySelectorAll('button[type="submit"]');
            buttons.forEach(btn => {
                btn.disabled = loading;
                if (loading) {
                    btn.innerHTML = '<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>';
                }
            });
        }

        function redirectToApp() {
            console.log('🚀 Redirecting to main application...');
            window.location.href = './index.html';
        }

        // Tab switching
        function switchTab(activeTab) {
            document.querySelectorAll('.auth-tab').forEach(tab => {
                tab.classList.remove('active', 'bg-white', 'bg-opacity-20', 'text-white');
                tab.classList.add('text-gray-300');
            });
            
            activeTab.classList.add('active', 'bg-white', 'bg-opacity-20', 'text-white');
            activeTab.classList.remove('text-gray-300');

            if (activeTab === loginTab) {
                loginForm.classList.remove('hidden');
                signupForm.classList.add('hidden');
            } else {
                loginForm.classList.add('hidden');
                signupForm.classList.remove('hidden');
            }
            hideMessages();
        }

        // Event listeners
        loginTab.addEventListener('click', () => switchTab(loginTab));
        signupTab.addEventListener('click', () => switchTab(signupTab));

        // Login form
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            if (isLoading) return;

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            hideMessages();
            setLoading(true);

            try {
                const { user, error } = await authManager.signIn(email, password);
                
                if (error) {
                    showError(error);
                } else if (user) {
                    console.log('✅ Login successful:', user.email);
                    redirectToApp();
                }
            } catch (error) {
                console.error('❌ Login error:', error);
                showError('An unexpected error occurred. Please try again.');
            } finally {
                setLoading(false);
            }
        });

        // Signup form
        signupForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            if (isLoading) return;

            const name = document.getElementById('signupName').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('signupPasswordConfirm').value;

            hideMessages();

            if (password !== confirmPassword) {
                showError('Passwords do not match.');
                return;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long.');
                return;
            }

            setLoading(true);

            try {
                const { user, error } = await authManager.signUp(email, password, { 
                    full_name: name 
                });
                
                if (error) {
                    showError(error);
                } else {
                    showSuccess('Account created successfully! Please check your email to verify your account before signing in.');
                    setTimeout(() => switchTab(loginTab), 3000);
                }
            } catch (error) {
                console.error('❌ Signup error:', error);
                showError('An unexpected error occurred. Please try again.');
            } finally {
                setLoading(false);
            }
        });

        // Google sign in
        document.getElementById('googleSignInBtn').addEventListener('click', async () => {
            if (isLoading) return;
            
            hideMessages();
            setLoading(true);

            try {
                const { error } = await authManager.signInWithGoogle();
                
                if (error) {
                    showError(error);
                    setLoading(false);
                }
                // If successful, the page will redirect via OAuth
            } catch (error) {
                console.error('❌ Google signin error:', error);
                showError('An unexpected error occurred. Please try again.');
                setLoading(false);
            }
        });

        // Forgot password
        document.getElementById('forgotPasswordBtn').addEventListener('click', () => {
            authForm.classList.add('hidden');
            forgotPasswordForm.classList.remove('hidden');
            hideMessages();
        });

        document.getElementById('backToLoginBtn').addEventListener('click', () => {
            forgotPasswordForm.classList.add('hidden');
            authForm.classList.remove('hidden');
            hideMessages();
        });

        // Reset password form
        document.getElementById('resetForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            if (isLoading) return;

            const email = document.getElementById('resetEmail').value;

            hideMessages();
            setLoading(true);

            try {
                const { error } = await authManager.resetPassword(email);
                
                if (error) {
                    showError(error);
                } else {
                    showSuccess('Password reset link sent! Check your email.');
                }
            } catch (error) {
                console.error('❌ Password reset error:', error);
                showError('An unexpected error occurred. Please try again.');
            } finally {
                setLoading(false);
            }
        });

        // Start the app
        init();
    </script>
</body>
</html>
