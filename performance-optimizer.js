/**
 * Performance Optimizer for RBAC System
 * Provides caching, query optimization, and performance monitoring
 */

export class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.metrics = {
            cacheHits: 0,
            cacheMisses: 0,
            queryCount: 0,
            averageResponseTime: 0,
            slowQueries: []
        };
        this.cacheConfig = {
            maxSize: 1000,
            ttl: 5 * 60 * 1000, // 5 minutes
            roleCheckTtl: 2 * 60 * 1000, // 2 minutes for role checks
            profileTtl: 10 * 60 * 1000 // 10 minutes for user profiles
        };
        this.initialized = false;
    }

    /**
     * Initialize the performance optimizer
     */
    async init() {
        if (this.initialized) return;
        
        console.log('🚀 Initializing Performance Optimizer...');
        
        // Start cache cleanup interval
        this.startCacheCleanup();
        
        // Initialize performance monitoring
        this.startPerformanceMonitoring();
        
        this.initialized = true;
        console.log('✅ Performance Optimizer initialized');
    }

    /**
     * Generate cache key for consistent caching
     */
    generateCacheKey(type, identifier, params = {}) {
        const paramString = Object.keys(params).length > 0 
            ? `_${JSON.stringify(params)}` 
            : '';
        return `${type}_${identifier}${paramString}`;
    }

    /**
     * Cached role check with optimized TTL
     */
    async getCachedRole(userId) {
        const cacheKey = this.generateCacheKey('role', userId);
        const cached = this.getFromCache(cacheKey, this.cacheConfig.roleCheckTtl);
        
        if (cached) {
            this.metrics.cacheHits++;
            return cached;
        }

        this.metrics.cacheMisses++;
        
        // This would be called from auth.js
        const startTime = performance.now();
        try {
            // Placeholder - actual implementation would call auth.getUserRole()
            const role = await this.fetchRoleFromDatabase(userId);
            const responseTime = performance.now() - startTime;
            
            this.recordMetric('role_check', responseTime);
            this.setCache(cacheKey, role, this.cacheConfig.roleCheckTtl);
            
            return role;
        } catch (error) {
            console.error('Role check failed:', error);
            throw error;
        }
    }

    /**
     * Cached user profile with longer TTL
     */
    async getCachedProfile(userId) {
        const cacheKey = this.generateCacheKey('profile', userId);
        const cached = this.getFromCache(cacheKey, this.cacheConfig.profileTtl);
        
        if (cached) {
            this.metrics.cacheHits++;
            return cached;
        }

        this.metrics.cacheMisses++;
        
        const startTime = performance.now();
        try {
            // Placeholder - actual implementation would call auth.getUserProfile()
            const profile = await this.fetchProfileFromDatabase(userId);
            const responseTime = performance.now() - startTime;
            
            this.recordMetric('profile_fetch', responseTime);
            this.setCache(cacheKey, profile, this.cacheConfig.profileTtl);
            
            return profile;
        } catch (error) {
            console.error('Profile fetch failed:', error);
            throw error;
        }
    }

    /**
     * Cached permission check
     */
    async getCachedPermission(userId, resource, action) {
        const cacheKey = this.generateCacheKey('permission', userId, { resource, action });
        const cached = this.getFromCache(cacheKey, this.cacheConfig.roleCheckTtl);
        
        if (cached !== undefined) {
            this.metrics.cacheHits++;
            return cached;
        }

        this.metrics.cacheMisses++;
        
        const startTime = performance.now();
        try {
            // Placeholder - actual implementation would check permissions
            const hasPermission = await this.checkPermissionInDatabase(userId, resource, action);
            const responseTime = performance.now() - startTime;
            
            this.recordMetric('permission_check', responseTime);
            this.setCache(cacheKey, hasPermission, this.cacheConfig.roleCheckTtl);
            
            return hasPermission;
        } catch (error) {
            console.error('Permission check failed:', error);
            throw error;
        }
    }

    /**
     * Set item in cache with TTL
     */
    setCache(key, value, ttl = this.cacheConfig.ttl) {
        if (this.cache.size >= this.cacheConfig.maxSize) {
            // Remove oldest entries
            const oldestKeys = Array.from(this.cache.keys()).slice(0, 10);
            oldestKeys.forEach(k => this.cache.delete(k));
        }

        this.cache.set(key, {
            value,
            timestamp: Date.now(),
            ttl
        });
    }

    /**
     * Get item from cache if not expired
     */
    getFromCache(key, customTtl = null) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        const ttl = customTtl || cached.ttl;
        const isExpired = Date.now() - cached.timestamp > ttl;
        
        if (isExpired) {
            this.cache.delete(key);
            return null;
        }

        return cached.value;
    }

    /**
     * Clear cache entries for a specific user
     */
    clearUserCache(userId) {
        const keysToDelete = [];
        for (const [key] of this.cache) {
            if (key.includes(`_${userId}`)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.cache.delete(key));
        
        console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for user ${userId}`);
    }

    /**
     * Clear all cache
     */
    clearAllCache() {
        const size = this.cache.size;
        this.cache.clear();
        console.log(`🗑️ Cleared all cache (${size} entries)`);
    }

    /**
     * Start periodic cache cleanup
     */
    startCacheCleanup() {
        setInterval(() => {
            const now = Date.now();
            const keysToDelete = [];
            
            for (const [key, entry] of this.cache) {
                if (now - entry.timestamp > entry.ttl) {
                    keysToDelete.push(key);
                }
            }
            
            keysToDelete.forEach(key => this.cache.delete(key));
            
            if (keysToDelete.length > 0) {
                console.log(`🧹 Cleaned up ${keysToDelete.length} expired cache entries`);
            }
        }, 60000); // Clean every minute
    }

    /**
     * Record performance metric
     */
    recordMetric(operation, responseTime) {
        this.metrics.queryCount++;
        
        // Update average response time
        this.metrics.averageResponseTime = 
            (this.metrics.averageResponseTime * (this.metrics.queryCount - 1) + responseTime) / 
            this.metrics.queryCount;

        // Track slow queries
        if (responseTime > 1000) { // Queries over 1 second
            this.metrics.slowQueries.push({
                operation,
                responseTime,
                timestamp: new Date().toISOString()
            });
            
            // Keep only last 100 slow queries
            if (this.metrics.slowQueries.length > 100) {
                this.metrics.slowQueries = this.metrics.slowQueries.slice(-100);
            }
        }
    }

    /**
     * Start performance monitoring
     */
    startPerformanceMonitoring() {
        setInterval(() => {
            this.logPerformanceStats();
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    /**
     * Log performance statistics
     */
    logPerformanceStats() {
        const cacheHitRate = this.metrics.cacheHits / 
            (this.metrics.cacheHits + this.metrics.cacheMisses) * 100;
        
        console.log('📊 Performance Stats:', {
            cacheSize: this.cache.size,
            cacheHitRate: `${cacheHitRate.toFixed(1)}%`,
            totalQueries: this.metrics.queryCount,
            averageResponseTime: `${this.metrics.averageResponseTime.toFixed(2)}ms`,
            slowQueries: this.metrics.slowQueries.length
        });
    }

    /**
     * Get current performance metrics
     */
    getMetrics() {
        const cacheHitRate = this.metrics.cacheMisses > 0 ? 
            (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100) : 0;

        return {
            ...this.metrics,
            cacheHitRate: parseFloat(cacheHitRate.toFixed(1)),
            cacheSize: this.cache.size,
            maxCacheSize: this.cacheConfig.maxSize
        };
    }

    /**
     * Optimize database queries with batching
     */
    async batchRoleChecks(userIds) {
        const startTime = performance.now();
        const results = new Map();
        const uncachedIds = [];

        // Check cache first
        for (const userId of userIds) {
            const cacheKey = this.generateCacheKey('role', userId);
            const cached = this.getFromCache(cacheKey, this.cacheConfig.roleCheckTtl);
            
            if (cached) {
                results.set(userId, cached);
                this.metrics.cacheHits++;
            } else {
                uncachedIds.push(userId);
                this.metrics.cacheMisses++;
            }
        }

        // Batch fetch uncached roles
        if (uncachedIds.length > 0) {
            try {
                const batchResults = await this.batchFetchRoles(uncachedIds);
                
                for (const [userId, role] of batchResults) {
                    results.set(userId, role);
                    const cacheKey = this.generateCacheKey('role', userId);
                    this.setCache(cacheKey, role, this.cacheConfig.roleCheckTtl);
                }
            } catch (error) {
                console.error('Batch role fetch failed:', error);
                throw error;
            }
        }

        const responseTime = performance.now() - startTime;
        this.recordMetric('batch_role_check', responseTime);

        return results;
    }

    /**
     * Placeholder for actual database operations
     * These would be implemented by calling the actual auth/database functions
     */
    async fetchRoleFromDatabase(userId) {
        // This would call auth.getUserRole(userId) in actual implementation
        return 'user'; // Placeholder
    }

    async fetchProfileFromDatabase(userId) {
        // This would call auth.getUserProfile(userId) in actual implementation
        return { id: userId, role: 'user', status: 'approved' }; // Placeholder
    }

    async checkPermissionInDatabase(userId, resource, action) {
        // This would implement actual permission checking logic
        return true; // Placeholder
    }

    async batchFetchRoles(userIds) {
        // This would implement batch database query for roles
        const results = new Map();
        for (const userId of userIds) {
            results.set(userId, 'user'); // Placeholder
        }
        return results;
    }
}

// Create singleton instance
export const performanceOptimizer = new PerformanceOptimizer();

// Enhanced wrapper functions with performance optimization
export function withPerformance(operation) {
    return async function(...args) {
        const startTime = performance.now();
        try {
            const result = await operation.apply(this, args);
            const responseTime = performance.now() - startTime;
            performanceOptimizer.recordMetric(operation.name || 'unknown', responseTime);
            return result;
        } catch (error) {
            const responseTime = performance.now() - startTime;
            performanceOptimizer.recordMetric(`${operation.name || 'unknown'}_error`, responseTime);
            throw error;
        }
    };
}

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        performanceOptimizer.init().catch(console.error);
    });
}

console.log('📈 Performance Optimizer module loaded');
