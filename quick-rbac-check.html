<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick RBAC Validation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-4">🔍 Quick RBAC Validation</h1>
        
        <div class="space-y-4">
            <div class="p-4 border rounded">
                <h3 class="font-semibold mb-2">Module Loading Status</h3>
                <div id="moduleStatus" class="space-y-2 text-sm"></div>
            </div>
            
            <div class="p-4 border rounded">
                <h3 class="font-semibold mb-2">Authentication Status</h3>
                <div id="authStatus" class="space-y-2 text-sm"></div>
            </div>
            
            <div class="p-4 border rounded">
                <h3 class="font-semibold mb-2">Role Manager Status</h3>
                <div id="roleStatus" class="space-y-2 text-sm"></div>
            </div>
            
            <div class="p-4 border rounded">
                <h3 class="font-semibold mb-2">Database Connectivity</h3>
                <div id="dbStatus" class="space-y-2 text-sm"></div>
            </div>
        </div>
        
        <button id="runCheck" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded">
            Run Quick Check
        </button>
    </div>

    <script type="module">
        function log(containerId, message, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = isError ? 'text-red-600' : 'text-green-600';
            div.textContent = message;
            container.appendChild(div);
        }

        async function runQuickCheck() {
            // Clear previous results
            ['moduleStatus', 'authStatus', 'roleStatus', 'dbStatus'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });

            // Test module loading
            try {
                log('moduleStatus', '1. Testing module imports...');
                
                const authModule = await import('./auth.js');
                if (authModule.authManager) {
                    log('moduleStatus', '✅ auth.js loaded successfully');
                } else {
                    log('moduleStatus', '❌ authManager not found in auth.js', true);
                }

                const roleModule = await import('./role-manager.js');
                if (roleModule.roleManager) {
                    log('moduleStatus', '✅ role-manager.js loaded successfully');
                } else {
                    log('moduleStatus', '❌ roleManager not found in role-manager.js', true);
                }

                // Test Supabase connection
                log('dbStatus', '2. Testing Supabase connection...');
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

                const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
                if (error) {
                    log('dbStatus', `❌ Database connection error: ${error.message}`, true);
                } else {
                    log('dbStatus', '✅ Database connection successful');
                }

                // Test authentication
                log('authStatus', '3. Testing authentication...');
                const { authManager } = authModule;
                
                const currentUser = authManager.getCurrentUser();
                if (currentUser) {
                    log('authStatus', `✅ User authenticated: ${currentUser.email}`);
                    
                    const profile = await authManager.getUserProfile();
                    if (profile) {
                        log('authStatus', `✅ User profile: ${profile.role} (${profile.status})`);
                    } else {
                        log('authStatus', '⚠️ User profile not found');
                    }
                } else {
                    log('authStatus', '⚠️ User not authenticated');
                }

                // Test role manager
                log('roleStatus', '4. Testing role manager...');
                const { roleManager } = roleModule;
                
                await roleManager.init();
                log('roleStatus', `✅ Role manager initialized`);
                log('roleStatus', `Current role: ${roleManager.getRole()}`);
                log('roleStatus', `Current status: ${roleManager.getStatus()}`);
                
                const restrictions = roleManager.getUIRestrictions();
                log('roleStatus', `UI restrictions: ${JSON.stringify(restrictions)}`);

            } catch (error) {
                log('moduleStatus', `❌ Error during testing: ${error.message}`, true);
                console.error('Quick check error:', error);
            }
        }

        document.getElementById('runCheck').addEventListener('click', runQuickCheck);

        // Auto-run on load
        setTimeout(runQuickCheck, 500);
    </script>
</body>
</html>
