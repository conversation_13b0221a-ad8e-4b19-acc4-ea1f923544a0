<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Real-Time System Monitor</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="elevated-saas-ui.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="saas-min-h-screen saas-p-lg">
        <div class="saas-container">
            <!-- Header -->
            <div class="saas-card saas-mb-xl saas-fade-in">
                <div class="saas-flex saas-justify-between saas-items-center">
                    <div>
                        <h1 class="saas-heading-lg saas-mb-sm">
                            <span class="saas-text-accent">◆</span> Alpha Gas Solution
                        </h1>
                        <p class="saas-text-secondary">Real-Time System Monitor</p>
                    </div>
                    <div class="saas-text-right">
                        <div id="lastUpdated" class="saas-text-xs saas-text-muted saas-mb-sm">Never updated</div>
                        <button id="refreshAll" class="saas-btn saas-btn-primary">
                            🔄 Refresh System
                        </button>
                    </div>
                </div>
                <div class="saas-mt-lg">
                    <div id="systemOverview" class="saas-text-secondary">Initializing system...</div>
                </div>
            </div>
        
            <!-- System Status Grid -->
            <div class="saas-grid saas-grid-cols-1 lg:saas-grid-cols-3 saas-mb-xl">
                <!-- Left Column: Core System Status -->
                <div class="saas-flex saas-flex-col saas-gap-lg">
                    <div class="saas-card saas-fade-in">
                        <div class="saas-card-header">
                            <h3 class="saas-card-title">
                                <span id="coreIcon" class="saas-mr-sm">⏳</span>Core System
                            </h3>
                        </div>
                        <div id="coreStatus" class="saas-card-content"></div>
                    </div>
                    
                    <div class="saas-card saas-fade-in">
                        <div class="saas-card-header">
                            <h3 class="saas-card-title">
                                <span id="authIcon" class="saas-mr-sm">⏳</span>Authentication
                            </h3>
                        </div>
                        <div id="authStatus" class="saas-card-content"></div>
                    </div>
                    
                    <div class="saas-card saas-fade-in">
                        <h3 class="aurora-section-title mb-4">
                            <span id="dbIcon" class="mr-2">⏳</span>Database
                        </h3>
                        <div id="dbStatus" class="space-y-2"></div>
                    </div>
                </div>
                
                <!-- Middle Column: User Status & Actions -->
                <div class="space-y-4">
                    <div class="glass-card aurora-fade-in">
                        <h3 class="aurora-section-title mb-4">Current User</h3>
                        <div id="userInfo" class="space-y-2"></div>
                    </div>
                    
                    <div class="glass-card aurora-fade-in">
                        <h3 class="aurora-section-title mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button id="testAuth" class="aurora-btn aurora-btn-success w-full">
                                <span>Test Authentication</span>
                            </button>
                            <button id="testRoles" class="aurora-btn aurora-btn-secondary w-full">
                                <span>Test Role System</span>
                            </button>
                            <button id="testData" class="aurora-btn aurora-btn-warning w-full">
                                <span>Test Data Access</span>
                            </button>
                            <button id="runDiagnostics" class="aurora-btn aurora-btn-error w-full">
                                <span>Run Full Diagnostics</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="glass-card aurora-fade-in">
                        <h3 class="aurora-section-title mb-4">System Health</h3>
                        <div id="healthMetrics" class="space-y-2"></div>
                    </div>
                </div>
                
                <!-- Right Column: Issues & Iterations -->
                <div class="space-y-4">
                    <div class="glass-card aurora-fade-in">
                        <h3 class="aurora-section-title mb-4">
                            <span id="issuesIcon" class="mr-2">⏳</span>Issues Found
                        </h3>
                        <div id="issuesList" class="space-y-2">
                            <div class="text-white/60">No issues detected</div>
                        </div>
                    </div>
                    
                    <div class="glass-card aurora-fade-in">
                        <h3 class="aurora-section-title mb-4">Recommended Actions</h3>
                        <div id="recommendations" class="space-y-2">
                            <div class="text-white/60">Analyzing system...</div>
                        </div>
                    </div>
                    
                    <div class="glass-card aurora-fade-in">
                        <h3 class="aurora-section-title mb-4">Performance</h3>
                        <div id="performanceMetrics" class="space-y-2"></div>
                    </div>
                </div>
            </div>
        
            <!-- Console Output -->
            <div class="glass-panel aurora-fade-in">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="aurora-section-title">Real-Time Console</h3>
                    <button id="clearConsole" class="aurora-btn aurora-btn-ghost">
                        <span>Clear Console</span>
                    </button>
                </div>
                <div id="console" class="aurora-console"></div>
            </div>
        </div>
    </div>

    <script type="module">
        let supabase;
        let authManager;
        let roleManager;
        let systemHealth = {
            core: 'unknown',
            auth: 'unknown',
            database: 'unknown',
            overall: 'unknown'
        };
        let issues = [];
        let recommendations = [];
        
        // Console logging
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-aurora-primary',
                error: 'text-aurora-error',
                warn: 'text-aurora-warning',
                success: 'text-aurora-success'
            };
            
            const div = document.createElement('div');
            div.className = colors[type] || 'text-green-400';
            div.textContent = `[${timestamp}] ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }

        function updateIcon(iconId, status) {
            const icon = document.getElementById(iconId);
            const icons = {
                unknown: '⏳',
                healthy: '✅',
                warning: '⚠️',
                error: '❌',
                offline: '🔴'
            };
            if (icon) icon.textContent = icons[status] || '⏳';
        }

        function updateSection(sectionId, content, status = 'info') {
            const section = document.getElementById(sectionId);
            if (!section) return;
            
            const colors = {
                healthy: 'text-aurora-success',
                error: 'text-aurora-error',
                warning: 'text-aurora-warning',
                info: 'text-white/80'
            };
            
            if (Array.isArray(content)) {
                section.innerHTML = content.map(item => 
                    `<div class="${colors[item.status] || colors.info}">${item.message}</div>`
                ).join('');
            } else {
                section.innerHTML = `<div class="${colors[status] || colors.info}">${content}</div>`;
            }
        }

        async function initializeSystem() {
            try {
                log('🚀 Initializing RBAC monitoring system...', 'info');
                
                // Initialize Supabase
                const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
                supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
                log('✅ Supabase client initialized', 'success');

                // Import modules
                try {
                    const authModule = await import('./auth.js');
                    authManager = authModule.authManager;
                    log('✅ Auth manager loaded', 'success');
                } catch (e) {
                    log(`❌ Auth manager failed: ${e.message}`, 'error');
                    issues.push({ type: 'critical', message: 'Auth manager failed to load', detail: e.message });
                }

                try {
                    const roleModule = await import('./role-manager.js');
                    roleManager = roleModule.roleManager;
                    log('✅ Role manager loaded', 'success');
                } catch (e) {
                    log(`❌ Role manager failed: ${e.message}`, 'error');
                    issues.push({ type: 'critical', message: 'Role manager failed to load', detail: e.message });
                }

                systemHealth.core = authManager && roleManager ? 'healthy' : 'error';
                updateIcon('coreIcon', systemHealth.core);
                
                log('🔍 System initialization complete', 'info');
                
            } catch (error) {
                log(`❌ System initialization failed: ${error.message}`, 'error');
                systemHealth.core = 'error';
                updateIcon('coreIcon', systemHealth.core);
                issues.push({ type: 'critical', message: 'System initialization failed', detail: error.message });
            }
        }

        async function checkAuthentication() {
            try {
                log('🔐 Checking authentication system...', 'info');
                
                if (!authManager) {
                    throw new Error('Auth manager not available');
                }

                const authResults = [];
                
                // Check current user
                const currentUser = authManager.getCurrentUser();
                if (currentUser) {
                    authResults.push({ 
                        message: `✅ User: ${currentUser.email}`, 
                        status: 'healthy' 
                    });
                    log(`✅ Authenticated user: ${currentUser.email}`, 'success');
                    
                    // Check profile
                    try {
                        const profile = await authManager.getUserProfile();
                        if (profile) {
                            authResults.push({ 
                                message: `✅ Profile: ${profile.role} (${profile.status})`, 
                                status: 'healthy' 
                            });
                            log(`✅ User profile found: ${profile.role}`, 'success');
                        } else {
                            authResults.push({ 
                                message: '⚠️ Profile: Not found', 
                                status: 'warning' 
                            });
                            log('⚠️ User profile not found', 'warn');
                            issues.push({ type: 'warning', message: 'User profile missing', detail: 'User is authenticated but has no profile in database' });
                        }
                    } catch (e) {
                        authResults.push({ 
                            message: `❌ Profile error: ${e.message}`, 
                            status: 'error' 
                        });
                        log(`❌ Profile check failed: ${e.message}`, 'error');
                        issues.push({ type: 'error', message: 'Profile check failed', detail: e.message });
                    }
                } else {
                    authResults.push({ 
                        message: '⚠️ Guest mode (not authenticated)', 
                        status: 'warning' 
                    });
                    log('⚠️ No authenticated user - guest mode', 'warn');
                }

                // Test auth functions
                try {
                    const isAdmin = await authManager.isAdmin();
                    const isApproved = await authManager.isApproved();
                    const userRole = await authManager.getUserRole();
                    
                    authResults.push({ 
                        message: `✅ Functions: Admin(${isAdmin}), Approved(${isApproved}), Role(${userRole})`, 
                        status: 'healthy' 
                    });
                    log('✅ Auth functions working', 'success');
                } catch (e) {
                    authResults.push({ 
                        message: `❌ Function error: ${e.message}`, 
                        status: 'error' 
                    });
                    log(`❌ Auth functions failed: ${e.message}`, 'error');
                    issues.push({ type: 'error', message: 'Auth functions failed', detail: e.message });
                }

                updateSection('authStatus', authResults);
                systemHealth.auth = authResults.some(r => r.status === 'error') ? 'error' : 
                                   authResults.some(r => r.status === 'warning') ? 'warning' : 'healthy';
                updateIcon('authIcon', systemHealth.auth);
                
            } catch (error) {
                log(`❌ Authentication check failed: ${error.message}`, 'error');
                updateSection('authStatus', [{ message: `❌ ${error.message}`, status: 'error' }]);
                systemHealth.auth = 'error';
                updateIcon('authIcon', systemHealth.auth);
                issues.push({ type: 'critical', message: 'Authentication system failed', detail: error.message });
            }
        }

        async function checkDatabase() {
            try {
                log('💾 Checking database connectivity...', 'info');
                
                const dbResults = [];
                
                // Test basic connection
                const { data: connectionTest, error: connectionError } = await supabase
                    .from('user_profiles')
                    .select('count')
                    .limit(1);

                if (connectionError) {
                    dbResults.push({ 
                        message: `❌ Connection: ${connectionError.message}`, 
                        status: 'error' 
                    });
                    log(`❌ Database connection failed: ${connectionError.message}`, 'error');
                    issues.push({ type: 'critical', message: 'Database connection failed', detail: connectionError.message });
                } else {
                    dbResults.push({ 
                        message: '✅ Connection: Active', 
                        status: 'healthy' 
                    });
                    log('✅ Database connection successful', 'success');
                }

                // Test table access
                const tables = ['user_profiles', 'gas_analytes', 'admin_audit_log'];
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('count')
                            .limit(1);
                        
                        if (error) {
                            dbResults.push({ 
                                message: `❌ ${table}: ${error.message}`, 
                                status: 'error' 
                            });
                            log(`❌ Table ${table} error: ${error.message}`, 'error');
                            issues.push({ type: 'error', message: `Table ${table} access failed`, detail: error.message });
                        } else {
                            dbResults.push({ 
                                message: `✅ ${table}: Accessible`, 
                                status: 'healthy' 
                            });
                            log(`✅ Table ${table} accessible`, 'success');
                        }
                    } catch (e) {
                        dbResults.push({ 
                            message: `❌ ${table}: Exception`, 
                            status: 'error' 
                        });
                        log(`❌ Table ${table} exception: ${e.message}`, 'error');
                        issues.push({ type: 'error', message: `Table ${table} exception`, detail: e.message });
                    }
                }

                updateSection('dbStatus', dbResults);
                systemHealth.database = dbResults.some(r => r.status === 'error') ? 'error' : 'healthy';
                updateIcon('dbIcon', systemHealth.database);
                
            } catch (error) {
                log(`❌ Database check failed: ${error.message}`, 'error');
                updateSection('dbStatus', [{ message: `❌ ${error.message}`, status: 'error' }]);
                systemHealth.database = 'error';
                updateIcon('dbIcon', systemHealth.database);
                issues.push({ type: 'critical', message: 'Database system failed', detail: error.message });
            }
        }

        async function checkRoleSystem() {
            try {
                log('👥 Checking role management system...', 'info');
                
                if (!roleManager) {
                    throw new Error('Role manager not available');
                }

                await roleManager.init();
                log('✅ Role manager initialized', 'success');

                const role = roleManager.getRole();
                const status = roleManager.getStatus();
                log(`✅ Current role: ${role} (${status})`, 'success');

                // Test role functions
                const permissions = roleManager.getPermissions();
                const restrictions = roleManager.getUIRestrictions();
                
                log(`✅ Permissions configured: ${Object.keys(permissions).length}`, 'success');
                log(`✅ UI restrictions configured: ${Object.keys(restrictions).length}`, 'success');

            } catch (error) {
                log(`❌ Role system check failed: ${error.message}`, 'error');
                issues.push({ type: 'error', message: 'Role system failed', detail: error.message });
            }
        }

        async function updateUserInfo() {
            try {
                const userResults = [];
                
                if (authManager) {
                    const currentUser = authManager.getCurrentUser();
                    if (currentUser) {
                        userResults.push(`<strong>Email:</strong> ${currentUser.email}`);
                        userResults.push(`<strong>ID:</strong> ${currentUser.id}`);
                        
                        const profile = await authManager.getUserProfile();
                        if (profile) {
                            userResults.push(`<strong>Role:</strong> ${profile.role}`);
                            userResults.push(`<strong>Status:</strong> ${profile.status}`);
                            userResults.push(`<strong>Created:</strong> ${new Date(profile.created_at).toLocaleDateString()}`);
                        }
                    } else {
                        userResults.push('<strong>Status:</strong> Guest (Not authenticated)');
                        userResults.push('<strong>Role:</strong> guest');
                    }
                } else {
                    userResults.push('<strong>Status:</strong> System not initialized');
                }

                if (roleManager) {
                    await roleManager.init();
                    const role = roleManager.getRole();
                    const status = roleManager.getStatus();
                    userResults.push(`<strong>Current Role:</strong> ${role}`);
                    userResults.push(`<strong>Current Status:</strong> ${status}`);
                }

                document.getElementById('userInfo').innerHTML = userResults.join('<br>');
                
            } catch (error) {
                document.getElementById('userInfo').innerHTML = `<span class="text-red-600">Error: ${error.message}</span>`;
            }
        }

        async function generateRecommendations() {
            recommendations = [];
            
            // Analyze issues and suggest improvements
            const criticalIssues = issues.filter(i => i.type === 'critical');
            const errorIssues = issues.filter(i => i.type === 'error');
            const warningIssues = issues.filter(i => i.type === 'warning');

            if (criticalIssues.length > 0) {
                recommendations.push({
                    priority: 'high',
                    action: 'Fix Critical Issues',
                    description: `${criticalIssues.length} critical issues need immediate attention`,
                    details: criticalIssues.map(i => i.message)
                });
            }

            if (errorIssues.length > 0) {
                recommendations.push({
                    priority: 'medium',
                    action: 'Address System Errors',
                    description: `${errorIssues.length} errors affecting functionality`,
                    details: errorIssues.map(i => i.message)
                });
            }

            if (warningIssues.length > 0) {
                recommendations.push({
                    priority: 'low',
                    action: 'Resolve Warnings',
                    description: `${warningIssues.length} warnings for optimization`,
                    details: warningIssues.map(i => i.message)
                });
            }

            // System improvement recommendations
            if (systemHealth.auth === 'healthy' && systemHealth.database === 'healthy') {
                recommendations.push({
                    priority: 'low',
                    action: 'Performance Optimization',
                    description: 'System is healthy - consider performance improvements',
                    details: ['Add caching for role checks', 'Optimize database queries', 'Add loading states']
                });
            }

            if (issues.length === 0) {
                recommendations.push({
                    priority: 'low',
                    action: 'Enhancement Features',
                    description: 'No issues found - ready for feature enhancements',
                    details: ['Add email notifications', 'Implement audit logging', 'Add user activity tracking']
                });
            }

            // Update UI
            const recDiv = document.getElementById('recommendations');
            if (recommendations.length === 0) {
                recDiv.innerHTML = '<div class="text-green-600">✅ System performing optimally</div>';
            } else {
                const html = recommendations.map(rec => {
                    const priorityColors = {
                        high: 'text-red-600 bg-red-50 border-red-200',
                        medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
                        low: 'text-blue-600 bg-blue-50 border-blue-200'
                    };
                    return `
                        <div class="p-2 border rounded ${priorityColors[rec.priority]}">
                            <div class="font-medium">${rec.action}</div>
                            <div class="text-xs">${rec.description}</div>
                        </div>
                    `;
                }).join('');
                recDiv.innerHTML = html;
            }
        }

        async function updateIssuesList() {
            const issuesDiv = document.getElementById('issuesList');
            const issuesIcon = document.getElementById('issuesIcon');
            
            if (issues.length === 0) {
                issuesDiv.innerHTML = '<div class="text-green-600">✅ No issues detected</div>';
                updateIcon('issuesIcon', 'healthy');
            } else {
                const html = issues.map(issue => {
                    const colors = {
                        critical: 'text-red-600',
                        error: 'text-orange-600',
                        warning: 'text-yellow-600'
                    };
                    return `<div class="${colors[issue.type]}">${issue.message}</div>`;
                }).join('');
                issuesDiv.innerHTML = html;
                
                const hasCritical = issues.some(i => i.type === 'critical');
                const hasError = issues.some(i => i.type === 'error');
                updateIcon('issuesIcon', hasCritical ? 'error' : hasError ? 'warning' : 'healthy');
            }
        }

        async function runFullDiagnostics() {
            log('🔄 Starting full system diagnostics...', 'info');
            
            // Clear previous results
            issues = [];
            systemHealth = { core: 'unknown', auth: 'unknown', database: 'unknown', overall: 'unknown' };
            
            // Update UI to show checking state
            ['coreIcon', 'authIcon', 'dbIcon', 'issuesIcon'].forEach(id => updateIcon(id, 'unknown'));
            
            try {
                await initializeSystem();
                await checkAuthentication();
                await checkDatabase();
                await checkRoleSystem();
                await updateUserInfo();
                await updateIssuesList();
                await generateRecommendations();
                
                // Update overall health
                const healthValues = Object.values(systemHealth).filter(h => h !== 'unknown');
                if (healthValues.includes('error')) {
                    systemHealth.overall = 'error';
                } else if (healthValues.includes('warning')) {
                    systemHealth.overall = 'warning';
                } else {
                    systemHealth.overall = 'healthy';
                }
                
                // Update system overview
                const overviewDiv = document.getElementById('systemOverview');
                const statusText = {
                    healthy: '✅ All systems operational',
                    warning: '⚠️ Some issues detected',
                    error: '❌ Critical issues found'
                };
                overviewDiv.textContent = statusText[systemHealth.overall] || 'Status unknown';
                
                // Update last updated
                document.getElementById('lastUpdated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
                
                log('✅ Full diagnostics completed', 'success');
                
            } catch (error) {
                log(`❌ Diagnostics failed: ${error.message}`, 'error');
                systemHealth.overall = 'error';
            }
        }

        // Event listeners
        document.getElementById('refreshAll').addEventListener('click', runFullDiagnostics);
        document.getElementById('testAuth').addEventListener('click', checkAuthentication);
        document.getElementById('testRoles').addEventListener('click', checkRoleSystem);
        document.getElementById('testData').addEventListener('click', checkDatabase);
        document.getElementById('runDiagnostics').addEventListener('click', runFullDiagnostics);
        document.getElementById('clearConsole').addEventListener('click', () => {
            document.getElementById('console').innerHTML = '';
        });

        // Initialize on load
        window.addEventListener('load', () => {
            setTimeout(runFullDiagnostics, 1000);
        });

        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (document.hidden) return; // Don't refresh when tab is not visible
            runFullDiagnostics();
        }, 30000);

        // Handle errors
        window.addEventListener('error', (event) => {
            log(`Global Error: ${event.error.message}`, 'error');
            issues.push({ type: 'error', message: 'Global error', detail: event.error.message });
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`Unhandled Promise Rejection: ${event.reason}`, 'error');
            issues.push({ type: 'error', message: 'Unhandled promise rejection', detail: event.reason });
        });
    </script>
</body>
</html>
