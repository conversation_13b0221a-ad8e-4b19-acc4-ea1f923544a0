// Role-based UI management for Alpha Gas Solution
import { authManager } from './auth.js';
import { performanceOptimizer } from './performance-optimizer.js';
import { emailService } from './email-service.js';

export class RoleManager {
    constructor() {
        this.currentRole = 'guest';
        this.currentStatus = 'guest';
        this.isInitialized = false;
        this.lastRoleCheck = null;
        this.roleCheckInterval = 60000; // Check role every minute
    }

    // Initialize role management with enhanced services
    async init() {
        console.log('🔧 Initializing Role Manager...');
        
        if (!authManager.isAuthenticated()) {
            this.currentRole = 'guest';
            this.currentStatus = 'guest';
            this.isInitialized = true;
            console.log('👤 Guest user detected');
            return;
        }

        try {
            // Initialize performance optimizer integration
            await performanceOptimizer.init();
            
            // Get user profile with caching
            const profile = await authManager.getUserProfile();
            if (profile) {
                this.currentRole = profile.role;
                this.currentStatus = profile.status;
                console.log(`✅ Role Manager initialized - Role: ${this.currentRole}, Status: ${this.currentStatus}`);
            } else {
                this.currentRole = 'guest';
                this.currentStatus = 'guest';
                console.log('⚠️ No profile found, defaulting to guest');
            }
            
            // Start periodic role check
            this.startRoleMonitoring();
            
            this.isInitialized = true;
        } catch (error) {
            console.error('❌ Error initializing role manager:', error);
            this.currentRole = 'guest';
            this.currentStatus = 'guest';
            this.isInitialized = true;
        }
    }

    // Start monitoring role changes
    startRoleMonitoring() {
        setInterval(async () => {
            await this.checkRoleChanges();
        }, this.roleCheckInterval);
    }

    // Check for role changes and update UI accordingly
    async checkRoleChanges() {
        if (!authManager.isAuthenticated()) return;
        
        try {
            const profile = await authManager.getUserProfile();
            if (profile) {
                const oldRole = this.currentRole;
                const oldStatus = this.currentStatus;
                
                if (profile.role !== oldRole || profile.status !== oldStatus) {
                    console.log(`🔄 Role change detected: ${oldRole}/${oldStatus} -> ${profile.role}/${profile.status}`);
                    
                    this.currentRole = profile.role;
                    this.currentStatus = profile.status;
                    
                    // Trigger UI updates
                    await this.handleRoleTransition(oldRole, oldStatus, profile.role, profile.status);
                    
                    // Clear cache for updated user
                    await authManager.clearUserCache();
                }
            }
        } catch (error) {
            console.error('Role change check failed:', error);
        }
    }

    // Handle role transition smoothly
    async handleRoleTransition(oldRole, oldStatus, newRole, newStatus) {
        // Show notification to user
        this.showRoleChangeNotification(oldRole, oldStatus, newRole, newStatus);
        
        // Update UI elements
        await this.updateRoleBasedUI();
        
        // Refresh page if significant role change
        if (this.requiresPageRefresh(oldRole, newRole)) {
            setTimeout(() => {
                window.location.reload();
            }, 3000); // Give user time to read notification
        }
    }

    // Check if role change requires page refresh
    requiresPageRefresh(oldRole, newRole) {
        const significantChanges = [
            ['guest', 'user'],
            ['guest', 'admin'],
            ['user', 'admin'],
            ['admin', 'user']
        ];
        
        return significantChanges.some(([from, to]) => 
            oldRole === from && newRole === to
        );
    }

    // Show role change notification
    showRoleChangeNotification(oldRole, oldStatus, newRole, newStatus) {
        const message = this.getRoleChangeMessage(oldRole, oldStatus, newRole, newStatus);
        this.showNotification(message, 'info', 8000);
    }

    // Get appropriate message for role change
    getRoleChangeMessage(oldRole, oldStatus, newRole, newStatus) {
        if (oldStatus === 'pending' && newStatus === 'approved') {
            return `🎉 Great news! Your account has been approved. You now have ${newRole} access.`;
        } else if (oldStatus === 'pending' && newStatus === 'rejected') {
            return `❌ Your account application was not approved. Please contact support for more information.`;
        } else if (oldRole !== newRole) {
            return `🔄 Your role has been updated from ${oldRole} to ${newRole}.`;
        } else if (oldStatus !== newStatus) {
            return `📋 Your account status has been updated to ${newStatus}.`;
        }
        return `🔄 Your account information has been updated.`;
    }

    // Get current user role
    getRole() {
        return this.currentRole;
    }

    // Get current user status
    getStatus() {
        return this.currentStatus;
    }

    // Check if user has access to feature
    hasAccess(feature) {
        switch (feature) {
            case 'admin_dashboard':
                return this.currentRole === 'admin' && this.currentStatus === 'approved';
            case 'data_persistence':
                return this.currentStatus === 'approved';
            case 'guest_features':
                return true; // Everyone has access to guest features
            case 'user_features':
                return this.currentStatus === 'approved';
            case 'pending_notice':
                return this.currentStatus === 'pending';
            default:
                return false;
        }
    }

    // Get UI restrictions based on role
    getUIRestrictions() {
        const restrictions = {
            showPendingNotice: this.hasAccess('pending_notice'),
            showAdminLink: this.hasAccess('admin_dashboard'),
            allowDataPersistence: this.hasAccess('data_persistence'),
            showUserFeatures: this.hasAccess('user_features'),
            showGuestWarning: this.currentRole === 'guest'
        };

        return restrictions;
    }

    // Get role-specific messages
    getRoleMessages() {
        switch (this.currentStatus) {
            case 'pending':
                return {
                    type: 'warning',
                    title: 'Account Pending Approval',
                    message: 'Your account is awaiting admin approval. You currently have guest-level access.',
                    action: 'Contact admin for faster approval'
                };
            case 'approved':
                return {
                    type: 'success',
                    title: `Welcome, ${this.currentRole.charAt(0).toUpperCase() + this.currentRole.slice(1)}!`,
                    message: 'You have full access to all features.',
                    action: this.currentRole === 'admin' ? 'Access Admin Dashboard' : null
                };
            case 'rejected':
                return {
                    type: 'error',
                    title: 'Account Access Denied',
                    message: 'Your account request was not approved. You have guest-level access.',
                    action: 'Contact admin for assistance'
                };
            case 'suspended':
                return {
                    type: 'error',
                    title: 'Account Suspended',
                    message: 'Your account has been suspended. You have guest-level access.',
                    action: 'Contact admin for assistance'
                };
            default:
                return {
                    type: 'info',
                    title: 'Guest Mode',
                    message: 'You\'re using guest mode. Data saves locally only.',
                    action: 'Create account for cloud sync'
                };
        }
    }

    // Setup role-based UI elements
    setupUI() {
        const restrictions = this.getUIRestrictions();
        const messages = this.getRoleMessages();

        // Show/hide role notification
        this.showRoleNotification(messages);

        // Setup menu based on role
        this.setupRoleMenu(restrictions);

        // Enable/disable features
        this.applyFeatureRestrictions(restrictions);

        return { restrictions, messages };
    }

    // Show role-based notification
    showRoleNotification(messages) {
        const existing = document.getElementById('roleNotification');
        if (existing) existing.remove();

        if (messages.type === 'info' && this.currentRole === 'guest') {
            // Don't show notification for regular guest users unless they need to know something
            return;
        }

        const notification = document.createElement('div');
        notification.id = 'roleNotification';
        notification.className = `saas-card saas-mb-md ${this.getNotificationStyle(messages.type)}`;
        
        notification.innerHTML = `
            <div class="saas-flex saas-items-start">
                <span class="saas-mr-sm">${this.getNotificationIcon(messages.type)}</span>
                <div class="saas-flex-1">
                    <div class="saas-text-base saas-text-primary">${messages.title}</div>
                    <div class="saas-text-sm saas-text-secondary saas-mt-sm">${messages.message}</div>
                    ${messages.action ? `
                        <button class="saas-btn saas-btn-ghost saas-btn-sm saas-mt-sm" onclick="roleManager.handleNotificationAction('${messages.type}')">
                            ${messages.action}
                        </button>
                    ` : ''}
                </div>
            </div>
        `;

        // Insert after header
        const header = document.querySelector('.header-subtitle') || document.querySelector('h1');
        if (header && header.parentNode) {
            header.parentNode.insertBefore(notification, header.nextSibling);
        }
    }

    // Setup role-based menu
    setupRoleMenu(restrictions) {
        // Add admin link if user is admin
        if (restrictions.showAdminLink) {
            this.addAdminMenuLink();
        }

        // Update user menu based on status
        this.updateUserMenuStatus();
    }

    // Add admin dashboard link to menu
    addAdminMenuLink() {
        const userMenu = document.getElementById('userMenu') || document.getElementById('guestMenu');
        if (!userMenu) return;

        const adminLink = document.createElement('a');
        adminLink.href = './admin-dashboard.html';
        adminLink.className = 'saas-btn saas-btn-ghost saas-btn-sm saas-text-sm';
        adminLink.innerHTML = '🛡️ Admin Dashboard';

        const hr = userMenu.querySelector('hr');
        if (hr) {
            hr.parentNode.insertBefore(adminLink, hr);
        }
    }

    // Update user menu to show status
    updateUserMenuStatus() {
        const statusElement = document.getElementById('userStatus') || document.getElementById('guestStatus');
        if (statusElement) {
            const status = this.currentStatus.charAt(0).toUpperCase() + this.currentStatus.slice(1);
            statusElement.textContent = `${this.currentRole.charAt(0).toUpperCase() + this.currentRole.slice(1)} (${status})`;
        }
    }

    // Apply feature restrictions
    applyFeatureRestrictions(restrictions) {
        // Show guest warning if needed
        if (restrictions.showGuestWarning && !restrictions.allowDataPersistence) {
            this.showDataPersistenceWarning();
        }

        // Disable features for non-approved users
        if (!restrictions.allowDataPersistence) {
            this.restrictDataFeatures();
        }
    }

    // Show data persistence warning
    showDataPersistenceWarning() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (!form.querySelector('.persistence-warning')) {
                const warning = document.createElement('div');
                warning.className = 'persistence-warning saas-card saas-mb-sm';
                warning.style.borderColor = 'var(--saas-warning)';
                warning.style.backgroundColor = 'rgba(217, 119, 6, 0.05)';
                warning.innerHTML = '<span style="color: var(--saas-warning);">⚠️</span> <strong>Guest Mode:</strong> Your data will only be saved locally and may be lost.';
                form.insertBefore(warning, form.firstChild);
            }
        });
    }

    // Restrict data features for non-approved users
    restrictDataFeatures() {
        // This could disable certain features or show warnings
        // For now, we'll just ensure the user knows about limitations
        console.log('User has limited data persistence capabilities');
    }

    // Handle notification actions
    handleNotificationAction(type) {
        switch (type) {
            case 'success':
                if (this.currentRole === 'admin') {
                    window.location.href = './admin-dashboard.html';
                }
                break;
            case 'warning':
            case 'error':
                // Could open a contact form or redirect to help
                alert('Please contact the administrator for assistance with your account.');
                break;
            case 'info':
                window.location.href = './direct-login-simple.html';
                break;
        }
    }

    // Helper methods for notification styling
    getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'bg-zinc-900 border-green-800 text-green-400';
            case 'warning':
                return 'bg-zinc-900 border-amber-800 text-amber-400';
            case 'error':
                return 'bg-zinc-900 border-red-800 text-red-400';
            case 'info':
            default:
                return 'bg-zinc-900 border-blue-800 text-blue-400';
        }
    }

    getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return '✅';
            case 'warning':
                return '⚠️';
            case 'error':
                return '❌';
            case 'info':
            default:
                return 'ℹ️';
        }
    }

    // Refresh role data
    async refresh() {
        await this.init();
        this.setupUI();
    }
}

// Create global role manager instance
export const roleManager = new RoleManager();
