<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Transition Tester</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">🔄 Role Transition Tester</h1>
        <p class="text-gray-600 mb-6">Test role changes and transitions for the RBAC system</p>

        <!-- Current User Info -->
        <div id="currentUserInfo" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="font-semibold text-blue-800 mb-2">Current User</h3>
            <div id="userDetails" class="text-sm text-blue-700">Loading...</div>
        </div>

        <!-- Test Controls -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <!-- Role Simulation -->
            <div class="border rounded-lg p-4">
                <h3 class="font-semibold mb-3">🎭 Simulate Role</h3>
                <div class="space-y-2">
                    <button id="simulateGuest" class="w-full bg-gray-500 text-white px-3 py-2 rounded text-sm">
                        Simulate Guest User
                    </button>
                    <button id="simulatePending" class="w-full bg-yellow-500 text-white px-3 py-2 rounded text-sm">
                        Simulate Pending User
                    </button>
                    <button id="simulateApproved" class="w-full bg-green-500 text-white px-3 py-2 rounded text-sm">
                        Simulate Approved User
                    </button>
                    <button id="simulateAdmin" class="w-full bg-red-500 text-white px-3 py-2 rounded text-sm">
                        Simulate Admin User
                    </button>
                </div>
            </div>

            <!-- Database Tests -->
            <div class="border rounded-lg p-4">
                <h3 class="font-semibold mb-3">🗄️ Database Tests</h3>
                <div class="space-y-2">
                    <button id="checkUserProfiles" class="w-full bg-blue-500 text-white px-3 py-2 rounded text-sm">
                        Check User Profiles
                    </button>
                    <button id="testRLSPolicies" class="w-full bg-purple-500 text-white px-3 py-2 rounded text-sm">
                        Test RLS Policies
                    </button>
                    <button id="testDataAccess" class="w-full bg-indigo-500 text-white px-3 py-2 rounded text-sm">
                        Test Data Access
                    </button>
                    <button id="viewAuditLog" class="w-full bg-orange-500 text-white px-3 py-2 rounded text-sm">
                        View Audit Log
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="border rounded-lg p-4">
            <h3 class="font-semibold mb-3">📋 Test Results</h3>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto bg-gray-50 p-3 rounded text-sm font-mono"></div>
            <button id="clearResults" class="mt-3 bg-gray-500 text-white px-3 py-1 rounded text-sm">
                Clear Results
            </button>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold mb-2">Quick Actions</h3>
            <div class="flex flex-wrap gap-2 text-sm">
                <a href="./guest-mode-simple.html" target="_blank" class="text-blue-600 hover:underline">Test Guest Mode</a>
                <a href="./direct-login-simple.html" target="_blank" class="text-blue-600 hover:underline">Test Registration</a>
                <a href="./admin-dashboard.html" target="_blank" class="text-blue-600 hover:underline">Test Admin Dashboard</a>
                <a href="./app.html" target="_blank" class="text-blue-600 hover:underline">Test Main App</a>
            </div>
        </div>
    </div>

    <script type="module">
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Mock role manager for simulation
        let mockRoleManager = {
            currentRole: 'guest',
            currentStatus: 'guest',
            
            setRole(role, status) {
                this.currentRole = role;
                this.currentStatus = status;
            },
            
            getRole() { return this.currentRole; },
            getStatus() { return this.currentStatus; },
            
            hasAccess(feature) {
                switch (feature) {
                    case 'admin_dashboard':
                        return this.currentRole === 'admin' && this.currentStatus === 'approved';
                    case 'data_persistence':
                        return this.currentStatus === 'approved';
                    case 'user_features':
                        return this.currentStatus === 'approved';
                    default:
                        return true;
                }
            },
            
            getUIRestrictions() {
                return {
                    showPendingNotice: this.currentStatus === 'pending',
                    showAdminLink: this.hasAccess('admin_dashboard'),
                    allowDataPersistence: this.hasAccess('data_persistence'),
                    showUserFeatures: this.hasAccess('user_features'),
                    showGuestWarning: this.currentRole === 'guest'
                };
            },
            
            getRoleMessages() {
                switch (this.currentStatus) {
                    case 'pending':
                        return {
                            type: 'warning',
                            title: 'Account Pending Approval',
                            message: 'Your account is awaiting admin approval.',
                            action: 'Contact admin for faster approval'
                        };
                    case 'approved':
                        return {
                            type: 'success',
                            title: `Welcome, ${this.currentRole}!`,
                            message: 'You have full access to all features.',
                            action: this.currentRole === 'admin' ? 'Access Admin Dashboard' : null
                        };
                    case 'rejected':
                        return {
                            type: 'error',
                            title: 'Account Access Denied',
                            message: 'Your account request was not approved.',
                            action: 'Contact admin for assistance'
                        };
                    default:
                        return {
                            type: 'info',
                            title: 'Guest Mode',
                            message: 'You are using guest mode.',
                            action: 'Create account for cloud sync'
                        };
                }
            }
        };

        function log(message, type = 'info') {
            const container = document.getElementById('testResults');
            const div = document.createElement('div');
            
            const typeColors = {
                pass: 'text-green-700',
                fail: 'text-red-700',
                warn: 'text-yellow-700',
                info: 'text-blue-700'
            };
            
            div.className = typeColors[type] || typeColors.info;
            div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function loadCurrentUserInfo() {
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    document.getElementById('userDetails').innerHTML = `❌ Auth error: ${error.message}`;
                    return;
                }

                if (session?.user) {
                    const user = session.user;
                    
                    // Get user profile
                    const { data: profile, error: profileError } = await supabase
                        .from('user_profiles')
                        .select('*')
                        .eq('user_id', user.id)
                        .single();

                    const profileInfo = profile ? 
                        `${profile.role} (${profile.status}) - Created: ${new Date(profile.created_at).toLocaleDateString()}` : 
                        'No profile found';
                    
                    document.getElementById('userDetails').innerHTML = `
                        <strong>Email:</strong> ${user.email}<br/>
                        <strong>Profile:</strong> ${profileInfo}<br/>
                        <strong>User ID:</strong> ${user.id}
                    `;
                    
                    // Update mock role manager with real data
                    if (profile) {
                        mockRoleManager.setRole(profile.role, profile.status);
                    }
                } else {
                    document.getElementById('userDetails').innerHTML = 'Not authenticated (testing in guest mode)';
                    mockRoleManager.setRole('guest', 'guest');
                }
            } catch (error) {
                document.getElementById('userDetails').innerHTML = `Error: ${error.message}`;
            }
        }

        function testRoleSimulation(role, status) {
            log(`🎭 Simulating role: ${role} (${status})`, 'info');
            
            mockRoleManager.setRole(role, status);
            
            // Test access controls
            const features = ['admin_dashboard', 'data_persistence', 'user_features'];
            features.forEach(feature => {
                const hasAccess = mockRoleManager.hasAccess(feature);
                log(`  ${feature}: ${hasAccess ? 'GRANTED' : 'DENIED'}`, hasAccess ? 'pass' : 'info');
            });
            
            // Test UI restrictions
            const restrictions = mockRoleManager.getUIRestrictions();
            log(`  UI Restrictions: ${JSON.stringify(restrictions)}`, 'info');
            
            // Test role messages
            const messages = mockRoleManager.getRoleMessages();
            log(`  Message: ${messages.title} - ${messages.message}`, 'info');
            
            log(`✅ Role simulation complete for ${role}`, 'pass');
        }

        async function checkUserProfiles() {
            log('🔍 Checking user profiles in database...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('user_profiles')
                    .select('email, role, status, created_at')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) {
                    log(`❌ Error querying user profiles: ${error.message}`, 'fail');
                    return;
                }

                log(`✅ Found ${data.length} user profiles:`, 'pass');
                data.forEach((profile, index) => {
                    log(`  ${index + 1}. ${profile.email} - ${profile.role} (${profile.status})`, 'info');
                });

            } catch (error) {
                log(`❌ User profiles check failed: ${error.message}`, 'fail');
            }
        }

        async function testRLSPolicies() {
            log('🔐 Testing Row Level Security policies...', 'info');
            
            try {
                // Test gas_analytes RLS
                const { data: analytesData, error: analytesError } = await supabase
                    .from('gas_analytes')
                    .select('name, user_id, is_shared, is_custom')
                    .limit(5);

                if (analytesError) {
                    log(`🔒 gas_analytes RLS: ${analytesError.message}`, 'warn');
                } else {
                    log(`✅ gas_analytes accessible (${analytesData.length} records)`, 'pass');
                }

                // Test user_profiles RLS  
                const { data: profilesData, error: profilesError } = await supabase
                    .from('user_profiles')
                    .select('email, role')
                    .limit(3);

                if (profilesError) {
                    log(`🔒 user_profiles RLS: ${profilesError.message}`, 'warn');
                } else {
                    log(`✅ user_profiles accessible (${profilesData.length} records)`, 'pass');
                }

                // Test admin_audit_log RLS
                const { data: auditData, error: auditError } = await supabase
                    .from('admin_audit_log')
                    .select('action, created_at')
                    .limit(3);

                if (auditError) {
                    log(`🔒 admin_audit_log RLS: ${auditError.message}`, 'warn');
                } else {
                    log(`✅ admin_audit_log accessible (${auditData.length} records)`, 'pass');
                }

            } catch (error) {
                log(`❌ RLS policy test failed: ${error.message}`, 'fail');
            }
        }

        async function testDataAccess() {
            log('💾 Testing data access patterns...', 'info');
            
            try {
                // Test creating analyte with hybrid properties
                const testAnalyte = {
                    name: `AccessTest_${Date.now()}`,
                    current_ranges: [{ min: 10, max: 100, label: "Test" }],
                    target_ranges: [{ min: 1, max: 1000, label: "Test" }],
                    gap_notes: "Access test analyte",
                    is_custom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                const { data: { session } } = await supabase.auth.getSession();
                
                if (session?.user) {
                    testAnalyte.user_id = session.user.id;
                    
                    const { data, error } = await supabase
                        .from('gas_analytes')
                        .insert(testAnalyte)
                        .select();

                    if (error) {
                        log(`⚠️ Data insertion: ${error.message}`, 'warn');
                    } else {
                        log(`✅ Data inserted successfully (ID: ${data[0].id})`, 'pass');
                        
                        // Clean up
                        await supabase.from('gas_analytes').delete().eq('id', data[0].id);
                        log(`🧹 Test data cleaned up`, 'info');
                    }
                } else {
                    log(`⚠️ Data access test skipped (not authenticated)`, 'warn');
                }

                // Test filtering logic
                const testAnalytes = [{
                    ...testAnalyte,
                    isCustom: true, // Note: this is the client-side property
                }];
                
                const filtered = testAnalytes.filter(a => !a.is_shared && a.isCustom);
                log(`✅ Filter test: ${filtered.length}/${testAnalytes.length} analytes passed filter`, 'pass');

            } catch (error) {
                log(`❌ Data access test failed: ${error.message}`, 'fail');
            }
        }

        async function viewAuditLog() {
            log('📋 Viewing recent audit log entries...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('admin_audit_log')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) {
                    log(`❌ Audit log error: ${error.message}`, 'fail');
                    return;
                }

                if (data.length === 0) {
                    log(`ℹ️ No audit log entries found`, 'info');
                    return;
                }

                log(`✅ Found ${data.length} audit log entries:`, 'pass');
                data.forEach((entry, index) => {
                    const date = new Date(entry.created_at).toLocaleString();
                    log(`  ${index + 1}. [${date}] ${entry.action} by ${entry.admin_email}`, 'info');
                    if (entry.details) {
                        log(`     Details: ${entry.details}`, 'info');
                    }
                });

            } catch (error) {
                log(`❌ Audit log view failed: ${error.message}`, 'fail');
            }
        }

        // Event listeners
        document.getElementById('simulateGuest').addEventListener('click', () => 
            testRoleSimulation('guest', 'guest'));
        
        document.getElementById('simulatePending').addEventListener('click', () => 
            testRoleSimulation('user', 'pending'));
        
        document.getElementById('simulateApproved').addEventListener('click', () => 
            testRoleSimulation('user', 'approved'));
        
        document.getElementById('simulateAdmin').addEventListener('click', () => 
            testRoleSimulation('admin', 'approved'));

        document.getElementById('checkUserProfiles').addEventListener('click', checkUserProfiles);
        document.getElementById('testRLSPolicies').addEventListener('click', testRLSPolicies);
        document.getElementById('testDataAccess').addEventListener('click', testDataAccess);
        document.getElementById('viewAuditLog').addEventListener('click', viewAuditLog);

        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('testResults').innerHTML = '';
        });

        // Initialize
        loadCurrentUserInfo();
    </script>
</body>
</html>
