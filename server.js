const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname)));

// API endpoint for Gemini AI analysis
app.post('/api/analyze-calibration', async (req, res) => {
    console.log('API endpoint called with body:', req.body);
    
    try {
        // Import fetch dynamically
        const fetch = (await import('node-fetch')).default;
        
        const { prompt } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }

        // Get API key from environment variable
        const apiKey = process.env.GEMINI_API_KEY;
        console.log('API Key configured:', apiKey ? 'Yes' : 'No');
        
        if (!apiKey) {
            console.error('GEMINI_API_KEY not found in environment variables');
            return res.status(500).json({ error: 'API key not configured' });
        }

        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = {
            contents: chatHistory,
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "ARRAY",
                    items: {
                        type: "OBJECT",
                        properties: {
                            "calibrationGasName": { "type": "STRING" },
                            "canAnalyzeFully": { "type": "BOOLEAN" },
                            "analysisDetails": { "type": "STRING" },
                            "problematicComponents": {
                                "type": "ARRAY",
                                "items": {
                                    "type": "OBJECT",
                                    "properties": {
                                        "compoundName": { "type": "STRING" },
                                        "concentrationPPM": { "type": "NUMBER" },
                                        "reason": { "type": "STRING" }
                                    },
                                    required: ["compoundName", "concentrationPPM", "reason"]
                                }
                            }
                        },
                        required: ["calibrationGasName", "canAnalyzeFully", "analysisDetails", "problematicComponents"]
                    }
                }
            }
        };

        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
        console.log('Making request to Gemini API...');

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        console.log('Gemini API response status:', response.status);

        if (!response.ok) {
            const errorBody = await response.text();
            console.error('Gemini API error:', errorBody);
            throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
        }

        const result = await response.json();
        console.log('Successful API response received');
        res.json(result);

    } catch (error) {
        console.error('Error calling Gemini API:', error);
        res.status(500).json({ error: 'Failed to analyze calibration data', details: error.message });
    }
});

// Debug endpoint
app.get('/api/debug', (req, res) => {
    console.log('Debug endpoint called');
    const apiKey = process.env.GEMINI_API_KEY;
    const hasApiKey = !!apiKey;
    const apiKeyLength = apiKey ? apiKey.length : 0;
    
    res.json({ 
        message: 'Debug endpoint working',
        method: req.method,
        url: req.url,
        hasApiKey,
        apiKeyLength,
        env: Object.keys(process.env).filter(key => key.includes('GEMINI')),
        timestamp: new Date().toISOString(),
        headers: req.headers
    });
});

// Test endpoint
app.get('/api/test', (req, res) => {
    console.log('Test endpoint called');
    const apiKey = process.env.GEMINI_API_KEY;
    
    res.json({
        message: 'Test endpoint working', 
        status: 'success',
        hasApiKey: !!apiKey,
        timestamp: new Date().toISOString()
    });
});

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
