<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Professional Analysis Tool</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
</head>
<body>
    <!-- Main Login Container -->
    <div class="md-flex md-items-center md-justify-center" style="min-height: 100vh; padding: 24px;">
        <div class="md-card md-max-w-md md-w-full md-fade-in">
            <!-- Header -->
            <div class="md-text-center md-mb-xl">
                <h1 class="md-headline-large md-mb-sm">
                    <span class="material-icons md-text-primary">analytics</span> Alpha Gas Solution
                </h1>
                <p class="md-body-large md-text-secondary">Professional Analysis Platform</p>
            </div>
            
            <!-- Tab Navigation -->
            <div class="md-tabs md-mb-lg">
                <button class="md-tab active" onclick="switchTab('login')">
                    Sign In
                </button>
                <button class="md-tab" onclick="switchTab('signup')">
                    Create Account
                </button>
            </div>

            <!-- Login Tab -->
            <div id="login-tab" class="md-tab-content active">
                <form id="login-form">
                    <div class="md-form-group">
                        <label for="login-email" class="md-label">Email Address</label>
                        <input type="email" id="login-email" class="md-input" placeholder="Enter your email" value="<EMAIL>" required>
                    </div>
                    <div class="md-form-group">
                        <label for="login-password" class="md-label">Password</label>
                        <input type="password" id="login-password" class="md-input" placeholder="Enter your password" required>
                    </div>
                    <button type="submit" class="md-btn md-btn-filled md-w-full md-btn-lg">
                        Sign In to Dashboard
                    </button>
                </form>
            </div>

            <!-- Signup Tab -->
            <div id="signup-tab" class="md-tab-content">
                <form id="signup-form">
                    <div class="md-form-group">
                        <label for="signup-email" class="md-label">Email Address</label>
                        <input type="email" id="signup-email" class="md-input" placeholder="Enter your email" value="<EMAIL>" required>
                    </div>
                    <div class="md-form-group">
                        <label for="signup-password" class="md-label">Password</label>
                        <input type="password" id="signup-password" class="md-input" placeholder="Create a password" required>
                    </div>
                    <div class="md-form-group">
                        <label for="signup-confirm" class="md-label">Confirm Password</label>
                        <input type="password" id="signup-confirm" class="md-input" placeholder="Confirm your password" required>
                    </div>
                    <button type="submit" class="md-btn md-btn-success md-w-full md-btn-lg">
                        Create Account
                    </button>
                </form>
            </div>

            <!-- Status Messages -->
            <div id="status" class="md-mt-lg"></div>

            <!-- Action Buttons -->
            <div class="md-flex md-gap-md md-mt-lg">
                <button onclick="testConnection()" class="md-btn md-btn-ghost md-flex-1">
                    Test Connection
                </button>
                <button onclick="goToApp()" class="md-btn md-btn-secondary md-flex-1">
                    Go to App
                </button>
                <button onclick="testQuickLogin()" class="md-btn md-btn-outlined md-flex-1" title="Quick login test with preset credentials">
                    Quick Test
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function switchTab(tab) {
            // Update tab buttons
            document.querySelectorAll('.md-tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
            
            // Update tab content
            document.querySelectorAll('.md-tab-content').forEach(c => c.classList.remove('active'));
            document.getElementById(`${tab}-tab`).classList.add('active');
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            let statusClass = 'md-status-info';
            
            if (type === 'success') statusClass = 'md-status-success';
            else if (type === 'error') statusClass = 'md-status-error';
            
            statusDiv.innerHTML = `<div class="md-card md-card-compact ${statusClass} md-text-center">${message}</div>`;
        }

        async function handleLogin(e) {
            e.preventDefault();
            isLoggingIn = true; // Set flag to allow redirect
            
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            console.log('Attempting login with email:', email);
            showStatus('Logging in...', 'info');

            try {
                console.log('Calling Supabase signInWithPassword...');
                const { data, error } = await supabase.auth.signInWithPassword({
                    email,
                    password
                });

                console.log('Supabase response:', { data, error });

                if (error) {
                    console.error('Login error:', error);
                    showStatus(`Login failed: ${error.message}`, 'error');
                    isLoggingIn = false;
                } else {
                    console.log('Login successful:', data);
                    showStatus(`✅ Login successful! Welcome back, ${data.user.email}`, 'success');
                    
                    // Create or update user profile
                    await ensureUserProfile(data.user);
                    
                    // Wait for session to be fully established
                    showStatus('✅ Login successful! Verifying session...', 'success');
                    
                    // Verify session is properly established before redirecting
                    let sessionVerified = false;
                    let attempts = 0;
                    
                    while (!sessionVerified && attempts < 10) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                        const { data: { session } } = await supabase.auth.getSession();
                        
                        if (session && session.user && session.user.id === data.user.id) {
                            sessionVerified = true;
                            console.log('✅ Session verified, redirecting to app...');
                            showStatus('✅ Session verified! Redirecting to dashboard...', 'success');
                            
                            setTimeout(() => {
                                window.location.href = 'app-simple.html';
                            }, 1000);
                        } else {
                            attempts++;
                            console.log(`Session verification attempt ${attempts}/10...`);
                        }
                    }
                    
                    if (!sessionVerified) {
                        console.error('❌ Session verification failed after 10 attempts');
                        showStatus('⚠️ Login successful but session verification failed. Please try clicking "Go to App" manually.', 'error');
                        isLoggingIn = false;
                    }
                }
            } catch (error) {
                console.error('Login exception:', error);
                showStatus(`Error: ${error.message}`, 'error');
                isLoggingIn = false;
            }
        }

        async function handleSignup(e) {
            e.preventDefault();
            
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirm = document.getElementById('signup-confirm').value;
            
            if (password !== confirm) {
                showStatus('Passwords do not match', 'error');
                return;
            }

            showStatus('Creating account...', 'info');

            try {
                const { data, error } = await supabase.auth.signUp({
                    email,
                    password,
                    options: {
                        data: {
                            full_name: 'Tommy Lee',
                            role: 'admin'
                        }
                    }
                });

                if (error) {
                    showStatus(`Signup failed: ${error.message}`, 'error');
                } else {
                    showStatus(`✅ Account created! Welcome, ${data.user.email}`, 'success');
                    
                    // Create user profile immediately
                    await ensureUserProfile(data.user);
                    
                    // Redirect after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'app-simple.html';
                    }, 2000);
                }
            } catch (error) {
                showStatus(`Error: ${error.message}`, 'error');
            }
        }

        async function ensureUserProfile(user) {
            try {
                // Check if profile exists
                const { data: existingProfile } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();

                if (!existingProfile) {
                    // Create profile
                    const { error } = await supabase
                        .from('user_profiles')
                        .insert([{
                            user_id: user.id,
                            email: user.email,
                            full_name: user.user_metadata?.full_name || 'Tommy Lee',
                            role: 'admin',
                            status: 'approved',
                            approved_by: user.id,
                            approved_at: new Date().toISOString()
                        }]);

                    if (error) {
                        console.error('Error creating user profile:', error);
                    } else {
                        console.log('User profile created successfully');
                    }
                }
            } catch (error) {
                console.error('Error ensuring user profile:', error);
            }
        }

        async function testConnection() {
            showStatus('Testing database connection...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count')
                    .limit(1);

                if (error) {
                    showStatus(`❌ Database connection failed: ${error.message}`, 'error');
                } else {
                    showStatus('✅ Database connection successful!', 'success');
                }
            } catch (error) {
                showStatus(`❌ Connection error: ${error.message}`, 'error');
            }
        }

        function goToApp() {
            window.location.href = 'app-simple.html';
        }

        async function testQuickLogin() {
            const email = document.getElementById('login-email').value || '<EMAIL>';
            const password = document.getElementById('login-password').value;
            
            if (!password) {
                showStatus('Please enter a password first', 'error');
                return;
            }
            
            showStatus('🚀 Quick login test starting...', 'info');
            
            // Simulate the login form submission
            const loginForm = document.getElementById('login-form');
            const submitEvent = new Event('submit');
            loginForm.dispatchEvent(submitEvent);
        }

        // Add event listeners
        document.getElementById('login-form').addEventListener('submit', handleLogin);
        document.getElementById('signup-form').addEventListener('submit', handleSignup);

        // Check if already authenticated - but only redirect on explicit login
        let isLoggingIn = false;
        
        supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_IN' && session && isLoggingIn) {
                showStatus(`✅ Already authenticated as ${session.user.email}`, 'success');
                setTimeout(() => {
                    window.location.href = 'app.html';
                }, 1000);
            }
        });

        // Check current session on load - but don't auto-redirect
        window.addEventListener('load', async () => {
            const { data: { session } } = await supabase.auth.getSession();
            if (session) {
                // Show that user is already logged in but don't auto-redirect
                showStatus(`✅ Already signed in as ${session.user.email}. Click "Sign In to Dashboard" to continue.`, 'success');
                
                // Pre-fill the email field
                document.getElementById('login-email').value = session.user.email;
                
                // Enable direct access button
                const loginForm = document.getElementById('login-form');
                const directAccessBtn = document.createElement('button');
                directAccessBtn.type = 'button';
                directAccessBtn.className = 'md-btn md-btn-secondary md-w-full md-btn-lg md-mt-sm';
                directAccessBtn.textContent = '🚀 Continue to Dashboard';
                directAccessBtn.onclick = () => {
                    window.location.href = 'app-simple.html';
                };
                loginForm.appendChild(directAccessBtn);
            }
        });
    </script>
</body>
</html>
