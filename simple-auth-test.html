<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Auth Test - Alpha Gas Solution</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
</head>
<body>
    <div class="md-container md-py-xl">
        <div class="md-card md-max-w-lg md-mx-auto">
            <div class="md-card-header">
                <h1 class="md-headline-large">🧪 Simple Authentication Test</h1>
                <p class="md-body-large md-text-secondary">Testing basic auth flow without modules</p>
            </div>
            
            <div class="md-card-content">
                <div id="authStatus" class="md-mb-lg">
                    <div class="md-loading-spinner"></div>
                    <span>Checking authentication...</span>
                </div>
                
                <div id="authResult" class="md-hidden"></div>
                
                <div class="md-flex md-gap-md md-mt-lg">
                    <button onclick="testAuth()" class="md-btn md-btn-filled">Test Auth</button>
                    <button onclick="goToFullApp()" class="md-btn md-btn-secondary">Try Full App</button>
                    <button onclick="goToLogin()" class="md-btn md-btn-outlined">Back to Login</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('authStatus');
            const resultDiv = document.getElementById('authResult');
            
            let className = 'md-status-info';
            let icon = '📝';
            
            if (type === 'success') {
                className = 'md-status-success';
                icon = '✅';
            } else if (type === 'error') {
                className = 'md-status-error';
                icon = '❌';
            } else if (type === 'loading') {
                icon = '⏳';
            }
            
            statusDiv.innerHTML = `<div class="md-card md-card-compact ${className}">${icon} ${message}</div>`;
            resultDiv.classList.remove('md-hidden');
        }

        async function testAuth() {
            updateStatus('Testing authentication...', 'loading');
            
            try {
                console.log('🔍 Getting current session...');
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    console.error('Session error:', error);
                    updateStatus(`Session error: ${error.message}`, 'error');
                    return;
                }
                
                if (session && session.user) {
                    console.log('✅ Session found:', session.user.email);
                    updateStatus(`✅ Authenticated as: ${session.user.email}`, 'success');
                    
                    // If authenticated, try to load the full app
                    setTimeout(() => {
                        updateStatus('✅ Authentication successful! You can now access the full app.', 'success');
                        document.getElementById('authResult').innerHTML = `
                            <div class="md-card md-card-compact md-mt-md">
                                <h3 class="md-title-medium md-mb-sm">Session Details:</h3>
                                <p><strong>Email:</strong> ${session.user.email}</p>
                                <p><strong>User ID:</strong> ${session.user.id}</p>
                                <p><strong>Expires:</strong> ${new Date(session.expires_at * 1000).toLocaleString()}</p>
                                <div class="md-mt-md">
                                    <button onclick="proceedToApp()" class="md-btn md-btn-filled md-w-full">
                                        🚀 Proceed to Full App
                                    </button>
                                </div>
                            </div>
                        `;
                    }, 1000);
                } else {
                    console.log('❌ No session found');
                    updateStatus('❌ Not authenticated. Please log in first.', 'error');
                    document.getElementById('authResult').innerHTML = `
                        <div class="md-card md-card-compact md-mt-md">
                            <p class="md-text-secondary md-mb-md">No active session found. You need to log in first.</p>
                            <button onclick="goToLogin()" class="md-btn md-btn-filled md-w-full">
                                Go to Login Page
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Auth test error:', error);
                updateStatus(`Error: ${error.message}`, 'error');
            }
        }

        function proceedToApp() {
            updateStatus('Redirecting to full application...', 'loading');
            setTimeout(() => {
                window.location.href = 'app.html';
            }, 1000);
        }

        function goToFullApp() {
            window.location.href = 'app.html';
        }

        function goToLogin() {
            window.location.href = 'simple-auth-login.html';
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(testAuth, 500);
        });

        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth state changed:', event);
            if (event === 'SIGNED_IN') {
                updateStatus('✅ Sign in detected! Testing session...', 'success');
                setTimeout(testAuth, 1000);
            } else if (event === 'SIGNED_OUT') {
                updateStatus('❌ Signed out detected', 'error');
            }
        });
    </script>
</body>
</html>
