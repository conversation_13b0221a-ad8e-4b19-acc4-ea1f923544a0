// Hybrid data persistence: Shared capabilities + User-specific targets
import { supabaseStorage } from './supabase-client.js';
import { authManager } from './auth.js';

const STORAGE_KEY = 'alphaGasSolution_gasData';

// Save user-specific target analytes to Supabase
async function saveDataToLocalStorage() {
    console.log('💾 saveDataToLocalStorage called (hybrid mode)');
    
    // Check if user is authenticated
    if (!authManager.isAuthenticated()) {
        console.error('❌ User not authenticated, cannot save to Supabase');
        // For unauthenticated users, save to localStorage only
        try {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(gasData));
            console.log('✅ Full data cached to localStorage (guest mode)');
            return;
        } catch (localError) {
            console.error('❌ Error saving to localStorage:', localError);
            throw new Error(`Failed to save data locally: ${localError.message}`);
        }
    }

    // Ensure authentication is ready
    try {
        await authManager.ensureReady();
    } catch (authError) {
        console.error('❌ Authentication not ready:', authError);
        throw new Error(`Authentication not ready: ${authError.message}`);
    }

    // Filter out shared capabilities (only save user-specific targets)
    const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
    console.log('👤 Saving', userTargets.length, 'user-specific targets');

    // Save to localStorage as cache
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(gasData));
        console.log('✅ Full data cached to localStorage');
    } catch (localError) {
        console.error('❌ Error caching to localStorage:', localError);
        // Don't fail the entire operation for localStorage errors
    }

    // Save only user targets to Supabase with retry mechanism
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
        try {
            console.log(`🚀 Attempting to save user targets to Supabase (attempt ${retryCount + 1}/${maxRetries})...`);
            await supabaseStorage.saveUserTargets(userTargets);
            console.log('✅ User targets saved to Supabase successfully');
            return; // Success, exit the retry loop
        } catch (error) {
            retryCount++;
            console.error(`❌ Error saving to Supabase (attempt ${retryCount}):`, error);
            
            if (retryCount >= maxRetries) {
                throw new Error(`Failed to save to database after ${maxRetries} attempts: ${error.message}`);
            }
            
            // Wait before retrying (exponential backoff)
            const waitTime = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
            console.log(`⏳ Waiting ${waitTime/1000}s before retry...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
    }
}

// Load hybrid data: shared capabilities + user-specific targets
async function loadDataFromLocalStorage() {
    console.log('🔄 loadDataFromLocalStorage called (hybrid mode)');
    
    // Check if user is authenticated
    if (!authManager.isAuthenticated()) {
        console.log('👤 User not authenticated, loading from localStorage only (guest mode)');
        
        // For unauthenticated users, load from localStorage only
        try {
            const savedData = localStorage.getItem(STORAGE_KEY);
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                if (Array.isArray(parsedData) && parsedData.length > 0) {
                    gasData.length = 0;
                    gasData.push(...parsedData);
                    console.log('✅ Data loaded from localStorage (guest mode):', gasData.length, 'analytes');
                    return true;
                }
            }
            console.log('⚠️ No cached data found in localStorage');
            return false;
        } catch (error) {
            console.error('❌ Error loading from localStorage:', error);
            throw new Error(`Failed to load cached data: ${error.message}`);
        }
    }

    // Ensure authentication is ready
    try {
        await authManager.ensureReady();
    } catch (authError) {
        console.error('❌ Authentication not ready:', authError);
        throw new Error(`Authentication not ready: ${authError.message}`);
    }

    // Load hybrid data from Supabase (shared + user-specific)
    try {
        console.log('🚀 Attempting to load hybrid data from Supabase...');
        const hybridData = await supabaseStorage.loadHybridData();
        if (hybridData && hybridData.length > 0) {
            gasData.length = 0; // Clear existing data
            gasData.push(...hybridData); // Add loaded data
            console.log('✅ Hybrid data loaded from Supabase:', gasData.length, 'analytes');
            console.log('📊 Loaded analytes:', gasData.map(a => ({ name: a.name, current: a.current?.length, target: a.target?.length, shared: a.is_shared })));
            
            // Update localStorage cache
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(gasData));
                console.log('💾 Data cached to localStorage');
            } catch (localError) {
                console.error('❌ Error caching to localStorage:', localError);
            }
            
            return true;
        } else {
            console.log('⚠️ No hybrid data found in Supabase');
            
            // Check localStorage for any cached data
            try {
                console.log('🔍 Checking localStorage for cached data...');
                const savedData = localStorage.getItem(STORAGE_KEY);
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    if (Array.isArray(parsedData) && parsedData.length > 0) {
                        gasData.length = 0;
                        gasData.push(...parsedData);
                        console.log('✅ Data loaded from localStorage cache:', gasData.length, 'analytes');
                        
                        // Sync cached data to Supabase
                        try {
                            console.log('🔄 Syncing cached data to Supabase...');
                            // Filter only user targets for syncing
                            const userTargets = parsedData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                            await supabaseStorage.saveUserTargets(userTargets);
                            console.log('✅ Cached user targets synced to Supabase');
                        } catch (syncError) {
                            console.error('❌ Could not sync cached data to Supabase:', syncError);
                        }
                        
                        return true;
                    }
                }
                console.log('⚠️ No cached data found in localStorage either');
                
                // Fallback: Initialize with default data structure (marking as shared where appropriate)
                console.log('🔧 Initializing with default gasData...');
                // Note: gasData is already initialized in data.js
                // Add hybrid properties to existing default data
                gasData.forEach(analyte => {
                    // Mark the first 10 analytes as shared capabilities based on the database
                    const sharedAnalytes = ['Hydrocarbons (Total/Generic)', 'SO2', 'H2S', 'O2', 'CH4', 'CO2', 'CO', 'N2', 'H2'];
                    analyte.is_shared = sharedAnalytes.includes(analyte.name);
                    analyte.data_type = analyte.is_shared ? 'shared_capability' : 'user_target';
                    analyte.isCustom = !analyte.is_shared;
                });
                
                console.log('✅ Default data initialized with hybrid properties');
                return true;
                
            } catch (error) {
                console.error('❌ Error checking localStorage cache:', error);
            }
        }
    } catch (error) {
        console.error('❌ Error loading from Supabase:', error);
        throw error;
    }

    return false;
}

// Reset to default data (authenticated users only)
async function resetToDefaultData() {
    if (!authManager.isAuthenticated()) {
        console.error('❌ User not authenticated, cannot reset data');
        return;
    }

    if (confirm('Are you sure you want to reset all data to defaults? This will remove all custom analytes you\'ve added.')) {
        try {
            // Clear localStorage cache
            localStorage.removeItem(STORAGE_KEY);
            console.log('Data cleared from localStorage cache');
            
            // Clear Supabase data for this user (only user targets, not shared data)
            try {
                await supabaseStorage.clearUserData();
                console.log('User data cleared from Supabase');
            } catch (error) {
                console.error('Error clearing Supabase user data:', error);
            }
        } catch (error) {
            console.error('Error clearing data:', error);
        }
        location.reload(); // Reload page to get fresh default data
    }
}

// Export data as JSON for backup
function exportData() {
    const dataStr = JSON.stringify(gasData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'alpha_gas_solution_data.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Import data from JSON file
async function importData(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = async function(e) {
            try {
                const importedData = JSON.parse(e.target.result);
                if (Array.isArray(importedData)) {
                    gasData.length = 0;
                    gasData.push(...importedData);
                    
                    // Save to localStorage first
                    localStorage.setItem(STORAGE_KEY, JSON.stringify(gasData));
                    
                    // Try to save to Supabase
                    try {
                        await supabaseStorage.importData(importedData);
                        console.log('Data imported to Supabase successfully');
                    } catch (error) {
                        console.error('Error importing to Supabase:', error);
                    }
                    
                    renderChart();
                    alert('Data imported successfully!');
                } else {
                    alert('Invalid data format. Please select a valid JSON file.');
                }
            } catch (error) {
                alert('Error reading file. Please select a valid JSON file.');
                console.error('Import error:', error);
            }
        };
        reader.readAsText(file);
    }
}

// Make functions globally available for form-handler.js
window.saveDataToLocalStorage = saveDataToLocalStorage;
window.loadDataFromLocalStorage = loadDataFromLocalStorage;
window.resetToDefaultData = resetToDefaultData;
window.exportData = exportData;
window.importData = importData;
