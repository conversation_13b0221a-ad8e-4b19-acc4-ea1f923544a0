body {
    font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    background: #0a0a0a;
    color: #e4e4e7;
    margin: 0;
    padding: 0;
    padding-bottom: 12.5rem;
    line-height: 1.6;
    font-size: 0.875rem;
    overflow-x: hidden;
}
.chart-container, .customization-form-container, .calibration-analyzer-container {
    width: 95%;
    max-width: 1400px;
    margin: 20px auto;
    padding: 24px;
    background: #111113;
    border: 1px solid #27272a;
    border-radius: 8px;
    box-shadow: 
        0 2px 8px rgba(0,0,0,0.4),
        0 0 20px rgba(168, 85, 247, 0.05);
    position: relative;
}
.gas-row {
    display: flex;
    align-items: flex-start; 
    margin-bottom: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #27272a;
    transition: background-color 0.2s ease;
}
.gas-row:last-child {
    border-bottom: none;
}
.gas-row:hover {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-radius: 6px;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(168, 85, 247, 0.2);
}
.gas-name {
    width: 180px;
    font-weight: 500;
    color: #f4f4f5;
    font-size: 0.85rem;
    padding-right: 16px;
    flex-shrink: 0;
    margin-top: 10px; 
    display: flex;
    align-items: center;
    justify-content: space-between;
    letter-spacing: 0.025em;
}
.bar-area {
    flex-grow: 1;
    height: 40px;
    position: relative;
    background: #18181b;
    border: 1px solid #27272a;
    border-radius: 4px;
    overflow: visible; 
}
.bar {
    position: absolute;
    height: 60%;
    top: 20%;
    border-radius: 2px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.65rem;
    color: #fafafa;
    white-space: nowrap;
    overflow: visible; 
    font-weight: 500;
    letter-spacing: 0.025em;
}
.current-bar {
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border: 1px solid #2563eb;
    box-shadow: 0 0 12px rgba(59, 130, 246, 0.3);
    z-index: 3; 
}
.target-bar {
    background: linear-gradient(90deg, #10b981 0%, #047857 100%);
    border: 1px solid #059669;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.2);
    height: 100%;
    top: 0;
    opacity: 0.8;
    z-index: 2; 
}
.gap-notes-section {
    width: 250px;
    padding-left: 16px;
    flex-shrink: 0;
}
.gap-notes-text {
    font-size: 0.75rem;
    color: #a1a1aa;
    line-height: 1.4;
    white-space: pre-wrap;
}
.scale-label {
    font-size: 0.65rem;
    color: #71717a;
    position: absolute;
    bottom: -18px;
    transform: translateX(-50%);
    font-weight: 400;
    letter-spacing: 0.025em;
}
.legend {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding: 12px 16px;
    background: #18181b;
    border: 1px solid #27272a;
    border-radius: 6px;
}
.legend-item {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: #d4d4d8;
    font-weight: 500;
}
.legend-color {
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 2px;
    border: 1px solid #3f3f3f;
}
.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fafafa;
    margin-bottom: 8px;
    text-align: center;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #a855f7 0%, #3b82f6 50%, #10b981 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}
.header-title::before {
    content: "$ ";
    color: #71717a;
    font-weight: 400;
}
.header-subtitle {
    font-size: 0.9rem;
    color: #a1a1aa;
    margin-bottom: 32px;
    text-align: center;
    font-weight: 400;
}

.tooltip {
    position: absolute;
    background: #000000;
    border: 1px solid #404040;
    color: #fafafa;
    padding: 8px 12px; 
    border-radius: 4px; 
    font-size: 0.75rem; 
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
    z-index: 100; 
    pointer-events: none;
    min-width: 150px; 
    max-width: 300px; 
    white-space: normal; 
    box-shadow: 0 4px 12px rgba(0,0,0,0.6);
    bottom: 110%; 
    left: 50%;
    transform: translateX(-50%);
    font-weight: 400;
    letter-spacing: 0.025em;
}

.bar:hover .tooltip {
    visibility: visible;
    opacity: 1;
}

.form-section, .analyzer-section {
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #27272a;
}
.form-section:last-child, .analyzer-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}
.saas-form-title, .saas-analyzer-title { 
    background: linear-gradient(135deg, #f4f4f5 0%, #e4e4e7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}
.saas-form-title::after, .saas-analyzer-title::after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 0;
    width: 2rem;
    height: 2px;
    background: linear-gradient(90deg, #a855f7 0%, #3b82f6 100%);
    border-radius: 1px;
}
.saas-input, .saas-textarea, .saas-analyzer-input, .saas-select {
    background: linear-gradient(135deg, #18181b 0%, #1f1f23 100%);
    border: 1px solid #3f3f46;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.saas-input::before, .saas-textarea::before, .saas-analyzer-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
}
.saas-input:focus, .saas-textarea:focus, .saas-analyzer-input:focus, .saas-select:focus {
    border-color: #a855f7;
    box-shadow: 
        0 0 0 3px rgba(168, 85, 247, 0.15),
        0 2px 8px rgba(168, 85, 247, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform: translateY(-1px);
}
.saas-input:hover, .saas-textarea:hover, .saas-analyzer-input:hover, .saas-select:hover {
    border-color: #52525b;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.saas-input::placeholder, .saas-textarea::placeholder, .saas-analyzer-input::placeholder {
    color: #71717a;
    font-style: italic;
    transition: color 0.3s ease;
}
.saas-input:focus::placeholder, .saas-textarea:focus::placeholder, .saas-analyzer-input:focus::placeholder {
    color: #a1a1aa;
}
.saas-textarea {
    background: linear-gradient(135deg, #18181b 0%, #1f1f23 100%);
    resize: vertical;
    font-family: 'JetBrains Mono', monospace;
    line-height: 1.5;
}
.saas-textarea:focus {
    background: linear-gradient(135deg, #1a1a1d 0%, #212126 100%);
}
.saas-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a855f7' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1rem 1rem;
    padding-right: 2.5rem;
    appearance: none;
}
.saas-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}
.compound-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, rgba(24, 24, 27, 0.5) 0%, rgba(39, 39, 42, 0.5) 100%);
    border: 1px solid rgba(63, 63, 70, 0.5);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}
.compound-row:hover {
    background: linear-gradient(135deg, rgba(24, 24, 27, 0.7) 0%, rgba(39, 39, 42, 0.7) 100%);
    border-color: rgba(113, 113, 122, 0.5);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.compound-row .saas-input {
    margin-bottom: 0;
    background: linear-gradient(135deg, #0f0f10 0%, #18181b 100%);
}
.saas-input:invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.15);
    animation: shakeError 0.5s ease-in-out;
}
.saas-input:valid {
    border-color: #16a34a;
    box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.15);
}
@keyframes shakeError {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}
.saas-analyzer-input.concentration {
    background: linear-gradient(135deg, #18181b 0%, #1f1f23 100%);
    border: 1px solid #3f3f46;
    font-variant-numeric: tabular-nums;
    text-align: right;
    font-weight: 500;
}
.saas-analyzer-input.concentration:focus {
    background: linear-gradient(135deg, #1a1a1d 0%, #212126 100%);
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
}

/* Delete Button Styles */
.delete-analyte-btn {
    background: none;
    border: 1px solid #3f3f46;
    color: #a1a1aa;
    cursor: pointer;
    font-size: 0.75rem;
    margin-left: 8px;
    padding: 4px 6px;
    border-radius: 3px;
    transition: all 0.2s ease;
    font-family: 'JetBrains Mono', monospace;
}

.delete-analyte-btn:hover {
    background: #27272a;
    color: #f87171;
    border-color: #52525b;
    transform: scale(1.05);
}

/* Data Management Controls */
.data-management-controls {
    border-top: 1px solid #3f3f46;
    padding-top: 20px;
    margin-top: 20px;
}

.data-controls-section h3 {
    color: #f4f4f5;
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 1rem;
}

.data-controls-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.data-control-btn {
    padding: 10px 16px;
    border: 1px solid #3f3f46;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-family: 'JetBrains Mono', monospace;
    background: #27272a;
    color: #d4d4d8;
}

.export-btn:hover, .import-btn:hover, .reset-btn:hover {
    background: #3f3f46;
    border-color: #52525b;
}

/* Save Confirmation Controls */
.save-status-container {
    background: #18181b;
    border: 1px solid #3f3f46;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 16px;
}

.auth-status, .save-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    margin-bottom: 4px;
    font-family: 'JetBrains Mono', monospace;
}

.auth-status:last-child, .save-status:last-child {
    margin-bottom: 0;
}

.status-indicator {
    font-size: 0.9rem;
    min-width: 20px;
    filter: grayscale(0.3);
    transition: filter 0.2s ease;
}

.auth-status.authenticated .status-indicator {
    color: #86efac;
}

.auth-status.guest .status-indicator {
    color: #fbbf24;
}

.auth-status.error .status-indicator {
    color: #fca5a5;
}

.save-status.success .status-indicator {
    color: #86efac;
}

.save-status.pending .status-indicator {
    color: #93c5fd;
    animation: pulse 1.5s ease-in-out infinite;
}

.save-status.error .status-indicator {
    color: #fca5a5;
}

.auth-status:hover .status-indicator,
.save-status:hover .status-indicator {
    filter: grayscale(0);
}

.form-buttons-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    flex-wrap: wrap;
    border-top: 1px solid #27272a;
    padding-top: 16px;
    margin-top: 16px;
}

.save-btn {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: #ffffff;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 200px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.85rem;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.25);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.save-btn:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
    transform: translateY(-2px);
}

.save-btn:active {
    transform: translateY(0);
}

.save-btn:disabled {
    background: #27272a;
    color: #71717a;
    cursor: not-allowed;
    transform: none;
    border-color: #3f3f46;
}

.save-btn.saving {
    background: #404040;
    border-color: #525252;
}

.save-btn.success {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
    color: #ffffff;
}

.save-btn.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25);
    color: #ffffff;
}

.save-info {
    background: #18181b;
    border-left: 3px solid #52525b;
    padding: 12px 16px;
    border-radius: 0 4px 4px 0;
    font-size: 0.8rem;
    font-family: 'JetBrains Mono', monospace;
}

.save-info p {
    margin: 0;
    line-height: 1.5;
    color: #d4d4d8;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.05);
    }
}

/* Modern Dark Theme Enhancements */

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #18181b;
}

::-webkit-scrollbar-thumb {
    background: #3f3f46;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #52525b;
}

/* Selection Styling */
::selection {
    background: rgba(82, 82, 91, 0.3);
    color: #fafafa;
}

/* Focus Outline */
*:focus {
    outline: none;
}

/* Professional Modern Headers */
.header-title::before {
    content: "◆ ";
    color: #a855f7;
    font-weight: 400;
    text-shadow: 0 0 8px rgba(168, 85, 247, 0.3);
}

/* Subtle Grid Background */
.chart-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(39, 39, 42, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(39, 39, 42, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
    border-radius: 8px;
}

.chart-container > * {
    position: relative;
    z-index: 1;
}

/* Enhanced Bar Area with Grid */
.bar-area::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(63, 63, 70, 0.2) 1px, transparent 1px),
        linear-gradient(90deg, rgba(63, 63, 70, 0.1) 1px, transparent 1px);
    background-size: 10% 100%, 100% 20%;
    pointer-events: none;
    border-radius: 4px;
}

/* Monospace Number Styling */
input[type="number"], .scale-label {
    font-variant-numeric: tabular-nums;
    letter-spacing: 0.025em;
}

/* Professional Loading Animation */
@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes breathe {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.8; }
}

.loading {
    animation: breathe 2s ease-in-out infinite;
}

/* Subtle Hover Effects */
.gas-row:hover .gas-name {
    color: #fafafa;
    text-shadow: 0 0 8px rgba(168, 85, 247, 0.3);
    transition: all 0.3s ease;
}

.gas-row:hover .bar {
    transform: scaleY(1.05);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Code-like Styling for Values */
.tooltip, .scale-label, .saas-input[type="number"] {
    font-variant-numeric: tabular-nums;
}

/* Minimal Status Indicators */
.status-indicator {
    filter: grayscale(0.3);
    transition: filter 0.2s ease;
}

.auth-status:hover .status-indicator,
.save-status:hover .status-indicator {
    filter: grayscale(0);
}

/* Professional Button Group */
.form-buttons-row {
    border-top: 1px solid #27272a;
    padding-top: 16px;
    margin-top: 16px;
}

/* Subtle Form Section Dividers */
.form-section::before {
    content: "";
    display: block;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #a855f7 0%, #3b82f6 100%);
    margin-bottom: 16px;
    border-radius: 1px;
    box-shadow: 0 0 8px rgba(168, 85, 247, 0.3);
}

.form-section:first-of-type::before {
    display: none;
}

/* Enhanced Range Input Groups */
.range-input-group::before {
    content: "◆";
    color: #a855f7;
    margin-right: 8px;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(168, 85, 247, 0.5);
}

/* Professional Error States */
.saas-input:invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.15);
}

.saas-input:valid {
    border-color: #16a34a;
}

/* Minimal Animation for Chart Updates */
.bar {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.bar::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bar:hover::after {
    opacity: 1;
}

.gas-row {
    transition: all 0.2s ease;
}

/* Professional Typography Scale */
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    letter-spacing: -0.025em;
    color: #f4f4f5;
}

/* Subtle Container Depth */
.customization-form-container,
.calibration-analyzer-container {
    background: linear-gradient(145deg, #111113 0%, #0f0f10 100%);
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.02);
}

/* Professional Code Block Styling */
pre, code {
    font-family: 'JetBrains Mono', monospace;
    background: #0a0a0a;
    border: 1px solid #27272a;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 0.8rem;
    color: #d4d4d8;
}

/* Refined Legend Colors for Dark Theme */
.legend-color:nth-child(1) { background: linear-gradient(90deg, #4c4c4c 0%, #6b6b6b 100%); }
.legend-color:nth-child(2) { background: linear-gradient(90deg, #262626 0%, #404040 100%); }

/* Professional Focus States */
.saas-input:focus,
.saas-textarea:focus,
.saas-analyzer-input:focus,
.saas-select:focus {
    border-color: #a855f7;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2);
}

/* Enhanced Button Focus States */
.add-range-btn:focus,
.analyzer-btn:focus,
.add-compound-btn:focus,
.submit-btn:focus,
.remove-compound-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.3);
}

/* Subtle Disabled States */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Enhanced Chart Visual Effects */
.enhanced-target-bar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(2px);
    border-radius: 0.375rem;
}

.enhanced-current-bar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.375rem;
    position: relative;
    z-index: 10;
}

.enhanced-current-bar::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, rgba(229, 77, 46, 0.3), rgba(220, 38, 38, 0.3));
    border-radius: 0.5rem;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.enhanced-current-bar:hover::before {
    opacity: 1;
}

/* Pulsing glow animation for current values */
@keyframes pulseGlow {
    0% {
        box-shadow: 0 0 20px rgba(229, 77, 46, 0.6), 0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    100% {
        box-shadow: 0 0 30px rgba(229, 77, 46, 0.8), 0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

/* Enhanced chart scale */
.enhanced-chart-scale {
    background: linear-gradient(90deg, transparent 0%, rgba(113, 113, 122, 0.1) 50%, transparent 100%);
    border-radius: 0.25rem;
}

.enhanced-scale-label {
    animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(10px);
    background: rgba(0, 0, 0, 0.8);
    color: #e4e4e7;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(113, 113, 122, 0.3);
}

.enhanced-scale-tick {
    animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: scaleY(0);
    background: linear-gradient(180deg, #71717a 0%, #52525b 100%);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Enhanced gap notes */
.enhanced-gap-notes {
    background: linear-gradient(135deg, rgba(24, 24, 27, 0.8) 0%, rgba(39, 39, 42, 0.8) 100%);
    border: 1px solid rgba(113, 113, 122, 0.3);
    border-radius: 0.5rem;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
}

.enhanced-gap-notes:hover {
    background: linear-gradient(135deg, rgba(24, 24, 27, 0.9) 0%, rgba(39, 39, 42, 0.9) 100%);
    border-color: rgba(113, 113, 122, 0.5);
    transform: translateX(2px);
}

.enhanced-notes-text {
    position: relative;
}

.enhanced-notes-text::before {
    content: '📝';
    margin-right: 0.5rem;
    opacity: 0.7;
}

/* Enhanced tooltips */
.enhanced-tooltip {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(24, 24, 27, 0.95) 100%);
    border: 1px solid rgba(168, 85, 247, 0.3);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5), 0 0 20px rgba(168, 85, 247, 0.2);
    backdrop-filter: blur(12px);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.enhanced-tooltip::after {
    border-color: rgba(24, 24, 27, 0.95) transparent transparent transparent;
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.3));
}

/* Slide in up animation */
@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scale in animation */
@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scaleY(0);
    }
    100% {
        opacity: 1;
        transform: scaleY(1);
    }
}

/* Enhanced loading states */
.saas-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 1rem;
    color: #a1a1aa;
    font-weight: 500;
}

.saas-loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(168, 85, 247, 0.2);
    border-top: 3px solid #a855f7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced fade in animation */
.saas-fade-in {
    animation: fadeInWithScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
}

@keyframes fadeInWithScale {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Enhanced gas row hover effects */
.saas-gas-row:hover {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.08) 0%, rgba(59, 130, 246, 0.08) 100%);
    border-radius: 0.5rem;
    transform: translateX(4px) translateY(-1px);
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(168, 85, 247, 0.2);
}

/* Enhanced delete button styling */
.delete-analyte-btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.375rem;
    backdrop-filter: blur(4px);
}

.delete-analyte-btn:hover {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.15) 0%, rgba(185, 28, 28, 0.15) 100%);
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}
