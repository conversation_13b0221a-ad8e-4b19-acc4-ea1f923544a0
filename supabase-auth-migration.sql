-- Migration to add authentication support
-- This will update the gas_analytes table to use user_id instead of user_session

-- First, add the user_id column
ALTER TABLE gas_analytes ADD COLUMN user_id UUID REFERENCES auth.users(id);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_gas_analytes_user_id ON gas_analytes(user_id);

-- Update existing records to use a placeholder user_id (optional - for migration of existing data)
-- You may want to clear existing data instead since it was session-based
-- DELETE FROM gas_analytes WHERE user_session IS NOT NULL;

-- Drop the old user_session column (uncomment when ready)
-- ALTER TABLE gas_analytes DROP COLUMN user_session;

-- Enable Row Level Security (RLS) for user data isolation
ALTER TABLE gas_analytes ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only access their own data
CREATE POLICY "Users can access own analytes" ON gas_analytes
FOR ALL USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT ALL ON gas_analytes TO authenticated;
GRANT USAGE ON SEQUENCE gas_analytes_id_seq TO authenticated;
