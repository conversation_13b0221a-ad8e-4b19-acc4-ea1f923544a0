// Supabase client configuration for Gas Analysis Application
// Load Supabase from CDN
const { createClient } = window.supabase || {};

// Supabase credentials
const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';

// Create Supabase client
let supabase;
if (createClient) {
    supabase = createClient(supabaseUrl, supabaseKey);
} else {
    console.warn('Supabase not loaded, using localStorage fallback');
    supabase = null;
}

export { supabase };

// Database operations
export class SupabaseStorage {
    constructor() {
        // No longer need session ID, will use authenticated user ID
    }

    // Get current authenticated user ID
    async getUserId() {
        const { data: { user } } = await supabase.auth.getUser();
        return user?.id || null;
    }

    // Save all analyte data to Supabase
    async saveData(analytesData) {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            console.log('Saving data to Supabase...', {
                userId: userId,
                dataCount: analytesData.length,
                dataPreview: analytesData.map(a => ({ name: a.name, currentCount: a.current?.length, targetCount: a.target?.length }))
            });
            
            // First, clear existing data for this user
            const { error: deleteError } = await supabase
                .from('gas_analytes')
                .delete()
                .eq('user_id', userId);

            if (deleteError) {
                console.error('Error clearing existing data:', deleteError);
            } else {
                console.log('Existing data cleared successfully');
            }

            // Insert all current data
            const dataToInsert = analytesData.map(analyte => ({
                user_id: userId,
                name: analyte.name,
                current_ranges: analyte.current || [],
                target_ranges: analyte.target || [],
                gap_notes: analyte.gapNotes || '',
                is_custom: analyte.isCustom !== false // Default to true unless explicitly false
            }));

            console.log('Data to insert:', dataToInsert);

            const { data, error } = await supabase
                .from('gas_analytes')
                .insert(dataToInsert);

            if (error) {
                console.error('Error saving to Supabase:', error);
                throw error;
            }

            console.log('Data saved successfully to Supabase, inserted records:', data?.length || dataToInsert.length);
            return data;
        } catch (error) {
            console.error('Failed to save data to Supabase:', error);
            throw error;
        }
    }

    // Load all analyte data from Supabase
    async loadData() {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            console.log('Loading data from Supabase for user:', userId);
            
            const { data, error } = await supabase
                .from('gas_analytes')
                .select('*')
                .eq('user_id', userId)
                .order('created_at');

            if (error) {
                console.error('Error loading from Supabase:', error);
                throw error;
            }

            if (!data || data.length === 0) {
                console.log('No data found in Supabase for this user');
                return [];
            }

            // Transform Supabase data back to app format
            const transformedData = data.map(item => ({
                name: item.name,
                current: item.current_ranges || [],
                target: item.target_ranges || [],
                gapNotes: item.gap_notes || '',
                isCustom: item.is_custom
            }));

            console.log('Data loaded successfully from Supabase:', transformedData);
            return transformedData;
        } catch (error) {
            console.error('Failed to load data from Supabase:', error);
            throw error;
        }
    }

    // Delete a specific analyte
    async deleteAnalyte(analyteName) {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            const { error } = await supabase
                .from('gas_analytes')
                .delete()
                .eq('user_id', userId)
                .eq('name', analyteName);

            if (error) {
                console.error('Error deleting from Supabase:', error);
                throw error;
            }

            console.log('Analyte deleted successfully from Supabase:', analyteName);
        } catch (error) {
            console.error('Failed to delete analyte from Supabase:', error);
            throw error;
        }
    }

    // Import data (replace all existing data)
    async importData(importedData) {
        try {
            await this.saveData(importedData);
            console.log('Data imported successfully to Supabase');
        } catch (error) {
            console.error('Failed to import data to Supabase:', error);
            throw error;
        }
    }

    // Clear all data for this user
    async clearData() {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            const { error } = await supabase
                .from('gas_analytes')
                .delete()
                .eq('user_id', userId);

            if (error) {
                console.error('Error clearing data from Supabase:', error);
                throw error;
            }

            console.log('All data cleared from Supabase');
        } catch (error) {
            console.error('Failed to clear data from Supabase:', error);
            throw error;
        }
    }

    // Test connection to Supabase
    async testConnection() {
        try {
            const { data, error } = await supabase
                .from('gas_analytes')
                .select('count(*)')
                .limit(1);

            if (error) {
                console.error('Supabase connection test failed:', error);
                return false;
            }

            console.log('Supabase connection successful');
            return true;
        } catch (error) {
            console.error('Supabase connection test error:', error);
            return false;
        }
    }

    // Load hybrid data: shared capabilities + user-specific targets
    async loadHybridData() {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            console.log('Loading hybrid data from Supabase for user:', userId);
            
            // Load data using RLS policy: shared data + user's own data
            const { data, error } = await supabase
                .from('gas_analytes')
                .select('*')
                .order('created_at');

            if (error) {
                console.error('Error loading hybrid data from Supabase:', error);
                throw error;
            }

            if (!data || data.length === 0) {
                console.log('No hybrid data found in Supabase');
                return [];
            }

            // Transform Supabase data back to app format
            const transformedData = data.map(item => ({
                name: item.name,
                current: item.current_ranges || [],
                target: item.target_ranges || [],
                gapNotes: item.gap_notes || '',
                isCustom: item.is_custom,
                is_shared: item.is_shared || false,
                data_type: item.data_type || 'user_target'
            }));

            console.log('Hybrid data loaded successfully from Supabase:');
            console.log('- Shared capabilities:', transformedData.filter(d => d.is_shared).length);
            console.log('- User targets:', transformedData.filter(d => !d.is_shared).length);
            
            return transformedData;
        } catch (error) {
            console.error('Failed to load hybrid data from Supabase:', error);
            throw error;
        }
    }

    // Save only user-specific target analytes (not shared data)
    async saveUserTargets(userTargets) {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            console.log('Saving user targets to Supabase...', {
                userId: userId,
                targetCount: userTargets.length,
                targets: userTargets.map(t => t.name)
            });
            
            // First, clear existing user targets (not shared data)
            const { error: deleteError } = await supabase
                .from('gas_analytes')
                .delete()
                .eq('user_id', userId)
                .eq('is_shared', false);

            if (deleteError) {
                console.error('Error clearing existing user targets:', deleteError);
            } else {
                console.log('Existing user targets cleared successfully');
            }

            // Insert user targets only
            if (userTargets.length > 0) {
                const dataToInsert = userTargets.map(analyte => ({
                    user_id: userId,
                    name: analyte.name,
                    current_ranges: analyte.current || [],
                    target_ranges: analyte.target || [],
                    gap_notes: analyte.gapNotes || '',
                    is_custom: analyte.isCustom !== false,
                    is_shared: false,
                    data_type: 'user_target'
                }));

                console.log('User targets to insert:', dataToInsert.length);

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert(dataToInsert);

                if (error) {
                    console.error('Error saving user targets to Supabase:', error);
                    throw error;
                }

                console.log('User targets saved successfully to Supabase');
                return data;
            } else {
                console.log('No user targets to save');
                return [];
            }
        } catch (error) {
            console.error('Failed to save user targets to Supabase:', error);
            throw error;
        }
    }

    // Clear only user data (not shared capabilities)
    async clearUserData() {
        try {
            const userId = await this.getUserId();
            if (!userId) {
                throw new Error('User not authenticated');
            }

            const { error } = await supabase
                .from('gas_analytes')
                .delete()
                .eq('user_id', userId)
                .eq('is_shared', false);

            if (error) {
                console.error('Error clearing user data from Supabase:', error);
                throw error;
            }

            console.log('User data cleared from Supabase (shared capabilities preserved)');
        } catch (error) {
            console.error('Failed to clear user data from Supabase:', error);
            throw error;
        }
    }
}

// Export singleton instance
export const supabaseStorage = new SupabaseStorage();
