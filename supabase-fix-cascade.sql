-- Alternative approach: Use CASCADE to drop dependencies automatically
-- Run this SQL in your Supabase SQL Editor as an alternative to supabase-fix.sql

-- This will drop the function and all dependent objects (like triggers) automatically
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Create the function with proper security settings
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Recreate the trigger
CREATE TRIGGER update_gas_analytes_updated_at 
    BEFORE UPDATE ON gas_analytes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Verify the fix worked
SELECT 
    routine_name,
    routine_type,
    security_type,
    is_deterministic
FROM information_schema.routines 
WHERE routine_name = 'update_updated_at_column';
