-- Fix for the search_path security issue in Supabase
-- Run this SQL in your Supabase SQL Editor to fix the function

-- Step 1: Drop the trigger first (this removes the dependency)
DROP TRIGGER IF EXISTS update_gas_analytes_updated_at ON gas_analytes;

-- Step 2: Now we can safely drop the function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Step 3: Create the function with proper security settings
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Step 4: Recreate the trigger
CREATE TRIGGER update_gas_analytes_updated_at 
    BEFORE UPDATE ON gas_analytes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Optional: Check that the function is working
-- This should show no security warnings
SELECT 
    routine_name,
    routine_type,
    security_type,
    is_deterministic
FROM information_schema.routines 
WHERE routine_name = 'update_updated_at_column';
