-- Gas Analysis Data Table for Alpha Gas Solution
-- Run this SQL in your Supabase SQL Editor

-- Create the main table for storing gas analysis data
CREATE TABLE IF NOT EXISTS gas_analytes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_session TEXT DEFAULT 'default', -- For basic session tracking
    name TEXT NOT NULL,
    current_ranges JSONB DEFAULT '[]'::jsonb,
    target_ranges JSONB DEFAULT '[]'::jsonb,
    gap_notes TEXT,
    is_custom BOOLEAN DEFAULT true, -- true for user-added, false for default analytes
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_gas_analytes_user_session ON gas_analytes(user_session);
CREATE INDEX IF NOT EXISTS idx_gas_analytes_name ON gas_analytes(name);

-- Enable Row Level Security (RLS)
ALTER TABLE gas_analytes ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows all operations (you can restrict this later)
CREATE POLICY "Allow all operations for now" ON gas_analytes
    FOR ALL USING (true);

-- Optional: Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_gas_analytes_updated_at 
    BEFORE UPDATE ON gas_analytes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default analytes (your original 14)
INSERT INTO gas_analytes (name, current_ranges, target_ranges, gap_notes, is_custom, user_session) VALUES
('Hydrocarbons (Total/Generic)', 
 '[{"min": 1, "max": 100, "label": "1-100 ppm (GC1-FID, unvalidated)"}]'::jsonb,
 '[{"min": 0.01, "max": 1000000, "label": "0.01 ppm - 100%"}]'::jsonb,
 'Speciation needed (VOCs). Current FID unvalidated. Broader range for pure components & trace analysis.',
 false, 'default'),

('SO2',
 '[{"min": 1, "max": 50, "label": "<50 ppm (GC2-FPD, saturated at 50ppm)"}, {"min": 1000, "max": 50000, "label": "0.1% - 5% (GC1-TCD)"}]'::jsonb,
 '[{"min": 1, "max": 500000, "label": "1 ppm - 50%"}]'::jsonb,
 'Gap between FPD saturation (~50ppm) and TCD lower limit (1000ppm). Lower detection for CEMS.',
 false, 'default'),

('H2S',
 '[{"min": 1, "max": 30, "label": "<30 ppm (GC2-FPD, saturated at 30ppm)"}, {"min": 500, "max": 60000, "label": "0.05% - >6% (GC1-TCD)"}]'::jsonb,
 '[{"min": 1, "max": 200000, "label": "1 ppm - 20%"}]'::jsonb,
 'Gap between FPD saturation (~30ppm) and TCD lower limit (500ppm).',
 false, 'default'),

('O2',
 '[{"min": 1000, "max": 980000, "label": "0.1% - 98% (GC1-TCD)"}]'::jsonb,
 '[{"min": 1, "max": 1000000, "label": "1 ppm - 100%"}]'::jsonb,
 'Need ppb/low ppm for UHP.',
 false, 'default'),

('CH4',
 '[{"min": 8, "max": 30000, "label": "8ppm - 3% (GC1-FID)"}, {"min": 30000, "max": 834000, "label": "3% - 83.4% (GC1-TCD)"}]'::jsonb,
 '[{"min": 1, "max": 1000000, "label": "1 ppm - 100%"}]'::jsonb,
 'Lower detection for trace.',
 false, 'default'),

('CO2',
 '[{"min": 5000, "max": 150000, "label": "≥0.5% (Teledyne, max std 15%)"}]'::jsonb,
 '[{"min": 1, "max": 1000000, "label": "1 ppm - 100%"}]'::jsonb,
 'GC1-TCD not operational.',
 false, 'default'),

('CO',
 '[{"min": 5000, "max": 100000, "label": "≥0.5% (Teledyne, max std 10%)"}]'::jsonb,
 '[{"min": 1, "max": 100000, "label": "1 ppm - 10%"}]'::jsonb,
 'GC1-TCD not operational.',
 false, 'default'),

('N2O',
 '[{"min": 5000, "max": 500000, "label": "≥0.5% (Teledyne, Entonox)"}]'::jsonb,
 '[{"min": 0.1, "max": 500000, "label": "0.1 ppm - 50%"}]'::jsonb,
 'Need lower for CEMS.',
 false, 'default'),

('N2',
 '[]'::jsonb,
 '[{"min": 1, "max": 1000000, "label": "1 ppm - 100%"}]'::jsonb,
 'Teledyne N2 overlap issues.',
 false, 'default'),

('NO',
 '[{"min": 1, "max": 5000, "label": "0-5000 ppm (Teledyne)"}]'::jsonb,
 '[{"min": 1, "max": 10000, "label": "1 ppm - 1%"}]'::jsonb,
 'Good for CEMS.',
 false, 'default'),

('NO2',
 '[{"min": 1, "max": 5000, "label": "0-5000 ppm (Teledyne)"}]'::jsonb,
 '[{"min": 1, "max": 500, "label": "1 ppm - 500 ppm"}]'::jsonb,
 'Good for CEMS.',
 false, 'default'),

('H2',
 '[]'::jsonb,
 '[{"min": 1, "max": 1000000, "label": "1 ppm - 100%"}]'::jsonb,
 'Major gap.',
 false, 'default'),

('Argon (Ar)',
 '[]'::jsonb,
 '[{"min": 1, "max": 1000000, "label": "1 ppm - 100%"}]'::jsonb,
 'Critical, N2 overlap.',
 false, 'default'),

('VOCs (Speciated)',
 '[]'::jsonb,
 '[{"min": 0.001, "max": 100000, "label": "1 ppb - Various %"}]'::jsonb,
 'Requires GC-MS.',
 false, 'default')

ON CONFLICT DO NOTHING;
