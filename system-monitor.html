<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RBAC System Monitor - Alpha Gas Solution</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .monitor-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .monitor-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .monitor-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .monitor-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .monitor-content {
            padding: 30px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .metric-card.warning {
            border-left-color: #f39c12;
        }

        .metric-card.error {
            border-left-color: #e74c3c;
        }

        .metric-card.success {
            border-left-color: #27ae60;
        }

        .metric-title {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .metric-description {
            font-size: 0.9rem;
            color: #95a5a6;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-offline { background-color: #e74c3c; }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px 0;
            border-bottom: 1px solid #34495e;
        }

        .log-timestamp {
            color: #95a5a6;
            margin-right: 10px;
        }

        .log-level-info { color: #3498db; }
        .log-level-warn { color: #f39c12; }
        .log-level-error { color: #e74c3c; }
        .log-level-success { color: #27ae60; }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.secondary {
            background: #95a5a6;
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.success {
            background: #27ae60;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-style: italic;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .monitor-header h1 {
                font-size: 2rem;
            }
            
            .monitor-content {
                padding: 20px;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="monitor-header">
            <h1>🔐 RBAC System Monitor</h1>
            <p>Real-time monitoring and performance analytics for Role-Based Access Control</p>
        </div>

        <div class="monitor-content">
            <div class="controls">
                <button class="btn" onclick="refreshAll()">🔄 Refresh All</button>
                <button class="btn secondary" onclick="clearLogs()">🗑️ Clear Logs</button>
                <button class="btn success" onclick="runHealthCheck()">🏥 Health Check</button>
                <button class="btn" onclick="exportMetrics()">📊 Export Metrics</button>
            </div>

            <div class="metrics-grid">
                <div class="metric-card" id="system-status">
                    <div class="metric-title">System Status</div>
                    <div class="metric-value">
                        <span class="status-indicator status-online"></span>
                        <span id="status-text">Initializing...</span>
                    </div>
                    <div class="metric-description">Overall system health</div>
                </div>

                <div class="metric-card" id="active-users">
                    <div class="metric-title">Active Users</div>
                    <div class="metric-value" id="active-users-count">--</div>
                    <div class="metric-description">Currently logged in users</div>
                </div>

                <div class="metric-card" id="cache-performance">
                    <div class="metric-title">Cache Hit Rate</div>
                    <div class="metric-value" id="cache-hit-rate">--</div>
                    <div class="metric-description">Performance optimization effectiveness</div>
                </div>

                <div class="metric-card" id="pending-approvals">
                    <div class="metric-title">Pending Approvals</div>
                    <div class="metric-value" id="pending-count">--</div>
                    <div class="metric-description">Users awaiting admin approval</div>
                </div>

                <div class="metric-card" id="avg-response-time">
                    <div class="metric-title">Avg Response Time</div>
                    <div class="metric-value" id="response-time">--</div>
                    <div class="metric-description">Database query performance</div>
                </div>

                <div class="metric-card" id="error-rate">
                    <div class="metric-title">Error Rate</div>
                    <div class="metric-value" id="error-rate-value">--</div>
                    <div class="metric-description">System reliability metric</div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">📈 Performance Charts</h2>
                <div class="chart-container">
                    <div class="chart-placeholder">
                        Performance charts would be rendered here using Chart.js or similar
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">📊 Role Distribution</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-title">Administrators</div>
                        <div class="metric-value" id="admin-count">--</div>
                        <div class="metric-description">Users with admin privileges</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Approved Users</div>
                        <div class="metric-value" id="user-count">--</div>
                        <div class="metric-description">Regular approved users</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Guest Sessions</div>
                        <div class="metric-value" id="guest-count">--</div>
                        <div class="metric-description">Anonymous user sessions</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">📝 System Logs</h2>
                <div class="log-container" id="system-logs">
                    <div class="log-entry">
                        <span class="log-timestamp">[--:--:--]</span>
                        <span class="log-level-info">[INFO]</span>
                        System monitor initializing...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { auth } from './auth.js';
        import { supabase } from './supabase-client.js';
        import { performanceOptimizer } from './performance-optimizer.js';
        import { emailService } from './email-service.js';

        class SystemMonitor {
            constructor() {
                this.isRunning = false;
                this.updateInterval = 5000; // 5 seconds
                this.logs = [];
                this.maxLogs = 100;
            }

            async init() {
                this.log('System monitor starting...', 'info');
                
                try {
                    // Initialize dependencies
                    await performanceOptimizer.init();
                    await emailService.init();
                    
                    this.log('Dependencies initialized successfully', 'success');
                    
                    // Start monitoring
                    this.startMonitoring();
                    
                    this.log('System monitor online', 'success');
                    this.updateSystemStatus('online', 'System Online');
                    
                } catch (error) {
                    this.log(`Failed to initialize: ${error.message}`, 'error');
                    this.updateSystemStatus('offline', 'System Error');
                }
            }

            startMonitoring() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateMetrics();
                
                setInterval(() => {
                    this.updateMetrics();
                }, this.updateInterval);
            }

            async updateMetrics() {
                try {
                    // Update performance metrics
                    const perfMetrics = performanceOptimizer.getMetrics();
                    this.updateCacheMetrics(perfMetrics);
                    
                    // Update user metrics
                    await this.updateUserMetrics();
                    
                    // Update system health
                    await this.updateSystemHealth();
                    
                } catch (error) {
                    this.log(`Metrics update failed: ${error.message}`, 'error');
                }
            }

            updateCacheMetrics(metrics) {
                const cacheHitRateEl = document.getElementById('cache-hit-rate');
                const responseTimeEl = document.getElementById('response-time');
                
                if (cacheHitRateEl) {
                    cacheHitRateEl.textContent = `${metrics.cacheHitRate}%`;
                    const card = cacheHitRateEl.closest('.metric-card');
                    card.className = `metric-card ${metrics.cacheHitRate > 80 ? 'success' : metrics.cacheHitRate > 60 ? 'warning' : 'error'}`;
                }
                
                if (responseTimeEl) {
                    responseTimeEl.textContent = `${metrics.averageResponseTime.toFixed(0)}ms`;
                    const card = responseTimeEl.closest('.metric-card');
                    card.className = `metric-card ${metrics.averageResponseTime < 500 ? 'success' : metrics.averageResponseTime < 1000 ? 'warning' : 'error'}`;
                }
            }

            async updateUserMetrics() {
                try {
                    // Get user counts by role
                    const { data: profiles, error } = await supabase
                        .from('user_profiles')
                        .select('role, status');
                    
                    if (error) throw error;
                    
                    const roleCounts = profiles.reduce((acc, profile) => {
                        acc[profile.role] = (acc[profile.role] || 0) + 1;
                        if (profile.status === 'pending') {
                            acc.pending = (acc.pending || 0) + 1;
                        }
                        return acc;
                    }, {});
                    
                    // Update UI
                    this.updateElement('admin-count', roleCounts.admin || 0);
                    this.updateElement('user-count', roleCounts.user || 0);
                    this.updateElement('pending-count', roleCounts.pending || 0);
                    
                    // Active users (simplified - would need session tracking)
                    this.updateElement('active-users-count', profiles.length);
                    
                } catch (error) {
                    this.log(`User metrics update failed: ${error.message}`, 'error');
                }
            }

            async updateSystemHealth() {
                try {
                    // Test database connection
                    const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
                    
                    if (error) {
                        throw error;
                    }
                    
                    // Calculate error rate (simplified)
                    const errorRate = this.calculateErrorRate();
                    this.updateElement('error-rate-value', `${errorRate.toFixed(1)}%`);
                    
                    const errorCard = document.getElementById('error-rate').closest('.metric-card');
                    errorCard.className = `metric-card ${errorRate < 1 ? 'success' : errorRate < 5 ? 'warning' : 'error'}`;
                    
                } catch (error) {
                    this.log(`Health check failed: ${error.message}`, 'error');
                    this.updateSystemStatus('warning', 'Partial Service');
                }
            }

            calculateErrorRate() {
                const recentLogs = this.logs.slice(-50); // Last 50 logs
                const errorLogs = recentLogs.filter(log => log.level === 'error');
                return recentLogs.length > 0 ? (errorLogs.length / recentLogs.length) * 100 : 0;
            }

            updateElement(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }

            updateSystemStatus(status, text) {
                const statusEl = document.getElementById('status-text');
                const indicatorEl = document.querySelector('.status-indicator');
                
                if (statusEl) statusEl.textContent = text;
                if (indicatorEl) {
                    indicatorEl.className = `status-indicator status-${status}`;
                }
            }

            log(message, level = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = { timestamp, message, level };
                
                this.logs.push(logEntry);
                if (this.logs.length > this.maxLogs) {
                    this.logs = this.logs.slice(-this.maxLogs);
                }
                
                this.updateLogDisplay();
                console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
            }

            updateLogDisplay() {
                const logsContainer = document.getElementById('system-logs');
                if (!logsContainer) return;
                
                const logsHtml = this.logs.slice(-20).map(log => 
                    `<div class="log-entry">
                        <span class="log-timestamp">[${log.timestamp}]</span>
                        <span class="log-level-${log.level}">[${log.level.toUpperCase()}]</span>
                        ${log.message}
                    </div>`
                ).join('');
                
                logsContainer.innerHTML = logsHtml;
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }

            async runHealthCheck() {
                this.log('Running comprehensive health check...', 'info');
                
                const checks = [
                    { name: 'Database Connection', check: () => supabase.from('user_profiles').select('count').limit(1) },
                    { name: 'Authentication Service', check: () => auth.isInitialized() },
                    { name: 'Performance Optimizer', check: () => performanceOptimizer.initialized },
                    { name: 'Email Service', check: () => emailService.initialized }
                ];
                
                for (const { name, check } of checks) {
                    try {
                        await check();
                        this.log(`✅ ${name}: OK`, 'success');
                    } catch (error) {
                        this.log(`❌ ${name}: ${error.message}`, 'error');
                    }
                }
                
                this.log('Health check completed', 'info');
            }

            clearLogs() {
                this.logs = [];
                this.updateLogDisplay();
                this.log('Logs cleared', 'info');
            }

            exportMetrics() {
                const metrics = {
                    timestamp: new Date().toISOString(),
                    performance: performanceOptimizer.getMetrics(),
                    notifications: emailService.getQueueStatus(),
                    logs: this.logs.slice(-50),
                    system: {
                        status: document.getElementById('status-text')?.textContent,
                        activeUsers: document.getElementById('active-users-count')?.textContent,
                        pendingApprovals: document.getElementById('pending-count')?.textContent
                    }
                };
                
                const dataStr = JSON.stringify(metrics, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `rbac-metrics-${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                this.log('Metrics exported successfully', 'success');
            }
        }

        // Global functions for buttons
        window.refreshAll = () => monitor.updateMetrics();
        window.clearLogs = () => monitor.clearLogs();
        window.runHealthCheck = () => monitor.runHealthCheck();
        window.exportMetrics = () => monitor.exportMetrics();

        // Initialize monitor
        const monitor = new SystemMonitor();
        
        document.addEventListener('DOMContentLoaded', () => {
            monitor.init();
        });
    </script>
</body>
</html>
