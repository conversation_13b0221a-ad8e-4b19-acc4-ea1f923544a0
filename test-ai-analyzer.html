<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Analyzer Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="chart-container">
        <h1 class="header-title">AI Analyzer Test</h1>
        
        <!-- Test the AI Analyzer Components -->
        <div class="calibration-analyzer-container">
            <h2 class="analyzer-title">AI Calibration Gas Capability Analysis</h2>
            <div class="analyzer-section">
                <div>
                    <label for="calibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                    <input type="text" id="calibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix">
                </div>
                
                <h3 class="text-lg font-medium text-zinc-300 mt-4 mb-2">Components:</h3>
                <div id="calibrationCompoundsContainer">
                    <!-- Compound input rows will be added here -->
                </div>
                <button type="button" id="addCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
                <div id="calibrationInputError" class="error-message" style="display: none;"></div>
                <button type="button" id="analyzeCalibrationBtn" class="analyzer-btn">🔬 Analyze Calibration Standard</button>
            </div>
            <div id="calibrationLoading" class="text-sm text-zinc-400 my-2" style="display: none;">Analyzing, please wait...</div>
            <div id="calibrationResults">
                <!-- AI analysis results will be displayed here -->
            </div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="ai-analyzer.js"></script>
    
    <script>
        console.log('🧪 Starting AI Analyzer Test...');
        
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📋 DOM ready, checking elements...');
            
            // Check if all required elements exist
            const elements = {
                'calibrationGasStandardName': document.getElementById('calibrationGasStandardName'),
                'addCalibrationCompoundBtn': document.getElementById('addCalibrationCompoundBtn'),
                'calibrationCompoundsContainer': document.getElementById('calibrationCompoundsContainer'),
                'analyzeCalibrationBtn': document.getElementById('analyzeCalibrationBtn'),
                'calibrationResultsDiv': document.getElementById('calibrationResults'),
                'calibrationLoadingDiv': document.getElementById('calibrationLoading'),
                'calibrationInputErrorDiv': document.getElementById('calibrationInputError')
            };
            
            console.log('🔍 Element check results:');
            for (const [name, element] of Object.entries(elements)) {
                console.log(`  ${name}: ${element ? '✅ Found' : '❌ Missing'}`);
            }
            
            // Try to initialize the AI analyzer
            try {
                if (typeof initializeAIAnalyzer === 'function') {
                    console.log('🚀 Initializing AI Analyzer...');
                    initializeAIAnalyzer();
                    console.log('✅ AI Analyzer initialized successfully');
                    
                    // Test adding a compound row
                    if (typeof addCalibrationCompoundRow === 'function') {
                        console.log('🧪 Adding test compound row...');
                        addCalibrationCompoundRow();
                        console.log('✅ Test compound row added');
                    } else {
                        console.error('❌ addCalibrationCompoundRow function not found');
                    }
                    
                    // Test button click
                    const addBtn = document.getElementById('addCalibrationCompoundBtn');
                    if (addBtn) {
                        console.log('🖱️ Testing add compound button...');
                        addBtn.addEventListener('click', function() {
                            console.log('✅ Add compound button clicked successfully!');
                        });
                    }
                    
                } else {
                    console.error('❌ initializeAIAnalyzer function not found');
                }
            } catch (error) {
                console.error('❌ Error during initialization:', error);
            }
        });
    </script>
</body>
</html>
