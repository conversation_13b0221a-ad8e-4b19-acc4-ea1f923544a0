<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Analyzer Test - Alpha Gas Solution</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #18181b;
            border: 1px solid #3f3f46;
            border-radius: 12px;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #27272a;
            border-radius: 8px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background-color: #10b981; }
        .status-fail { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="header-title">AI Analyzer Functionality Test</h1>
        <p class="header-subtitle">Testing Alpha Gas Solution AI Analysis Features</p>

        <div class="test-section">
            <h2 class="text-xl font-bold text-white mb-4">DOM Elements Test</h2>
            <div id="domTest">
                <p><span class="status-indicator" id="statusStandardName"></span>Calibration Gas Standard Name Input</p>
                <p><span class="status-indicator" id="statusAddBtn"></span>Add Compound Button</p>
                <p><span class="status-indicator" id="statusContainer"></span>Compounds Container</p>
                <p><span class="status-indicator" id="statusAnalyzeBtn"></span>Analyze Button</p>
                <p><span class="status-indicator" id="statusResults"></span>Results Container</p>
            </div>
        </div>

        <div class="test-section">
            <h2 class="text-xl font-bold text-white mb-4">Function Availability Test</h2>
            <div id="functionTest">
                <p><span class="status-indicator" id="statusInitFunc"></span>initializeAIAnalyzer function</p>
                <p><span class="status-indicator" id="statusAddRowFunc"></span>addCalibrationCompoundRow function</p>
            </div>
        </div>

        <div class="test-section">
            <h2 class="text-xl font-bold text-white mb-4">Interactive Test</h2>
            <div class="calibration-analyzer-container">
                <div class="analyzer-section">
                    <div>
                        <label for="testCalibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                        <input type="text" id="testCalibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix" value="Test Standard">
                    </div>
                    
                    <h3 class="text-lg font-medium text-zinc-300 mt-4 mb-2">Components:</h3>
                    <div id="testCalibrationCompoundsContainer">
                        <!-- Compound input rows will be added here -->
                    </div>
                    <button type="button" id="testAddCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
                    <div id="testCalibrationInputError" class="error-message" style="display: none;"></div>
                    <button type="button" id="testAnalyzeCalibrationBtn" class="analyzer-btn">🔬 Test Analyze Button</button>
                </div>
                <div id="testCalibrationLoading" class="text-sm text-zinc-400 my-2" style="display: none;">Testing, please wait...</div>
                <div id="testCalibrationResults">
                    <!-- Test results will be displayed here -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="text-xl font-bold text-white mb-4">Console Output</h2>
            <div id="consoleOutput" class="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto">
                <!-- Console messages will appear here -->
            </div>
        </div>
    </div>

    <!-- Load the required scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="ai-analyzer.js"></script>

    <script>
        // Capture console messages
        const consoleOutput = document.getElementById('consoleOutput');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-red-400' : 'text-green-400';
            div.textContent = `[${timestamp}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            addToConsole(args.join(' '));
            originalConsoleLog.apply(console, args);
        };

        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        // Test functions
        function testDOMElements() {
            const elements = {
                statusStandardName: document.getElementById('calibrationGasStandardName'),
                statusAddBtn: document.getElementById('addCalibrationCompoundBtn'),
                statusContainer: document.getElementById('calibrationCompoundsContainer'),
                statusAnalyzeBtn: document.getElementById('analyzeCalibrationBtn'),
                statusResults: document.getElementById('calibrationResults')
            };

            Object.keys(elements).forEach(key => {
                const statusEl = document.getElementById(key);
                const element = elements[key];
                if (element) {
                    statusEl.className = 'status-indicator status-pass';
                    console.log(`✅ ${key}: Found`);
                } else {
                    statusEl.className = 'status-indicator status-fail';
                    console.log(`❌ ${key}: Not found`);
                }
            });
        }

        function testFunctions() {
            const functions = {
                statusInitFunc: window.initializeAIAnalyzer,
                statusAddRowFunc: window.addCalibrationCompoundRow
            };

            Object.keys(functions).forEach(key => {
                const statusEl = document.getElementById(key);
                const func = functions[key];
                if (typeof func === 'function') {
                    statusEl.className = 'status-indicator status-pass';
                    console.log(`✅ ${key}: Available`);
                } else {
                    statusEl.className = 'status-indicator status-fail';
                    console.log(`❌ ${key}: Not available`);
                }
            });
        }

        function setupTestInterface() {
            // Replace the DOM elements with test versions
            window.calibrationGasStandardNameInput = document.getElementById('testCalibrationGasStandardName');
            window.addCalibrationCompoundBtn = document.getElementById('testAddCalibrationCompoundBtn');
            window.calibrationCompoundsContainer = document.getElementById('testCalibrationCompoundsContainer');
            window.analyzeCalibrationBtn = document.getElementById('testAnalyzeCalibrationBtn');
            window.calibrationResultsDiv = document.getElementById('testCalibrationResults');
            window.calibrationLoadingDiv = document.getElementById('testCalibrationLoading');
            window.calibrationInputErrorDiv = document.getElementById('testCalibrationInputError');

            // Set up event listeners for test interface
            document.getElementById('testAddCalibrationCompoundBtn').addEventListener('click', () => {
                console.log('🔬 Test: Add compound button clicked');
                if (typeof window.addCalibrationCompoundRow === 'function') {
                    window.addCalibrationCompoundRow();
                    console.log('✅ Test: Compound row added successfully');
                } else {
                    console.error('❌ Test: addCalibrationCompoundRow function not available');
                }
            });

            document.getElementById('testAnalyzeCalibrationBtn').addEventListener('click', () => {
                console.log('🔬 Test: Analyze button clicked');
                document.getElementById('testCalibrationResults').innerHTML = `
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p class="text-blue-600">✅ Test: Analyze button is working!</p>
                        <p class="text-sm text-blue-500 mt-2">This is a test response. In the real app, this would trigger AI analysis.</p>
                    </div>
                `;
            });

            // Add a test compound row by default
            if (typeof window.addCalibrationCompoundRow === 'function') {
                window.addCalibrationCompoundRow();
                console.log('✅ Test: Initial compound row added');
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔬 Starting AI Analyzer functionality test...');
            
            setTimeout(() => {
                testDOMElements();
                testFunctions();
                setupTestInterface();
                console.log('✅ Test setup complete');
            }, 500);
        });
    </script>
</body>
</html>
