<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Gas Solution - Analysis Tool (Test Mode)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="chart-container">
        <h1 class="header-title">Alpha Gas Solution</h1>
        <p class="header-subtitle">Analysis Range Visualization & AI Calibration Check (Test Mode)</p>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%); border: 1px solid #2563eb; box-shadow: 0 0 8px rgba(59, 130, 246, 0.2);"></div>
                <span>Current Capability</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #10b981 0%, #047857 100%); border: 1px solid #059669; box-shadow: 0 0 6px rgba(16, 185, 129, 0.2); opacity: 0.8;"></div>
                <span>Target Range (All-Rounder)</span>
            </div>
        </div>

        <div style="position: relative; height: 20px; margin-bottom: 25px; border-bottom: 1px solid #3f3f46;">
            <!-- Scale labels will be dynamically added here by JavaScript -->
        </div>

        <div id="chart">
            <!-- Gas rows will be dynamically added here -->
        </div>
    </div>

    <div class="customization-form-container">
        <h2 class="form-title">Add New Analyte to Chart</h2>
        <form id="addAnalyteForm">
            <div class="form-section">
                <label for="analyteName" class="form-label">Analyte Name:</label>
                <input type="text" id="analyteName" class="saas-input" required>
                <div id="analyteNameError" class="error-message" style="display: none;"></div>
            </div>

            <div class="form-section">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Current Capabilities</h3>
                <div id="currentRangesContainer" class="dynamic-ranges-container">
                    <!-- Current range inputs will be added here -->
                </div>
                <button type="button" class="add-range-btn" onclick="addRangeInput('current')">Add Current Range</button>
            </div>

            <div class="form-section">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Target Ranges</h3>
                <div id="targetRangesContainer" class="dynamic-ranges-container">
                    <!-- Target range inputs will be added here -->
                </div>
                <button type="button" class="add-range-btn" onclick="addRangeInput('target')">Add Target Range</button>
            </div>
            
            <div class="form-section">
                <label for="gapNotes" class="form-label">Gap Notes / Remarks:</label>
                <textarea id="gapNotes" class="saas-textarea"></textarea>
            </div>

            <div class="form-section">
                <button type="submit" class="submit-btn" id="addAnalyteBtn">Add Analyte to Chart</button>
            </div>
        </form>
    </div>

    <!-- AI Calibration Gas Analyzer Section -->
    <div class="calibration-analyzer-container">
        <h2 class="analyzer-title">AI Calibration Gas Capability Analysis</h2>
        <div class="analyzer-section">
            <div>
                <label for="calibrationGasStandardName" class="form-label">Calibration Gas Standard Name:</label>
                <input type="text" id="calibrationGasStandardName" class="saas-input" placeholder="e.g., Daily Check Mix">
            </div>
            
            <h3 class="text-lg font-medium text-zinc-300 mt-4 mb-2">Components:</h3>
            <div id="calibrationCompoundsContainer">
                <!-- Compound input rows will be added here -->
            </div>
            <button type="button" id="addCalibrationCompoundBtn" class="add-compound-btn">Add Compound</button>
            <div id="calibrationInputError" class="error-message" style="display: none;"></div>
            <button type="button" id="analyzeCalibrationBtn" class="analyzer-btn">🔬 Analyze Calibration Standard</button>
        </div>
        <div id="calibrationLoading" class="text-sm text-zinc-400 my-2" style="display: none;">Analyzing, please wait...</div>
        <div id="calibrationResults">
            <!-- AI analysis results will be displayed here -->
        </div>
    </div>

    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="chart.js"></script>
    <script src="form-handler.js"></script>
    <script src="ai-analyzer.js"></script>

    <script>
        // Test mode initialization - bypass authentication
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔧 Test Mode: Initializing Alpha Gas Solution without authentication...');
            
            try {
                // Load initial data (local storage only)
                await loadDataFromLocalStorage();
                console.log('✅ Test Mode: Initial data loaded');
                
                // Initialize basic chart components
                drawScaleLabels();
                renderChart();
                
                // Initialize form handler (test mode)
                if (typeof initializeFormHandler === 'function') {
                    initializeFormHandler();
                    console.log('✅ Test Mode: Form handler initialized');
                } else {
                    console.warn('⚠️ Test Mode: initializeFormHandler function not found');
                }
                
                // Initialize AI analyzer
                if (typeof initializeAIAnalyzer === 'function') {
                    initializeAIAnalyzer();
                    console.log('✅ Test Mode: AI analyzer initialized');
                } else {
                    console.warn('⚠️ Test Mode: initializeAIAnalyzer function not found');
                }
                
                // Add initial range inputs
                if (typeof addRangeInput === 'function') {
                    addRangeInput('current'); 
                    addRangeInput('target');
                    console.log('✅ Test Mode: Initial range inputs added');
                }
                
                // Add initial compound row for calibration analyzer
                if (typeof addCalibrationCompoundRow === 'function') {
                    addCalibrationCompoundRow();
                    console.log('✅ Test Mode: Initial calibration compound row added');
                }
                
                console.log('✅ Test Mode: Alpha Gas Solution initialized successfully');
                
            } catch (error) {
                console.error('❌ Test Mode: Error initializing app:', error);
            }
        });

        // Mock authentication for testing
        window.authManager = {
            getCurrentUser: () => ({ email: '<EMAIL>', id: 'test-user' }),
            isAuthenticated: () => true
        };
    </script>
</body>
</html>
