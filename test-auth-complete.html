<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Complete Authentication Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 193, 7, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        input {
            width: 200px;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px;
        }
        #log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Complete Authentication Test</h1>
        
        <div class="test-section">
            <h3>🔗 1. Supabase Connection Test</h3>
            <button onclick="testConnection()">Test Connection</button>
            <div id="connectionStatus"></div>
        </div>

        <div class="test-section">
            <h3>🔐 2. Authentication Test</h3>
            <div>
                <input type="email" id="testEmail" placeholder="<EMAIL>" value="<EMAIL>">
                <input type="password" id="testPassword" placeholder="password123" value="password123">
                <br>
                <button onclick="testSignUp()">Test Sign Up</button>
                <button onclick="testSignIn()">Test Sign In</button>
                <button onclick="testSignOut()">Test Sign Out</button>
            </div>
            <div id="authStatus"></div>
        </div>

        <div class="test-section">
            <h3>📊 3. Database Test (Requires Auth)</h3>
            <button onclick="testDatabaseWrite()">Test Write Analyte</button>
            <button onclick="testDatabaseRead()">Test Read Analytes</button>
            <button onclick="testDatabaseClear()">Clear Test Data</button>
            <div id="databaseStatus"></div>
        </div>

        <div class="test-section">
            <h3>🏠 4. Main App Integration</h3>
            <button onclick="testMainApp()">Test Main App Flow</button>
            <div id="integrationStatus"></div>
        </div>

        <div id="log"></div>
    </div>

    <script src="supabase-client.js"></script>
    <script>
        let currentUser = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logElement = document.getElementById('log');
            logElement.innerHTML += `<div>[${timestamp}] ${emoji} ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`${emoji} [${timestamp}] ${message}`);
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testConnection() {
            log('Testing Supabase connection...');
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count', { count: 'exact', head: true });
                
                if (error) throw error;
                
                updateStatus('connectionStatus', '✅ Connected to Supabase successfully!', 'success');
                log('Supabase connection successful', 'success');
            } catch (error) {
                updateStatus('connectionStatus', `❌ Connection failed: ${error.message}`, 'error');
                log(`Connection failed: ${error.message}`, 'error');
            }
        }

        async function testSignUp() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            log(`Attempting sign up for ${email}...`);
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password
                });
                
                if (error) throw error;
                
                if (data.user && !data.user.email_confirmed_at) {
                    updateStatus('authStatus', '📧 Sign up successful! Check email for verification.', 'warning');
                    log('Sign up successful - email verification required', 'warning');
                } else {
                    currentUser = data.user;
                    updateStatus('authStatus', '✅ Sign up and login successful!', 'success');
                    log('Sign up and login successful', 'success');
                }
            } catch (error) {
                updateStatus('authStatus', `❌ Sign up failed: ${error.message}`, 'error');
                log(`Sign up failed: ${error.message}`, 'error');
            }
        }

        async function testSignIn() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            log(`Attempting sign in for ${email}...`);
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) throw error;
                
                currentUser = data.user;
                updateStatus('authStatus', `✅ Signed in as ${data.user.email}`, 'success');
                log(`Signed in successfully as ${data.user.email}`, 'success');
            } catch (error) {
                updateStatus('authStatus', `❌ Sign in failed: ${error.message}`, 'error');
                log(`Sign in failed: ${error.message}`, 'error');
            }
        }

        async function testSignOut() {
            log('Attempting sign out...');
            try {
                const { error } = await supabase.auth.signOut();
                if (error) throw error;
                
                currentUser = null;
                updateStatus('authStatus', '✅ Signed out successfully', 'success');
                log('Signed out successfully', 'success');
            } catch (error) {
                updateStatus('authStatus', `❌ Sign out failed: ${error.message}`, 'error');
                log(`Sign out failed: ${error.message}`, 'error');
            }
        }

        async function testDatabaseWrite() {
            if (!currentUser) {
                updateStatus('databaseStatus', '❌ Must be signed in to test database', 'error');
                log('Database test requires authentication', 'error');
                return;
            }

            log('Testing database write operation...');
            try {
                const testAnalyte = {
                    user_id: currentUser.id,
                    analyte_name: 'Test Analyte',
                    matrix: 'Test Matrix',
                    method: 'Test Method',
                    category: 'Test Category',
                    created_at: new Date().toISOString()
                };

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert([testAnalyte])
                    .select();

                if (error) throw error;

                updateStatus('databaseStatus', '✅ Database write successful!', 'success');
                log(`Database write successful - inserted analyte with ID: ${data[0].id}`, 'success');
            } catch (error) {
                updateStatus('databaseStatus', `❌ Database write failed: ${error.message}`, 'error');
                log(`Database write failed: ${error.message}`, 'error');
            }
        }

        async function testDatabaseRead() {
            if (!currentUser) {
                updateStatus('databaseStatus', '❌ Must be signed in to test database', 'error');
                log('Database test requires authentication', 'error');
                return;
            }

            log('Testing database read operation...');
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_id', currentUser.id);

                if (error) throw error;

                updateStatus('databaseStatus', `✅ Database read successful! Found ${data.length} analytes for current user.`, 'success');
                log(`Database read successful - found ${data.length} analytes for user ${currentUser.email}`, 'success');
            } catch (error) {
                updateStatus('databaseStatus', `❌ Database read failed: ${error.message}`, 'error');
                log(`Database read failed: ${error.message}`, 'error');
            }
        }

        async function testDatabaseClear() {
            if (!currentUser) {
                updateStatus('databaseStatus', '❌ Must be signed in to clear test data', 'error');
                return;
            }

            log('Clearing test data...');
            try {
                const { error } = await supabase
                    .from('gas_analytes')
                    .delete()
                    .eq('user_id', currentUser.id)
                    .eq('analyte_name', 'Test Analyte');

                if (error) throw error;

                updateStatus('databaseStatus', '✅ Test data cleared successfully!', 'success');
                log('Test data cleared successfully', 'success');
            } catch (error) {
                updateStatus('databaseStatus', `❌ Clear failed: ${error.message}`, 'error');
                log(`Clear failed: ${error.message}`, 'error');
            }
        }

        async function testMainApp() {
            log('Testing main app integration...');
            
            // Test if main app files are accessible
            try {
                const response = await fetch('./index.html');
                if (response.ok) {
                    updateStatus('integrationStatus', '✅ Main app accessible. Ready for full integration test!', 'success');
                    log('Main app integration test successful', 'success');
                    
                    // Provide link to main app
                    document.getElementById('integrationStatus').innerHTML += 
                        '<br><a href="./index.html" target="_blank" style="color: #4ECDC4; text-decoration: none; font-weight: bold;">🚀 Open Main Application</a>';
                } else {
                    throw new Error('Main app not accessible');
                }
            } catch (error) {
                updateStatus('integrationStatus', `❌ Main app integration test failed: ${error.message}`, 'error');
                log(`Main app integration test failed: ${error.message}`, 'error');
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('🧪 Authentication test tool loaded');
            log('📋 Test sequence: Connection → Auth → Database → Integration');
            testConnection();
        });
    </script>
</body>
</html>
