<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication & Persistence Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            background: #f8f9fa;
        }
        .status-good { border-color: #28a745; background: #d4edda; }
        .status-bad { border-color: #dc3545; background: #f8d7da; }
        .status-warning { border-color: #ffc107; background: #fff3cd; }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover { background: #005a9e; }
        .test-button:disabled { background: #ccc; cursor: not-allowed; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .log { max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication & Data Persistence Test</h1>
        <p>This tool tests the complete authentication and data persistence flow.</p>

        <div id="authStatus" class="status-section">
            <h3>🔍 Authentication Status</h3>
            <p id="authInfo">Checking authentication...</p>
        </div>

        <div id="dbStatus" class="status-section">
            <h3>💾 Database Connection</h3>
            <p id="dbInfo">Testing database connection...</p>
        </div>

        <div id="persistenceTest" class="status-section">
            <h3>📊 Data Persistence Test</h3>
            <p id="persistenceInfo">Ready to test...</p>
            <button class="test-button" onclick="testDataPersistence()" id="testBtn">Test Data Persistence</button>
        </div>

        <div id="actionButtons">
            <button class="test-button" onclick="goToLogin()">Go to Login</button>
            <button class="test-button" onclick="goToMainApp()">Go to Main App</button>
            <button class="test-button" onclick="clearAllData()">Clear Test Data</button>
        </div>

        <div id="logs" class="status-section">
            <h3>📋 Test Logs</h3>
            <pre id="logOutput" class="log"></pre>
        </div>
    </div>

    <script type="module">
        // Import modules
        import { supabase } from './supabase-client.js';
        import { authManager } from './auth.js';

        let logMessages = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logMessages.push(logEntry);
            console.log(logEntry);
            document.getElementById('logOutput').textContent = logMessages.join('\n');
        }

        async function checkAuthentication() {
            try {
                log('Checking authentication status...');
                const user = await authManager.init();
                
                const authStatus = document.getElementById('authStatus');
                const authInfo = document.getElementById('authInfo');
                
                if (user) {
                    authStatus.className = 'status-section status-good';
                    authInfo.innerHTML = `✅ Authenticated as: <strong>${user.email}</strong><br>User ID: ${user.id}`;
                    log(`Authentication SUCCESS: ${user.email}`, 'success');
                    return true;
                } else {
                    authStatus.className = 'status-section status-bad';
                    authInfo.innerHTML = `❌ Not authenticated - Please login`;
                    log('Authentication FAILED: No user session', 'error');
                    return false;
                }
            } catch (error) {
                const authStatus = document.getElementById('authStatus');
                const authInfo = document.getElementById('authInfo');
                authStatus.className = 'status-section status-bad';
                authInfo.innerHTML = `❌ Authentication error: ${error.message}`;
                log(`Authentication ERROR: ${error.message}`, 'error');
                return false;
            }
        }

        async function testDatabaseConnection() {
            try {
                log('Testing database connection...');
                const { data, error } = await supabase.from('gas_analytes').select('count').limit(1);
                
                const dbStatus = document.getElementById('dbStatus');
                const dbInfo = document.getElementById('dbInfo');
                
                if (error) {
                    dbStatus.className = 'status-section status-bad';
                    dbInfo.innerHTML = `❌ Database connection failed: ${error.message}`;
                    log(`Database ERROR: ${error.message}`, 'error');
                    return false;
                } else {
                    dbStatus.className = 'status-section status-good';
                    dbInfo.innerHTML = `✅ Database connection successful`;
                    log('Database connection SUCCESS', 'success');
                    return true;
                }
            } catch (error) {
                const dbStatus = document.getElementById('dbStatus');
                const dbInfo = document.getElementById('dbInfo');
                dbStatus.className = 'status-section status-bad';
                dbInfo.innerHTML = `❌ Database test error: ${error.message}`;
                log(`Database TEST ERROR: ${error.message}`, 'error');
                return false;
            }
        }

        window.testDataPersistence = async function() {
            const testBtn = document.getElementById('testBtn');
            const persistenceTest = document.getElementById('persistenceTest');
            const persistenceInfo = document.getElementById('persistenceInfo');
            
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';
            
            try {
                log('Starting data persistence test...');
                
                // Check if user is authenticated
                const user = await supabase.auth.getUser();
                if (!user.data.user) {
                    throw new Error('User not authenticated');
                }
                
                // Create test analyte data
                const testAnalyte = {
                    user_id: user.data.user.id,
                    name: `Test Analyte ${Date.now()}`,
                    current_ranges: [{ min: 10, max: 100, label: 'Test Current' }],
                    target_ranges: [{ min: 5, max: 200, label: 'Test Target' }],
                    gap_notes: 'Test persistence functionality',
                    is_custom: true
                };
                
                log(`Created test data: ${testAnalyte.name}`);
                
                // Insert test data
                const { data: insertData, error: insertError } = await supabase
                    .from('gas_analytes')
                    .insert([testAnalyte])
                    .select();
                
                if (insertError) {
                    throw new Error(`Insert failed: ${insertError.message}`);
                }
                
                log(`Insert SUCCESS: Record created with ID ${insertData[0].id}`, 'success');
                
                // Verify data was saved
                const { data: selectData, error: selectError } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('id', insertData[0].id);
                
                if (selectError) {
                    throw new Error(`Select failed: ${selectError.message}`);
                }
                
                if (selectData.length === 0) {
                    throw new Error('Data not found after insert');
                }
                
                log(`Verification SUCCESS: Data retrieved successfully`, 'success');
                
                // Clean up test data
                const { error: deleteError } = await supabase
                    .from('gas_analytes')
                    .delete()
                    .eq('id', insertData[0].id);
                
                if (deleteError) {
                    log(`Cleanup WARNING: ${deleteError.message}`, 'warning');
                } else {
                    log('Cleanup SUCCESS: Test data removed', 'success');
                }
                
                persistenceTest.className = 'status-section status-good';
                persistenceInfo.innerHTML = `✅ Data persistence test PASSED!<br>Data was successfully saved and retrieved from the database.`;
                
            } catch (error) {
                persistenceTest.className = 'status-section status-bad';
                persistenceInfo.innerHTML = `❌ Data persistence test FAILED: ${error.message}`;
                log(`Persistence test FAILED: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = 'Test Data Persistence';
            }
        };

        window.goToLogin = function() {
            window.location.href = './simple-auth-login.html';
        };

        window.goToMainApp = function() {
            window.location.href = './app.html';
        };

        window.clearAllData = async function() {
            if (!confirm('Are you sure you want to clear all test data? This cannot be undone.')) {
                return;
            }
            
            try {
                const user = await supabase.auth.getUser();
                if (user.data.user) {
                    const { error } = await supabase
                        .from('gas_analytes')
                        .delete()
                        .eq('user_id', user.data.user.id)
                        .like('name', 'Test Analyte%');
                    
                    if (error) {
                        log(`Clear data ERROR: ${error.message}`, 'error');
                    } else {
                        log('Clear data SUCCESS: All test data removed', 'success');
                    }
                }
            } catch (error) {
                log(`Clear data ERROR: ${error.message}`, 'error');
            }
        };

        // Initialize tests
        async function init() {
            log('Initializing authentication and persistence test...');
            await checkAuthentication();
            await testDatabaseConnection();
            log('Test initialization complete');
        }

        init();
    </script>
</body>
</html>
