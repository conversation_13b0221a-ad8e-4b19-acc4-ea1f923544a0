<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Auth Redirect Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .loading { background: rgba(255, 255, 0, 0.2); }
        .success { background: rgba(0, 255, 0, 0.2); }
        .error { background: rgba(255, 0, 0, 0.2); }
        .info { background: rgba(0, 150, 255, 0.2); }
        #log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Authentication Redirect Test</h1>
        <p>This test checks if the authentication redirect is working properly.</p>

        <div id="status" class="status loading">🔄 Testing authentication redirect...</div>
        
        <div>
            <button onclick="testMainApp()" style="background: #4299e1; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;">
                Test Main App Redirect
            </button>
            <button onclick="clearLog()" style="background: #6b7280; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;">
                Clear Log
            </button>
        </div>

        <div id="log"></div>
    </div>

    <script type="module">
        import { authManager } from './auth.js';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[AUTH-TEST] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // Test authentication immediately
        async function testAuth() {
            log('🔐 Starting authentication test...');
            updateStatus('🔄 Checking authentication...', 'loading');

            try {
                const user = await authManager.init();
                
                if (user) {
                    updateStatus(`✅ User authenticated: ${user.email}`, 'success');
                    log(`User authenticated: ${user.email}`, 'success');
                    log('ℹ️ Since you are logged in, the main app should load normally');
                } else {
                    updateStatus('❌ No user authenticated', 'error');
                    log('No user authenticated - main app should redirect to login', 'error');
                    log('🔄 Testing redirect behavior...');
                    
                    // Simulate what index.html does
                    setTimeout(() => {
                        log('🚀 Redirecting to login page (as main app should do)');
                        window.location.href = './login.html';
                    }, 2000);
                }
            } catch (error) {
                updateStatus(`❌ Authentication error: ${error.message}`, 'error');
                log(`Authentication error: ${error.message}`, 'error');
            }
        }

        // Test main app access
        window.testMainApp = async function() {
            log('🏠 Testing main application access...');
            try {
                const response = await fetch('./index.html');
                if (response.ok) {
                    log('✅ Main app file accessible');
                    log('🔄 Checking if main app redirects properly...');
                    
                    // Open main app in new tab to test redirect
                    const newWindow = window.open('./index.html', '_blank');
                    if (newWindow) {
                        log('🔄 Opened main app in new tab - check if it redirects to login');
                    } else {
                        log('❌ Could not open new tab - popup blocked?');
                    }
                } else {
                    log('❌ Main app not accessible');
                }
            } catch (error) {
                log(`❌ Error testing main app: ${error.message}`, 'error');
            }
        };

        window.clearLog = function() {
            document.getElementById('log').innerHTML = '';
        };

        // Auto-run test on load
        window.addEventListener('load', () => {
            log('🧪 Auth redirect test tool loaded');
            testAuth();
        });
    </script>
</body>
</html>
