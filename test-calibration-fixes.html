<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Calibration Section - Test Fixes</title>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Google+Sans+Display:wght@400;500;600&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
    <style>
        body {
            padding: 20px;
            background-color: var(--md-surface-dim);
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: var(--md-surface);
            padding: 24px;
            border-radius: var(--md-radius-lg);
            margin-bottom: 24px;
            border: 1px solid var(--md-outline-variant);
        }
        .test-results {
            background: var(--md-surface-container);
            padding: 16px;
            border-radius: var(--md-radius-md);
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1 class="md-title-large">🧪 AI Calibration Section - Fix Validation</h1>
            <p class="md-body-medium">Testing the fixes for compound name input and layout issues</p>
        </div>

        <!-- AI Calibration Section Test -->
        <div class="test-section">
            <div class="md-card-header">
                <h2 class="md-title-large">🔬 AI Calibration Gas Capability Analysis</h2>
            </div>
            <div class="md-card-content">
                <div class="md-form-group">
                    <label for="calibrationGasStandardName" class="md-label">Calibration Gas Standard Name</label>
                    <input type="text" id="calibrationGasStandardName" class="md-input" placeholder="e.g., Daily Check Mix, Industrial Multi-Gas Standard, EPA Protocol Gas">
                    <div class="md-body-small md-text-secondary md-mt-xs">
                        💡 Enter a descriptive name for your calibration gas standard
                    </div>
                </div>
                
                <div class="md-form-group">
                    <h3 class="md-title-medium md-mb-md">Components</h3>
                    <div class="md-body-small md-text-secondary md-mb-md">
                        🧪 Add the compounds present in your calibration gas standard. Click "Add Compound" to start.
                    </div>
                    <div id="calibrationCompoundsContainer" class="md-mb-md">
                        <!-- Compound input rows will be added here -->
                    </div>
                    <button type="button" id="addCalibrationCompoundBtn" class="md-btn md-btn-outlined">
                        <span class="material-icons">add</span>
                        Add Compound
                    </button>
                    <div class="md-flex md-gap-sm md-mt-sm md-flex-wrap">
                        <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('CH4', '2500', 'ppm')">
                            + CH₄
                        </button>
                        <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('CO2', '5000', 'ppm')">
                            + CO₂
                        </button>
                        <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('H2S', '25', 'ppm')">
                            + H₂S
                        </button>
                        <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('SO2', '40', 'ppm')">
                            + SO₂
                        </button>
                        <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="addPresetCompound('NH3', '50', 'ppm')">
                            + NH₃
                        </button>
                    </div>
                    <div class="md-body-small md-text-secondary md-mt-xs">
                        💡 Quick add: Click the buttons above to add common compounds with typical concentrations, or use "Add Compound" for custom entries
                    </div>
                </div>
                
                <div id="calibrationInputError" class="md-text-error md-body-small md-hidden"></div>
                
                <button type="button" id="analyzeCalibrationBtn" class="md-btn md-btn-filled md-w-full">
                    <span class="material-icons">psychology</span>
                    Test Validation (Mock Analysis)
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h2 class="md-title-medium">📊 Test Results</h2>
            <div id="testResults" class="test-results">
                Test initialized...\n
            </div>
        </div>
    </div>

    <script>
        // Copy the enhanced functions from app-simple.html
        function addCalibrationCompoundRow() {
            const container = document.getElementById('calibrationCompoundsContainer');
            if (!container) {
                console.error('❌ Calibration compounds container not found');
                return false;
            }

            const compoundDiv = document.createElement('div');
            compoundDiv.className = 'compound-row md-flex md-gap-md md-items-center md-mb-md';
            compoundDiv.style.padding = '8px';
            compoundDiv.style.backgroundColor = 'var(--md-surface-container-lowest)';
            compoundDiv.style.borderRadius = 'var(--md-radius-md)';
            compoundDiv.style.border = '1px solid var(--md-outline-variant)';
            
            compoundDiv.innerHTML = `
                <div class="md-flex-1">
                    <label class="md-label md-body-small md-text-secondary">Compound Name</label>
                    <input type="text" class="md-input" placeholder="e.g., CH4, CO2, H2S, NH3, SO2" style="margin-top: 4px; font-size: 16px;">
                </div>
                <div style="min-width: 140px;">
                    <label class="md-label md-body-small md-text-secondary">Concentration</label>
                    <input type="number" class="md-input" placeholder="e.g., 1000" step="0.01" min="0" style="margin-top: 4px; font-size: 16px;">
                </div>
                <div style="min-width: 100px;">
                    <label class="md-label md-body-small md-text-secondary">Unit</label>
                    <select class="md-input" style="margin-top: 4px; font-size: 16px;">
                        <option value="ppm">ppm</option>
                        <option value="ppb">ppb</option>
                        <option value="mg/m³">mg/m³</option>
                        <option value="µg/m³">µg/m³</option>
                        <option value="%">%</option>
                        <option value="% LEL">% LEL</option>
                        <option value="% UEL">% UEL</option>
                        <option value="% volume">% volume</option>
                    </select>
                </div>
                <div style="padding-top: 18px;">
                    <button type="button" class="md-btn md-btn-ghost md-btn-sm" onclick="removeCalibrationCompound(this)" title="Remove compound">
                        <span class="material-icons">delete</span>
                    </button>
                </div>
            `;
            container.appendChild(compoundDiv);
            
            // Focus on the compound name input for better UX
            setTimeout(() => {
                const nameInput = compoundDiv.querySelector('input[type="text"]');
                if (nameInput) {
                    nameInput.focus();
                }
                // Enhance the newly added inputs
                enhanceCompoundInputs();
            }, 100);
            
            logTest('✅ Calibration compound row added successfully');
            return true;
        }

        function removeCalibrationCompound(button) {
            const row = button.closest('.compound-row');
            if (row) {
                // Add a smooth removal animation
                row.style.transition = 'all 0.3s ease-out';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    logTest('✅ Calibration compound row removed');
                }, 300);
            }
        }

        function addPresetCompound(name, concentration, unit) {
            // First add a new compound row
            addCalibrationCompoundRow();
            
            // Then fill it with the preset values
            setTimeout(() => {
                const rows = document.querySelectorAll('.compound-row');
                const lastRow = rows[rows.length - 1];
                if (lastRow) {
                    const inputs = lastRow.querySelectorAll('input, select');
                    if (inputs.length >= 3) {
                        inputs[0].value = name; // Compound name
                        inputs[1].value = concentration; // Concentration
                        inputs[2].value = unit; // Unit
                        
                        // Add visual feedback
                        lastRow.style.backgroundColor = 'var(--md-surface-container-high)';
                        setTimeout(() => {
                            lastRow.style.backgroundColor = 'var(--md-surface-container-lowest)';
                        }, 1000);
                        
                        logTest(`✅ Added preset compound: ${name} (${concentration} ${unit})`);
                    }
                }
            }, 100);
        }

        function enhanceCompoundInputs() {
            const compoundRows = document.querySelectorAll('.compound-row');
            compoundRows.forEach(row => {
                const nameInput = row.querySelector('input[type="text"]');
                if (nameInput && !nameInput.hasAttribute('data-enhanced')) {
                    nameInput.setAttribute('data-enhanced', 'true');
                    
                    // Add autocomplete suggestions
                    nameInput.addEventListener('input', function() {
                        const commonCompounds = [
                            'CH4', 'CO2', 'H2S', 'SO2', 'NH3', 'NO', 'NO2', 
                            'O2', 'CO', 'HCl', 'HF', 'Cl2', 'H2', 'C2H4',
                            'C2H6', 'C3H8', 'C4H10', 'Benzene', 'Toluene'
                        ];
                        
                        const value = this.value.toUpperCase();
                        if (value.length > 0) {
                            const matches = commonCompounds.filter(compound => 
                                compound.startsWith(value)
                            );
                            
                            if (matches.length > 0 && matches[0] !== value) {
                                // Add visual hint
                                this.style.backgroundColor = 'var(--md-surface-container-highest)';
                                this.title = `Suggestion: ${matches[0]}`;
                            } else {
                                this.style.backgroundColor = '';
                                this.title = '';
                            }
                        }
                    });
                    
                    // Auto-uppercase chemical formulas
                    nameInput.addEventListener('blur', function() {
                        if (this.value.match(/^[a-z0-9]+$/i)) {
                            // If it looks like a chemical formula, uppercase it
                            this.value = this.value.toUpperCase();
                        }
                        this.style.backgroundColor = '';
                        this.title = '';
                    });
                }
            });
        }

        function validateInputs() {
            const standardName = document.getElementById('calibrationGasStandardName').value.trim();
            const errorDiv = document.getElementById('calibrationInputError');
            
            errorDiv.style.display = 'none';
            errorDiv.classList.add('md-hidden');
            
            if (!standardName) {
                errorDiv.textContent = "Please enter a name for the calibration gas standard.";
                errorDiv.style.display = 'block';
                errorDiv.classList.remove('md-hidden');
                return false;
            }

            const compoundRows = document.querySelectorAll('.compound-row');
            if (compoundRows.length === 0) {
                errorDiv.textContent = "Please add at least one compound to the calibration standard.";
                errorDiv.style.display = 'block';
                errorDiv.classList.remove('md-hidden');
                return false;
            }

            let isValid = true;
            const components = [];
            
            compoundRows.forEach((row, index) => {
                const nameInput = row.querySelector('input[type="text"]');
                const concInput = row.querySelector('input[type="number"]');
                const unitSelect = row.querySelector('select');

                if (!nameInput || !concInput || !unitSelect) {
                    logTest(`❌ Missing input elements in compound row ${index + 1}`);
                    isValid = false;
                    return;
                }

                const compoundName = nameInput.value.trim();
                const concentrationStr = concInput.value.trim();
                const unit = unitSelect.value;

                if (!compoundName || !concentrationStr) {
                    isValid = false;
                    // Highlight the empty fields
                    if (!compoundName) {
                        nameInput.style.borderColor = 'var(--md-error)';
                    }
                    if (!concentrationStr) {
                        concInput.style.borderColor = 'var(--md-error)';
                    }
                    return;
                } else {
                    // Remove error styling if fields are now valid
                    nameInput.style.borderColor = '';
                    concInput.style.borderColor = '';
                }
                
                const concentrationPPM = parseFloat(concentrationStr);
                if (isNaN(concentrationPPM)) {
                    isValid = false;
                    concInput.style.borderColor = 'var(--md-error)';
                    return;
                }

                components.push({ 
                    compoundName, 
                    concentration: concentrationStr,
                    unit: unit
                });
            });

            if (!isValid) {
                errorDiv.textContent = "Please fill in all compound names and concentrations. Make sure all concentration values are valid numbers.";
                errorDiv.style.display = 'block';
                errorDiv.classList.remove('md-hidden');
                return false;
            }

            return { standardName, components };
        }

        function logTest(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `[${timestamp}] ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }

        // Initialize the test
        document.addEventListener('DOMContentLoaded', function() {
            logTest('🚀 AI Calibration Test Environment Initialized');
            
            // Set up event listeners
            document.getElementById('addCalibrationCompoundBtn').addEventListener('click', addCalibrationCompoundRow);
            
            document.getElementById('analyzeCalibrationBtn').addEventListener('click', function() {
                logTest('🔬 Testing validation...');
                const result = validateInputs();
                
                if (result) {
                    logTest(`✅ Validation passed! Standard: ${result.standardName}`);
                    logTest(`✅ Components: ${result.components.length} compounds found`);
                    result.components.forEach((comp, i) => {
                        logTest(`   ${i+1}. ${comp.compoundName}: ${comp.concentration} ${comp.unit}`);
                    });
                } else {
                    logTest('❌ Validation failed - please check the error message above');
                }
            });
            
            // Add a test compound automatically
            setTimeout(() => {
                logTest('➕ Adding initial test compound...');
                addCalibrationCompoundRow();
            }, 500);
        });
    </script>
</body>
</html>
