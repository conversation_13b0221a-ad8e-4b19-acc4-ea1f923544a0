<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Rendering Test - Alpha Gas Solution</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="elevated-saas-ui.css">
</head>
<body>
    <div class="saas-container saas-py-xl">
        <div class="saas-card">
            <div class="saas-card-header">
                <h1 class="saas-heading-lg saas-text-center">🧪 Chart Rendering Test</h1>
                <p class="saas-text-center saas-text-secondary">Testing SaaS-styled chart visualization</p>
            </div>

            <!-- Chart Legend -->
            <div class="saas-chart-legend">
                <div class="saas-legend-item">
                    <div class="saas-legend-color" style="background: linear-gradient(135deg, var(--saas-accent) 0%, #B91C1C 100%);"></div>
                    <span class="saas-legend-text">Current Capability</span>
                </div>
                <div class="saas-legend-item">
                    <div class="saas-legend-color" style="background: linear-gradient(135deg, var(--saas-success) 0%, #047857 100%);"></div>
                    <span class="saas-legend-text">Target Range</span>
                </div>
            </div>

            <div class="saas-card-content">
                <!-- Chart Container -->
                <div class="saas-chart-container">
                    <div class="saas-chart-scale">
                        <!-- Scale labels will be dynamically added here -->
                    </div>

                    <div id="chart">
                        <!-- Gas rows will be dynamically added here -->
                    </div>
                </div>
            </div>
        </div>

        <div class="saas-text-center saas-mt-lg">
            <button id="testBtn" class="saas-btn saas-btn-primary">🧪 Test Chart Rendering</button>
            <button id="refreshBtn" class="saas-btn saas-btn-secondary saas-ml-sm">🔄 Refresh Charts</button>
        </div>
    </div>

    <!-- Include necessary scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="chart.js"></script>

    <script>
        // Test chart rendering with sample data
        document.getElementById('testBtn').addEventListener('click', function() {
            console.log('🧪 Testing chart rendering...');
            
            // Initialize charts
            try {
                drawScaleLabels();
                renderChart();
                console.log('✅ Chart rendering test completed successfully');
                
                // Show success message
                const button = document.getElementById('testBtn');
                const originalText = button.textContent;
                button.textContent = '✅ Charts Rendered!';
                button.classList.remove('saas-btn-primary');
                button.classList.add('saas-btn-success');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('saas-btn-success');
                    button.classList.add('saas-btn-primary');
                }, 2000);
            } catch (error) {
                console.error('❌ Chart rendering test failed:', error);
                
                // Show error message
                const button = document.getElementById('testBtn');
                const originalText = button.textContent;
                button.textContent = '❌ Test Failed';
                button.classList.remove('saas-btn-primary');
                button.classList.add('saas-btn-error');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('saas-btn-error');
                    button.classList.add('saas-btn-primary');
                }, 2000);
            }
        });

        document.getElementById('refreshBtn').addEventListener('click', function() {
            console.log('🔄 Refreshing charts...');
            drawScaleLabels();
            renderChart();
        });

        // Auto-test on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('testBtn').click();
            }, 500);
        });
    </script>
</body>
</html>
