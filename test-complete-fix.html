<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Fix Verification - Alpha Gas Solution</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-6">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-6">🧪 Complete Fix Verification</h1>
            <p class="text-gray-600 mb-6">Testing the complete authentication timing and data persistence fix</p>
            
            <!-- Authentication Status -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-2">🔐 Authentication Status</h2>
                <div id="authStatus" class="text-sm text-gray-600">
                    <div id="authInfo">Checking authentication...</div>
                    <div id="timingInfo" class="mt-2 text-xs text-gray-500"></div>
                </div>
            </div>
            
            <!-- Test Controls -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <button id="testAuthBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    🔐 Test Authentication
                </button>
                <button id="testSaveBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                    💾 Test Save Operation
                </button>
                <button id="testCompleteFlowBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                    🚀 Test Complete Flow
                </button>
            </div>
            
            <!-- Quick Add Form (For Testing) -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">📝 Quick Add Test</h3>
                <div class="flex gap-4 items-end">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Analyte Name</label>
                        <input type="text" id="testAnalyteName" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Test Analyte">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Min (ppm)</label>
                        <input type="number" id="testMin" class="w-20 px-3 py-2 border border-gray-300 rounded-lg" placeholder="0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Max (ppm)</label>
                        <input type="number" id="testMax" class="w-20 px-3 py-2 border border-gray-300 rounded-lg" placeholder="100">
                    </div>
                    <button id="addTestAnalyteBtn" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg">
                        Add & Test Save
                    </button>
                </div>
            </div>
            
            <!-- Test Results -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">📊 Test Results</h3>
                <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Test results will appear here -->
                </div>
                <button id="clearResultsBtn" class="mt-4 bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                    Clear Results
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        // Initialize with real Supabase configuration
        const supabase = window.supabase.createClient(
            'https://xllrffehgebiaisgapdk.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE'
        );

        // Import modules
        import { authManager } from './auth.js';
        import { supabaseStorage } from './supabase-client.js';

        // Test data simulation
        let gasData = [];
        let testCount = 0;
        
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let bgColor = 'bg-gray-100';
            let textColor = 'text-gray-800';
            let icon = 'ℹ️';
            
            if (type === 'success') {
                bgColor = 'bg-green-100';
                textColor = 'text-green-800';
                icon = '✅';
            } else if (type === 'error') {
                bgColor = 'bg-red-100';
                textColor = 'text-red-800';
                icon = '❌';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-100';
                textColor = 'text-yellow-800';
                icon = '⚠️';
            } else if (type === 'timing') {
                bgColor = 'bg-blue-100';
                textColor = 'text-blue-800';
                icon = '⏱️';
            }
            
            div.className = `p-3 rounded-lg ${bgColor} ${textColor} text-sm`;
            div.innerHTML = `<span class="font-mono text-xs text-gray-500">[${timestamp}]</span> ${icon} ${message}`;
            
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateAuthStatus(status, timing = '') {
            document.getElementById('authInfo').textContent = status;
            document.getElementById('timingInfo').textContent = timing;
        }

        async function testAuthentication() {
            log('🔐 Testing authentication system...', 'info');
            const startTime = Date.now();
            
            try {
                // Test basic auth manager initialization
                log('Initializing AuthManager...', 'info');
                const initStart = Date.now();
                await authManager.init();
                const initTime = Date.now() - initStart;
                log(`AuthManager initialized in ${initTime}ms`, 'timing');
                
                // Test authentication status
                const isAuth = authManager.isAuthenticated();
                log(`Authentication status: ${isAuth ? 'Authenticated' : 'Not authenticated'}`, isAuth ? 'success' : 'warning');
                
                if (isAuth) {
                    const user = authManager.getCurrentUser();
                    log(`Current user: ${user?.email || 'Unknown'}`, 'success');
                    
                    // Test ensureReady method
                    log('Testing ensureReady() method...', 'info');
                    const readyStart = Date.now();
                    await authManager.ensureReady();
                    const readyTime = Date.now() - readyStart;
                    log(`ensureReady() completed in ${readyTime}ms`, 'timing');
                    
                    // Test getUserId consistency
                    const userId = authManager.getUserId();
                    log(`User ID retrieved: ${userId ? 'Yes' : 'No'}`, userId ? 'success' : 'error');
                    
                    updateAuthStatus(`Authenticated as ${user?.email}`, `Init: ${initTime}ms, Ready: ${readyTime}ms`);
                } else {
                    updateAuthStatus('Not authenticated - Guest mode', `Init: ${initTime}ms`);
                }
                
                const totalTime = Date.now() - startTime;
                log(`Total authentication test completed in ${totalTime}ms`, 'success');
                
            } catch (error) {
                const totalTime = Date.now() - startTime;
                log(`Authentication test failed after ${totalTime}ms: ${error.message}`, 'error');
                updateAuthStatus('Authentication test failed', `Failed after ${totalTime}ms`);
            }
        }

        async function testSaveOperation() {
            log('💾 Testing save operation with authentication timing...', 'info');
            const startTime = Date.now();
            
            try {
                // Create test data
                const testAnalyte = {
                    name: `Test Analyte ${++testCount}`,
                    current: [{ min: 0, max: 100, label: 'Test Range' }],
                    target: [{ min: 10, max: 90, label: 'Target Range' }],
                    gapNotes: 'Test gap notes',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };
                
                log(`Created test analyte: ${testAnalyte.name}`, 'info');
                
                // Check authentication before save
                if (!authManager.isAuthenticated()) {
                    log('User not authenticated - testing localStorage save', 'warning');
                    
                    // Test localStorage save for guest mode
                    gasData.push(testAnalyte);
                    localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
                    log('✅ Data saved to localStorage (guest mode)', 'success');
                    
                } else {
                    log('User authenticated - testing Supabase save', 'info');
                    
                    // Ensure authentication is ready
                    log('Ensuring authentication is ready...', 'info');
                    const readyStart = Date.now();
                    await authManager.ensureReady();
                    const readyTime = Date.now() - readyStart;
                    log(`Authentication ready check: ${readyTime}ms`, 'timing');
                    
                    // Test getUserId before save
                    const userId = authManager.getUserId();
                    if (!userId) {
                        throw new Error('User ID not available after ensureReady()');
                    }
                    log(`User ID confirmed: ${userId.substring(0, 8)}...`, 'success');
                    
                    // Add to local data and save
                    gasData.push(testAnalyte);
                    
                    // Test the actual save operation
                    log('Attempting Supabase save...', 'info');
                    const saveStart = Date.now();
                    
                    // Filter user targets for save
                    const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                    await supabaseStorage.saveUserTargets(userTargets);
                    
                    const saveTime = Date.now() - saveStart;
                    log(`✅ Data saved to Supabase in ${saveTime}ms`, 'success');
                    
                    // Also save to localStorage as cache
                    localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
                    log('✅ Data cached to localStorage', 'success');
                }
                
                const totalTime = Date.now() - startTime;
                log(`Save operation completed in ${totalTime}ms`, 'success');
                
            } catch (error) {
                const totalTime = Date.now() - startTime;
                log(`Save operation failed after ${totalTime}ms: ${error.message}`, 'error');
            }
        }

        async function testCompleteFlow() {
            log('🚀 Testing complete authentication + save flow...', 'info');
            const startTime = Date.now();
            
            try {
                // Step 1: Initialize authentication
                log('Step 1: Initializing authentication...', 'info');
                await authManager.init();
                
                // Step 2: Check status and ensure ready
                if (authManager.isAuthenticated()) {
                    log('Step 2: User authenticated, ensuring ready state...', 'info');
                    await authManager.ensureReady();
                } else {
                    log('Step 2: Guest mode detected', 'warning');
                }
                
                // Step 3: Simulate form submission
                log('Step 3: Simulating form data creation...', 'info');
                const formData = {
                    name: `Complete Flow Test ${++testCount}`,
                    current: [{ min: 5, max: 50, label: 'Current' }],
                    target: [{ min: 10, max: 40, label: 'Target' }],
                    gapNotes: 'Complete flow test notes',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };
                
                // Step 4: Add to data array (simulating chart update)
                log('Step 4: Adding to local data array...', 'info');
                gasData.push(formData);
                
                // Step 5: Attempt save operation
                log('Step 5: Attempting save operation...', 'info');
                if (authManager.isAuthenticated()) {
                    // Authenticated save
                    await authManager.ensureReady();
                    const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                    await supabaseStorage.saveUserTargets(userTargets);
                    log('✅ Authenticated save successful', 'success');
                } else {
                    // Guest save
                    localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
                    log('✅ Guest mode save successful', 'success');
                }
                
                const totalTime = Date.now() - startTime;
                log(`🎉 Complete flow test successful in ${totalTime}ms`, 'success');
                
            } catch (error) {
                const totalTime = Date.now() - startTime;
                log(`Complete flow test failed after ${totalTime}ms: ${error.message}`, 'error');
            }
        }

        async function addTestAnalyte() {
            const name = document.getElementById('testAnalyteName').value.trim();
            const min = parseFloat(document.getElementById('testMin').value) || 0;
            const max = parseFloat(document.getElementById('testMax').value) || 100;
            
            if (!name) {
                log('Please enter an analyte name', 'error');
                return;
            }
            
            log(`📝 Adding test analyte: ${name} (${min}-${max} ppm)`, 'info');
            
            try {
                // Simulate the exact form handler flow
                log('Simulating form handler process...', 'info');
                
                // Check authentication and ensure ready if authenticated
                if (authManager.isAuthenticated()) {
                    log('🔐 Ensuring authentication is ready before save...', 'info');
                    await authManager.ensureReady();
                    log('✅ Authentication confirmed ready, proceeding with save', 'success');
                }
                
                // Create analyte data
                const analyteData = {
                    name: name,
                    current: [{ min: min, max: max, label: 'Test Range' }],
                    target: [{ min: min + 5, max: max - 5, label: 'Target Range' }],
                    gapNotes: 'Added via test tool',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };
                
                // Add to array (simulating chart update)
                gasData.push(analyteData);
                log(`Added to gasData array (total: ${gasData.length})`, 'info');
                
                // Attempt save
                if (authManager.isAuthenticated()) {
                    const userTargets = gasData.filter(analyte => !analyte.is_shared && analyte.isCustom);
                    await supabaseStorage.saveUserTargets(userTargets);
                    log('✅ Saved to Supabase database', 'success');
                } else {
                    localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
                    log('✅ Saved to localStorage (guest mode)', 'success');
                }
                
                // Clear form
                document.getElementById('testAnalyteName').value = '';
                document.getElementById('testMin').value = '';
                document.getElementById('testMax').value = '';
                
                log(`🎉 Test analyte "${name}" added and saved successfully!`, 'success');
                
            } catch (error) {
                log(`Failed to add test analyte: ${error.message}`, 'error');
            }
        }

        // Event listeners
        document.getElementById('testAuthBtn').addEventListener('click', testAuthentication);
        document.getElementById('testSaveBtn').addEventListener('click', testSaveOperation);
        document.getElementById('testCompleteFlowBtn').addEventListener('click', testCompleteFlow);
        document.getElementById('addTestAnalyteBtn').addEventListener('click', addTestAnalyte);
        document.getElementById('clearResultsBtn').addEventListener('click', () => {
            document.getElementById('testResults').innerHTML = '';
        });

        // Initialize on load
        window.addEventListener('load', () => {
            log('🧪 Complete fix verification tool loaded', 'info');
            log('Click the test buttons above to verify the authentication timing fix', 'info');
            
            // Auto-run authentication test
            setTimeout(testAuthentication, 1000);
        });
    </script>
</body>
</html>
