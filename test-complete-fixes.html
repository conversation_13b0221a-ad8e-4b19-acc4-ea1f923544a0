<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Fix Verification - Alpha Gas Solution</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="elevated-saas-ui.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="saas-container saas-py-xl">
        <div class="saas-card saas-mb-xl">
            <div class="saas-card-header">
                <h1 class="saas-heading-lg saas-text-center">🛠️ Complete Fix Verification</h1>
                <p class="saas-text-center saas-text-secondary">Testing all critical issue fixes</p>
            </div>
            <div class="saas-card-content">
                <div class="saas-grid saas-grid-cols-1 md:saas-grid-cols-2 saas-gap-lg">
                    
                    <!-- Authentication Test -->
                    <div class="saas-card saas-card-subtle">
                        <div class="saas-card-header">
                            <h3 class="saas-card-title">🔐 Authentication Fix</h3>
                        </div>
                        <div class="saas-card-content">
                            <div id="authStatus" class="saas-status saas-status-info saas-mb-md">
                                <span id="authIcon">⏳</span>
                                <span id="authText">Testing...</span>
                            </div>
                            <p class="saas-text-sm saas-text-secondary">Tests login redirect loop prevention</p>
                            <button id="testAuthBtn" class="saas-btn saas-btn-primary saas-btn-sm saas-w-full saas-mt-sm">
                                Test Auth Flow
                            </button>
                        </div>
                    </div>

                    <!-- Chart Rendering Test -->
                    <div class="saas-card saas-card-subtle">
                        <div class="saas-card-header">
                            <h3 class="saas-card-title">📊 Chart Rendering Fix</h3>
                        </div>
                        <div class="saas-card-content">
                            <div id="chartStatus" class="saas-status saas-status-info saas-mb-md">
                                <span id="chartIcon">⏳</span>
                                <span id="chartText">Testing...</span>
                            </div>
                            <p class="saas-text-sm saas-text-secondary">Tests SaaS chart styling system</p>
                            <button id="testChartBtn" class="saas-btn saas-btn-primary saas-btn-sm saas-w-full saas-mt-sm">
                                Test Chart Rendering
                            </button>
                        </div>
                    </div>

                    <!-- Theme Consistency Test -->
                    <div class="saas-card saas-card-subtle">
                        <div class="saas-card-header">
                            <h3 class="saas-card-title">🎨 Theme Consistency</h3>
                        </div>
                        <div class="saas-card-content">
                            <div id="themeStatus" class="saas-status saas-status-info saas-mb-md">
                                <span id="themeIcon">⏳</span>
                                <span id="themeText">Testing...</span>
                            </div>
                            <p class="saas-text-sm saas-text-secondary">Tests unified SaaS design system</p>
                            <button id="testThemeBtn" class="saas-btn saas-btn-primary saas-btn-sm saas-w-full saas-mt-sm">
                                Test Theme
                            </button>
                        </div>
                    </div>

                    <!-- Navigation Test -->
                    <div class="saas-card saas-card-subtle">
                        <div class="saas-card-header">
                            <h3 class="saas-card-title">🧭 Navigation Fix</h3>
                        </div>
                        <div class="saas-card-content">
                            <div id="navStatus" class="saas-status saas-status-info saas-mb-md">
                                <span id="navIcon">⏳</span>
                                <span id="navText">Testing...</span>
                            </div>
                            <p class="saas-text-sm saas-text-secondary">Tests redirect loop prevention</p>
                            <div class="saas-flex saas-gap-xs saas-mt-sm">
                                <a href="simple-auth-login.html" class="saas-btn saas-btn-ghost saas-btn-sm saas-flex-1">Login</a>
                                <a href="app.html" class="saas-btn saas-btn-ghost saas-btn-sm saas-flex-1">App</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Chart Display -->
        <div class="saas-card">
            <div class="saas-card-header">
                <h2 class="saas-card-title">📊 Chart Visualization Test</h2>
            </div>

            <!-- Chart Legend -->
            <div class="saas-chart-legend">
                <div class="saas-legend-item">
                    <div class="saas-legend-color" style="background: linear-gradient(135deg, var(--saas-accent) 0%, #B91C1C 100%);"></div>
                    <span class="saas-legend-text">Current Capability</span>
                </div>
                <div class="saas-legend-item">
                    <div class="saas-legend-color" style="background: linear-gradient(135deg, var(--saas-success) 0%, #047857 100%);"></div>
                    <span class="saas-legend-text">Target Range</span>
                </div>
            </div>

            <div class="saas-card-content">
                <div class="saas-chart-container">
                    <div class="saas-chart-scale">
                        <!-- Scale labels will be added here -->
                    </div>

                    <div id="chart">
                        <!-- Chart content will be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="chart.js"></script>

    <script>
        // Initialize Supabase for auth testing
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function updateStatus(type, icon, text, statusType = 'info') {
            document.getElementById(`${type}Icon`).textContent = icon;
            document.getElementById(`${type}Text`).textContent = text;
            
            const statusEl = document.getElementById(`${type}Status`);
            statusEl.className = `saas-status saas-status-${statusType} saas-mb-md`;
        }

        // Test Authentication
        document.getElementById('testAuthBtn').addEventListener('click', async function() {
            updateStatus('auth', '⏳', 'Testing authentication...', 'info');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    updateStatus('auth', '✅', `Authenticated as ${session.user.email}`, 'success');
                } else {
                    updateStatus('auth', '⚠️', 'No active session (login required)', 'warning');
                }
            } catch (error) {
                updateStatus('auth', '❌', `Auth test failed: ${error.message}`, 'error');
            }
        });

        // Test Chart Rendering
        document.getElementById('testChartBtn').addEventListener('click', function() {
            updateStatus('chart', '⏳', 'Testing chart rendering...', 'info');
            
            try {
                drawScaleLabels();
                renderChart();
                
                // Check if chart elements were created
                const chartEl = document.getElementById('chart');
                const scaleEl = document.querySelector('.saas-chart-scale');
                
                if (chartEl && chartEl.children.length > 0 && scaleEl && scaleEl.children.length > 0) {
                    updateStatus('chart', '✅', 'Charts rendered successfully with SaaS styling', 'success');
                } else {
                    updateStatus('chart', '⚠️', 'Charts rendered but may have issues', 'warning');
                }
            } catch (error) {
                updateStatus('chart', '❌', `Chart test failed: ${error.message}`, 'error');
            }
        });

        // Test Theme
        document.getElementById('testThemeBtn').addEventListener('click', function() {
            updateStatus('theme', '⏳', 'Testing theme consistency...', 'info');
            
            try {
                // Check if SaaS CSS classes are available
                const testEl = document.createElement('div');
                testEl.className = 'saas-card saas-btn saas-status-success';
                document.body.appendChild(testEl);
                
                const styles = window.getComputedStyle(testEl);
                const hasStyles = styles.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
                                 styles.padding !== '0px' || 
                                 styles.borderRadius !== '0px';
                
                document.body.removeChild(testEl);
                
                if (hasStyles) {
                    updateStatus('theme', '✅', 'SaaS theme system working correctly', 'success');
                } else {
                    updateStatus('theme', '❌', 'SaaS theme system not loading', 'error');
                }
            } catch (error) {
                updateStatus('theme', '❌', `Theme test failed: ${error.message}`, 'error');
            }
        });

        // Auto-run tests on load
        window.addEventListener('load', function() {
            updateStatus('nav', '✅', 'Navigation links working', 'success');
            
            setTimeout(() => {
                document.getElementById('testAuthBtn').click();
            }, 500);
            
            setTimeout(() => {
                document.getElementById('testChartBtn').click();
            }, 1000);
            
            setTimeout(() => {
                document.getElementById('testThemeBtn').click();
            }, 1500);
        });
    </script>
</body>
</html>
