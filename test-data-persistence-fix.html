<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Persistence Fix Verification</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .log-success { color: #059669; background-color: #d1fae5; padding: 8px; border-radius: 4px; margin: 4px 0; }
        .log-error { color: #dc2626; background-color: #fee2e2; padding: 8px; border-radius: 4px; margin: 4px 0; }
        .log-info { color: #2563eb; background-color: #dbeafe; padding: 8px; border-radius: 4px; margin: 4px 0; }
        .log-warning { color: #d97706; background-color: #fef3c7; padding: 8px; border-radius: 4px; margin: 4px 0; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">🔧 Data Persistence Fix Verification</h1>
        <p class="text-gray-600 mb-6">Testing the fix for user-added analytes persisting after refresh</p>

        <!-- Test Steps -->
        <div class="space-y-4">
            <div class="border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold mb-3">Step 1: Authentication Check</h2>
                <div id="authResults" class="space-y-2">
                    <div class="log-info">Checking authentication status...</div>
                </div>
                <button id="authBtn" class="bg-blue-500 text-white px-4 py-2 rounded mt-2">Check Auth</button>
            </div>

            <div class="border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold mb-3">Step 2: Test New Analyte Properties</h2>
                <div id="analyteResults" class="space-y-2">
                    <div class="log-info">Ready to test analyte creation with proper hybrid properties</div>
                </div>
                <input type="text" id="testAnalyteName" placeholder="Test Analyte Name" class="border p-2 rounded mr-2">
                <button id="testAnalyteBtn" class="bg-green-500 text-white px-4 py-2 rounded">Test Analyte Creation</button>
            </div>

            <div class="border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold mb-3">Step 3: Verify Save to Database</h2>
                <div id="saveResults" class="space-y-2">
                    <div class="log-info">Ready to test save operation</div>
                </div>
                <button id="testSaveBtn" class="bg-purple-500 text-white px-4 py-2 rounded">Test Save & Verify</button>
            </div>

            <div class="border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold mb-3">Step 4: Database Inspection</h2>
                <div id="dbResults" class="space-y-2">
                    <div class="log-info">Ready to inspect database contents</div>
                </div>
                <button id="inspectDbBtn" class="bg-orange-500 text-white px-4 py-2 rounded">Inspect Database</button>
            </div>
        </div>

        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold mb-2">Quick Links:</h3>
            <a href="./direct-login-simple.html" target="_blank" class="text-blue-600 hover:underline mr-4">🔐 Login Page</a>
            <a href="./guest-mode-simple.html" target="_blank" class="text-blue-600 hover:underline mr-4">🏠 Main App</a>
            <a href="https://analysis-capabilities-ags.vercel.app/" target="_blank" class="text-blue-600 hover:underline">🌐 Live App</a>
        </div>
    </div>

    <script type="module">
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        let currentUser = null;

        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `log-${type}`;
            div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            container.appendChild(div);
        }

        // Test authentication
        document.getElementById('authBtn').addEventListener('click', async () => {
            const container = document.getElementById('authResults');
            container.innerHTML = '';

            try {
                addLog('authResults', 'Checking Supabase authentication...', 'info');
                
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    addLog('authResults', `Auth error: ${error.message}`, 'error');
                    return;
                }

                if (session?.user) {
                    currentUser = session.user;
                    addLog('authResults', `✅ Authenticated as: ${session.user.email}`, 'success');
                    addLog('authResults', `User ID: ${session.user.id}`, 'info');
                } else {
                    addLog('authResults', '❌ Not authenticated - please login first', 'warning');
                    addLog('authResults', 'Go to login page to create account or sign in', 'info');
                }
            } catch (error) {
                addLog('authResults', `Authentication check failed: ${error.message}`, 'error');
            }
        });

        // Test analyte creation with proper properties
        document.getElementById('testAnalyteBtn').addEventListener('click', async () => {
            const container = document.getElementById('analyteResults');
            const analyteName = document.getElementById('testAnalyteName').value.trim();

            if (!analyteName) {
                addLog('analyteResults', '⚠️ Please enter an analyte name', 'warning');
                return;
            }

            if (!currentUser) {
                addLog('analyteResults', '❌ Please authenticate first', 'error');
                return;
            }

            try {
                addLog('analyteResults', `🧪 Creating test analyte: ${analyteName}`, 'info');

                // Create analyte with proper hybrid properties (like the fixed form-handler.js)
                const newAnalyte = {
                    name: analyteName,
                    current: [{ min: 10, max: 100, label: "Test current range" }],
                    target: [{ min: 1, max: 1000, label: "Test target range" }],
                    gapNotes: "Test analyte created by fix verification",
                    isCustom: true,           // ✅ Fixed property
                    is_shared: false,         // ✅ Fixed property  
                    data_type: 'user_target'  // ✅ Fixed property
                };

                addLog('analyteResults', '✅ Analyte created with proper hybrid properties:', 'success');
                addLog('analyteResults', `- isCustom: ${newAnalyte.isCustom}`, 'info');
                addLog('analyteResults', `- is_shared: ${newAnalyte.is_shared}`, 'info');
                addLog('analyteResults', `- data_type: ${newAnalyte.data_type}`, 'info');

                // Store for save test
                window.testAnalyte = newAnalyte;

            } catch (error) {
                addLog('analyteResults', `❌ Error creating analyte: ${error.message}`, 'error');
            }
        });

        // Test save operation
        document.getElementById('testSaveBtn').addEventListener('click', async () => {
            const container = document.getElementById('saveResults');

            if (!window.testAnalyte) {
                addLog('saveResults', '⚠️ Please create a test analyte first', 'warning');
                return;
            }

            if (!currentUser) {
                addLog('saveResults', '❌ Please authenticate first', 'error');
                return;
            }

            try {
                addLog('saveResults', '💾 Testing save operation to Supabase...', 'info');

                // Simulate the fixed saveUserTargets function
                const userTargets = [window.testAnalyte];
                
                // Filter test (this should now pass)
                const filteredTargets = userTargets.filter(analyte => !analyte.is_shared && analyte.isCustom);
                addLog('saveResults', `🔍 Filter test: ${filteredTargets.length} of ${userTargets.length} analytes passed filter`, filteredTargets.length > 0 ? 'success' : 'error');

                if (filteredTargets.length === 0) {
                    addLog('saveResults', '❌ FILTER FAILED: Analyte would be filtered out during save!', 'error');
                    return;
                }

                // Prepare data for Supabase insert
                const dataToInsert = filteredTargets.map(analyte => ({
                    user_id: currentUser.id,
                    name: analyte.name,
                    current_ranges: analyte.current || [],
                    target_ranges: analyte.target || [],
                    gap_notes: analyte.gapNotes || '',
                    is_custom: analyte.isCustom !== false,
                    is_shared: false,
                    data_type: 'user_target'
                }));

                addLog('saveResults', '📤 Attempting to save to Supabase...', 'info');

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .insert(dataToInsert)
                    .select();

                if (error) {
                    addLog('saveResults', `❌ Save error: ${error.message}`, 'error');
                    return;
                }

                addLog('saveResults', '✅ Successfully saved to Supabase!', 'success');
                addLog('saveResults', `📊 Inserted ${data.length} record(s)`, 'info');
                addLog('saveResults', `Record ID: ${data[0]?.id}`, 'info');

            } catch (error) {
                addLog('saveResults', `❌ Save operation failed: ${error.message}`, 'error');
            }
        });

        // Inspect database
        document.getElementById('inspectDbBtn').addEventListener('click', async () => {
            const container = document.getElementById('dbResults');

            if (!currentUser) {
                addLog('dbResults', '❌ Please authenticate first', 'error');
                return;
            }

            try {
                addLog('dbResults', '🔍 Inspecting database for user-specific data...', 'info');

                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('name, is_shared, is_custom, data_type, created_at')
                    .eq('user_id', currentUser.id)
                    .eq('is_shared', false)
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) {
                    addLog('dbResults', `❌ Database query error: ${error.message}`, 'error');
                    return;
                }

                addLog('dbResults', `📊 Found ${data.length} user-specific analyte(s)`, data.length > 0 ? 'success' : 'warning');

                if (data.length > 0) {
                    data.forEach((analyte, index) => {
                        addLog('dbResults', `${index + 1}. ${analyte.name} (${analyte.data_type}) - ${new Date(analyte.created_at).toLocaleString()}`, 'info');
                    });
                } else {
                    addLog('dbResults', 'No user-specific analytes found in database', 'info');
                }

            } catch (error) {
                addLog('dbResults', `❌ Database inspection failed: ${error.message}`, 'error');
            }
        });

        // Auto-check auth on load
        setTimeout(() => {
            document.getElementById('authBtn').click();
        }, 1000);
    </script>
</body>
</html>
