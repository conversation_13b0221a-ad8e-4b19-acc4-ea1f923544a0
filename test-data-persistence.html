<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a8b;
        }
        .results {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007cba;
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Data Persistence Test</h1>
        <p>This tool tests the data persistence pipeline step by step.</p>

        <div class="test-section">
            <h3>Step 1: Test Data Addition</h3>
            <div class="step">
                <button onclick="testAddAnalyte()">Add Test Analyte</button>
                <button onclick="checkGasData()">Check gasData Array</button>
            </div>
            <div id="step1Results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Test Save Operation</h3>
            <div class="step">
                <button onclick="testSaveOperation()">Test Save to Storage</button>
                <button onclick="checkLocalStorage()">Check localStorage</button>
            </div>
            <div id="step2Results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Load Operation</h3>
            <div class="step">
                <button onclick="testLoadOperation()">Test Load from Storage</button>
                <button onclick="simulateRefresh()">Simulate Page Refresh</button>
            </div>
            <div id="step3Results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Step 4: Test Supabase Connection</h3>
            <div class="step">
                <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
                <button onclick="checkSupabaseData()">Check Supabase Data</button>
            </div>
            <div id="step4Results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Console Logs</h3>
            <div id="consoleLogs" class="results"></div>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <script type="module">
        import { supabase } from './supabase-client.js';
        import { supabaseStorage } from './supabase-client.js';

        let gasData = [];
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;

        // Capture console logs
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            appendToLogs('LOG: ' + args.join(' '));
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            appendToLogs('ERROR: ' + args.join(' '));
        };

        function appendToLogs(message) {
            const logElement = document.getElementById('consoleLogs');
            logElement.textContent += new Date().toLocaleTimeString() + ' - ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showResults(elementId, content) {
            document.getElementById(elementId).textContent = content;
        }

        // Test functions
        window.testAddAnalyte = function() {
            const testAnalyte = {
                name: "Test Analyte " + Date.now(),
                current: [{ min: 10, max: 100, label: "Test Current Range" }],
                target: [{ min: 1, max: 1000, label: "Test Target Range" }],
                gapNotes: "Test gap notes",
                isCustom: true
            };

            console.log('Adding test analyte:', testAnalyte);
            gasData.push(testAnalyte);
            
            showResults('step1Results', 
                `Test analyte added successfully!\n` +
                `gasData length: ${gasData.length}\n` +
                `Last analyte: ${JSON.stringify(testAnalyte, null, 2)}`
            );
        };

        window.checkGasData = function() {
            showResults('step1Results', 
                `gasData array contents:\n` +
                `Length: ${gasData.length}\n` +
                `Data: ${JSON.stringify(gasData, null, 2)}`
            );
        };

        window.testSaveOperation = async function() {
            try {
                console.log('Testing save operation...');
                
                // Save to localStorage
                localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
                console.log('Saved to localStorage');
                
                // Save to Supabase
                await supabaseStorage.saveData(gasData);
                console.log('Saved to Supabase');
                
                showResults('step2Results', 
                    `Save operation completed successfully!\n` +
                    `Data saved to both localStorage and Supabase\n` +
                    `Saved ${gasData.length} analytes`
                );
            } catch (error) {
                console.error('Save operation failed:', error);
                showResults('step2Results', `Save operation failed: ${error.message}`);
            }
        };

        window.checkLocalStorage = function() {
            const localData = localStorage.getItem('alphaGasSolution_gasData');
            if (localData) {
                const parsed = JSON.parse(localData);
                showResults('step2Results', 
                    `localStorage data found:\n` +
                    `Length: ${parsed.length}\n` +
                    `Data: ${JSON.stringify(parsed, null, 2)}`
                );
            } else {
                showResults('step2Results', 'No data found in localStorage');
            }
        };

        window.testLoadOperation = async function() {
            try {
                console.log('Testing load operation...');
                
                // Clear current data
                gasData.length = 0;
                console.log('Cleared gasData array');
                
                // Load from Supabase
                const loadedData = await supabaseStorage.loadData();
                if (loadedData && loadedData.length > 0) {
                    gasData.push(...loadedData);
                    console.log('Loaded from Supabase');
                } else {
                    // Fallback to localStorage
                    const localData = localStorage.getItem('alphaGasSolution_gasData');
                    if (localData) {
                        const parsed = JSON.parse(localData);
                        gasData.push(...parsed);
                        console.log('Loaded from localStorage');
                    }
                }
                
                showResults('step3Results', 
                    `Load operation completed!\n` +
                    `Loaded ${gasData.length} analytes\n` +
                    `Data: ${JSON.stringify(gasData, null, 2)}`
                );
            } catch (error) {
                console.error('Load operation failed:', error);
                showResults('step3Results', `Load operation failed: ${error.message}`);
            }
        };

        window.simulateRefresh = function() {
            console.log('Simulating page refresh...');
            gasData.length = 0;
            showResults('step3Results', 'gasData cleared (simulating refresh). Click "Test Load Operation" to see if data persists.');
        };

        window.testSupabaseConnection = async function() {
            try {
                console.log('Testing Supabase connection...');
                
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .limit(1);
                
                if (error) {
                    throw error;
                }
                
                showResults('step4Results', 
                    `Supabase connection successful!\n` +
                    `Connection test passed\n` +
                    `Response: ${JSON.stringify(data, null, 2)}`
                );
            } catch (error) {
                console.error('Supabase connection failed:', error);
                showResults('step4Results', `Supabase connection failed: ${error.message}`);
            }
        };

        window.checkSupabaseData = async function() {
            try {
                console.log('Checking Supabase data...');
                
                const sessionId = localStorage.getItem('gas_analysis_session_id');
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('*')
                    .eq('user_session', sessionId)
                    .order('created_at');
                
                if (error) {
                    throw error;
                }
                
                showResults('step4Results', 
                    `Supabase data check completed!\n` +
                    `Session ID: ${sessionId}\n` +
                    `Records found: ${data ? data.length : 0}\n` +
                    `Data: ${JSON.stringify(data, null, 2)}`
                );
            } catch (error) {
                console.error('Supabase data check failed:', error);
                showResults('step4Results', `Supabase data check failed: ${error.message}`);
            }
        };

        window.clearLogs = function() {
            document.getElementById('consoleLogs').textContent = '';
        };

        // Initialize
        console.log('Data persistence test tool initialized');
    </script>
</body>
</html>
