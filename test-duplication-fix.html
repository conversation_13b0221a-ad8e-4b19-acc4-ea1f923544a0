<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Duplication Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .test-btn { background-color: #007bff; color: white; }
        .clear-btn { background-color: #dc3545; color: white; }
        input[type="text"] { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🧪 Duplication Fix Test</h1>
    
    <div class="test-section">
        <h2>Test Form Handler Initialization</h2>
        <div id="initializationResult" class="result info">
            Checking form handler initialization...
        </div>
        <button class="test-btn" onclick="testInitialization()">Test Initialization</button>
    </div>

    <div class="test-section">
        <h2>Test Add Analyte (Check for Duplication)</h2>
        <p>Add a test analyte and check if only 1 entry is created:</p>
        <input type="text" id="testAnalyteName" placeholder="Test Analyte Name" value="Test Analyte">
        <button class="test-btn" onclick="testAddAnalyte()">Add Test Analyte</button>
        <button class="clear-btn" onclick="clearTestData()">Clear Test Data</button>
        
        <div id="addAnalyteResult" class="result info">
            Ready to test...
        </div>
        
        <div id="gasDataStatus" class="result info">
            Current gasData length: <span id="gasDataLength">Loading...</span>
        </div>
    </div>

    <div class="test-section">
        <h2>Current gasData Contents</h2>
        <pre id="gasDataContents" style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;"></pre>
        <button class="test-btn" onclick="refreshGasData()">Refresh Data View</button>
    </div>

    <!-- Include necessary scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="form-handler.js"></script>

    <script>
        // Mock form elements for testing
        document.body.innerHTML += `
            <div style="display: none;">
                <form id="addAnalyteForm" data-initialized="false">
                    <input id="analyteName" type="text">
                    <textarea id="gapNotes"></textarea>
                    <div id="currentRangesContainer">
                        <div class="range-input-group">
                            <input type="number" class="range-min" value="1">
                            <input type="number" class="range-max" value="10">
                            <input type="text" class="range-label" value="Test Range">
                        </div>
                    </div>
                    <div id="targetRangesContainer">
                        <div class="range-input-group">
                            <input type="number" class="range-min" value="0.1">
                            <input type="number" class="range-max" value="100">
                            <input type="text" class="range-label" value="Target Range">
                        </div>
                    </div>
                </form>
                <div id="analyteNameError"></div>
            </div>
        `;

        // Mock functions
        window.renderChart = function() { console.log('Chart rendered'); };
        window.updateSaveStatus = function(status, message, temp) { 
            console.log(`Save Status: ${status} - ${message}`); 
        };
        window.authManager = {
            isAuthenticated: () => false,
            ensureReady: async () => true
        };
        window.saveDataToLocalStorage = async function() {
            console.log('Mock save to localStorage');
        };

        let initialGasDataLength = 0;

        function testInitialization() {
            const result = document.getElementById('initializationResult');
            const form = document.getElementById('addAnalyteForm');
            
            // Reset the form initialization status
            form.removeAttribute('data-initialized');
            
            try {
                // Test multiple initializations
                console.log('Testing first initialization...');
                initializeFormHandler();
                const firstInit = form.hasAttribute('data-initialized');
                
                console.log('Testing second initialization...');
                initializeFormHandler();
                const secondInit = form.hasAttribute('data-initialized');
                
                if (firstInit && secondInit) {
                    result.className = 'result success';
                    result.innerHTML = '✅ Form handler initialization working correctly.<br>First init: ✅<br>Second init prevented: ✅';
                } else {
                    result.className = 'result error';
                    result.innerHTML = `❌ Form handler initialization issue.<br>First init: ${firstInit ? '✅' : '❌'}<br>Second init prevented: ${secondInit ? '✅' : '❌'}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ Error during initialization test: ${error.message}`;
            }
        }

        function testAddAnalyte() {
            const testName = document.getElementById('testAnalyteName').value;
            const result = document.getElementById('addAnalyteResult');
            
            if (!testName) {
                result.className = 'result error';
                result.innerHTML = '❌ Please enter a test analyte name';
                return;
            }

            // Record initial state
            const initialLength = gasData.length;
            const initialNames = gasData.map(g => g.name);
            
            console.log('Before adding analyte:', {
                length: initialLength,
                names: initialNames
            });

            // Set form values
            document.getElementById('analyteName').value = testName;
            document.getElementById('gapNotes').value = 'Test notes';

            // Simulate form submission
            const form = document.getElementById('addAnalyteForm');
            const event = new Event('submit', { bubbles: true, cancelable: true });
            
            try {
                form.dispatchEvent(event);
                
                // Check results after a short delay
                setTimeout(() => {
                    const finalLength = gasData.length;
                    const finalNames = gasData.map(g => g.name);
                    const addedCount = finalLength - initialLength;
                    
                    console.log('After adding analyte:', {
                        length: finalLength,
                        names: finalNames,
                        addedCount: addedCount
                    });

                    if (addedCount === 1) {
                        result.className = 'result success';
                        result.innerHTML = `✅ SUCCESS: Added exactly 1 analyte<br>Before: ${initialLength} analytes<br>After: ${finalLength} analytes<br>Added: "${testName}"`;
                    } else if (addedCount === 2) {
                        result.className = 'result error';
                        result.innerHTML = `❌ DUPLICATION DETECTED: Added ${addedCount} analytes instead of 1<br>Before: ${initialLength} analytes<br>After: ${finalLength} analytes`;
                    } else {
                        result.className = 'result error';
                        result.innerHTML = `❌ UNEXPECTED: Added ${addedCount} analytes<br>Before: ${initialLength} analytes<br>After: ${finalLength} analytes`;
                    }
                    
                    refreshGasData();
                }, 100);
                
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ Error during test: ${error.message}`;
            }
        }

        function clearTestData() {
            // Remove any test analytes
            gasData = gasData.filter(g => !g.name.toLowerCase().includes('test'));
            refreshGasData();
            
            const result = document.getElementById('addAnalyteResult');
            result.className = 'result info';
            result.innerHTML = 'Test data cleared. Ready for new test.';
        }

        function refreshGasData() {
            const lengthElement = document.getElementById('gasDataLength');
            const contentsElement = document.getElementById('gasDataContents');
            
            lengthElement.textContent = gasData.length;
            
            const displayData = gasData.map((gas, index) => ({
                index: index,
                name: gas.name,
                isCustom: gas.isCustom || false,
                currentRanges: gas.current ? gas.current.length : 0,
                targetRanges: gas.target ? gas.target.length : 0
            }));
            
            contentsElement.textContent = JSON.stringify(displayData, null, 2);
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            initialGasDataLength = gasData.length;
            refreshGasData();
            
            // Initialize form handler for testing
            initializeFormHandler();
        });
    </script>
</body>
</html>
