<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Hybrid System Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .status-success { color: #059669; background-color: #d1fae5; }
        .status-error { color: #dc2626; background-color: #fee2e2; }
        .status-warning { color: #d97706; background-color: #fef3c7; }
        .status-info { color: #2563eb; background-color: #dbeafe; }
        .test-section { border: 1px solid #e5e7eb; border-radius: 8px; margin: 16px 0; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">🧪 Complete Hybrid System Test</h1>
        <p class="text-gray-600 mb-6">Testing the complete authentication → hybrid data loading → chart rendering flow</p>

        <!-- Authentication Test -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">🔐 Authentication Test</h2>
            <div id="authStatus" class="p-3 rounded-md mb-3 bg-gray-100">Checking authentication...</div>
            <button id="loginBtn" class="bg-blue-500 text-white px-4 py-2 rounded mr-2" style="display: none;">Go to Login</button>
            <button id="signOutBtn" class="bg-red-500 text-white px-4 py-2 rounded" style="display: none;">Sign Out</button>
        </div>

        <!-- Database Connection Test -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">🔗 Database Connection Test</h2>
            <div id="dbStatus" class="p-3 rounded-md mb-3 bg-gray-100">Not tested yet</div>
            <button id="testDbBtn" class="bg-blue-500 text-white px-4 py-2 rounded">Test Connection</button>
        </div>

        <!-- Shared Data Verification -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">📊 Shared Data Verification</h2>
            <div id="sharedDataStatus" class="p-3 rounded-md mb-3 bg-gray-100">Not tested yet</div>
            <button id="testSharedDataBtn" class="bg-blue-500 text-white px-4 py-2 rounded">Check Shared Data</button>
        </div>

        <!-- Hybrid Data Loading Test -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">🔄 Hybrid Data Loading Test</h2>
            <div id="hybridDataStatus" class="p-3 rounded-md mb-3 bg-gray-100">Not tested yet</div>
            <button id="testHybridDataBtn" class="bg-blue-500 text-white px-4 py-2 rounded">Test Hybrid Loading</button>
            <div id="hybridDataDetails" class="mt-3 text-sm"></div>
        </div>

        <!-- User Target Management Test -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">👤 User Target Management Test</h2>
            <div id="userTargetStatus" class="p-3 rounded-md mb-3 bg-gray-100">Not tested yet</div>
            <div class="flex gap-2 mb-3">
                <input id="testAnalyteName" type="text" placeholder="Test Analyte Name" class="border rounded px-3 py-2 flex-1">
                <button id="addTestTargetBtn" class="bg-green-500 text-white px-4 py-2 rounded">Add Test Target</button>
            </div>
            <button id="clearUserDataBtn" class="bg-red-500 text-white px-4 py-2 rounded">Clear User Data</button>
        </div>

        <!-- Main App Integration Test -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">🚀 Main App Integration Test</h2>
            <div id="integrationStatus" class="p-3 rounded-md mb-3 bg-gray-100">Not tested yet</div>
            <button id="testIntegrationBtn" class="bg-purple-500 text-white px-4 py-2 rounded">Test Integration</button>
        </div>

        <!-- Test Logs -->
        <div class="test-section p-4">
            <h2 class="text-lg font-semibold mb-3">📝 Test Logs</h2>
            <div id="testLogs" class="bg-gray-50 p-4 rounded-md text-sm font-mono max-h-64 overflow-y-auto border"></div>
            <button id="clearLogsBtn" class="bg-gray-500 text-white px-4 py-2 rounded mt-2">Clear Logs</button>
        </div>
    </div>

    <script type="module">
        import { authManager } from './auth.js';
        import { supabaseStorage, supabase } from './supabase-client.js';

        let currentUser = null;

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `p-3 rounded-md mb-3 status-${type}`;
        }

        function log(message, type = 'info') {
            const logs = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logs.textContent += logEntry;
            logs.scrollTop = logs.scrollHeight;
            console.log(`[HYBRID-TEST] ${message}`);
        }

        // Test authentication
        async function testAuth() {
            log('🔐 Testing authentication...');
            try {
                currentUser = await authManager.init();
                
                if (currentUser) {
                    updateStatus('authStatus', `✅ Authenticated as: ${currentUser.email}`, 'success');
                    log(`Authentication successful: ${currentUser.email}`, 'success');
                    document.getElementById('signOutBtn').style.display = 'inline-block';
                    document.getElementById('loginBtn').style.display = 'none';
                    return true;
                } else {
                    updateStatus('authStatus', '❌ Not authenticated', 'error');
                    log('User not authenticated', 'error');
                    document.getElementById('loginBtn').style.display = 'inline-block';
                    document.getElementById('signOutBtn').style.display = 'none';
                    return false;
                }
            } catch (error) {
                updateStatus('authStatus', `❌ Auth error: ${error.message}`, 'error');
                log(`Authentication error: ${error.message}`, 'error');
                return false;
            }
        }

        // Test database connection
        async function testDbConnection() {
            if (!currentUser) {
                updateStatus('dbStatus', '❌ Must be authenticated first', 'error');
                return;
            }

            log('🔗 Testing database connection...');
            try {
                const isConnected = await supabaseStorage.testConnection();
                if (isConnected) {
                    updateStatus('dbStatus', '✅ Database connection successful', 'success');
                    log('Database connection test passed', 'success');
                } else {
                    updateStatus('dbStatus', '❌ Database connection failed', 'error');
                    log('Database connection test failed', 'error');
                }
            } catch (error) {
                updateStatus('dbStatus', `❌ Connection error: ${error.message}`, 'error');
                log(`Database connection error: ${error.message}`, 'error');
            }
        }

        // Test shared data verification
        async function testSharedData() {
            if (!currentUser) {
                updateStatus('sharedDataStatus', '❌ Must be authenticated first', 'error');
                return;
            }

            log('📊 Checking shared data...');
            try {
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('name, is_shared, data_type')
                    .eq('is_shared', true);

                if (error) throw error;

                const sharedCount = data?.length || 0;
                updateStatus('sharedDataStatus', `✅ Found ${sharedCount} shared capabilities`, 'success');
                log(`Shared data verification: ${sharedCount} shared capabilities found`, 'success');
                
                if (data && data.length > 0) {
                    log(`Shared analytes: ${data.map(d => d.name).join(', ')}`);
                }
            } catch (error) {
                updateStatus('sharedDataStatus', `❌ Error checking shared data: ${error.message}`, 'error');
                log(`Shared data check error: ${error.message}`, 'error');
            }
        }

        // Test hybrid data loading
        async function testHybridDataLoading() {
            if (!currentUser) {
                updateStatus('hybridDataStatus', '❌ Must be authenticated first', 'error');
                return;
            }

            log('🔄 Testing hybrid data loading...');
            try {
                const hybridData = await supabaseStorage.loadHybridData();
                
                const sharedData = hybridData.filter(d => d.is_shared);
                const userData = hybridData.filter(d => !d.is_shared);
                
                updateStatus('hybridDataStatus', 
                    `✅ Loaded ${hybridData.length} analytes (${sharedData.length} shared, ${userData.length} user)`, 
                    'success'
                );
                
                log(`Hybrid data loading successful:`, 'success');
                log(`- Total analytes: ${hybridData.length}`);
                log(`- Shared capabilities: ${sharedData.length}`);
                log(`- User targets: ${userData.length}`);

                // Display details
                const details = document.getElementById('hybridDataDetails');
                details.innerHTML = `
                    <div class="bg-blue-50 p-3 rounded mt-2">
                        <h4 class="font-semibold text-blue-800">Shared Capabilities (${sharedData.length}):</h4>
                        <p class="text-blue-700 text-sm">${sharedData.map(d => d.name).join(', ')}</p>
                    </div>
                    ${userData.length > 0 ? `
                    <div class="bg-green-50 p-3 rounded mt-2">
                        <h4 class="font-semibold text-green-800">User Targets (${userData.length}):</h4>
                        <p class="text-green-700 text-sm">${userData.map(d => d.name).join(', ')}</p>
                    </div>
                    ` : '<div class="bg-gray-50 p-3 rounded mt-2 text-gray-600">No user-specific targets</div>'}
                `;
                
            } catch (error) {
                updateStatus('hybridDataStatus', `❌ Hybrid loading error: ${error.message}`, 'error');
                log(`Hybrid data loading error: ${error.message}`, 'error');
            }
        }

        // Add test user target
        async function addTestTarget() {
            if (!currentUser) {
                updateStatus('userTargetStatus', '❌ Must be authenticated first', 'error');
                return;
            }

            const analyteName = document.getElementById('testAnalyteName').value.trim();
            if (!analyteName) {
                updateStatus('userTargetStatus', '❌ Please enter an analyte name', 'error');
                return;
            }

            log(`👤 Adding test target: ${analyteName}...`);
            try {
                const testTargets = [{
                    name: analyteName,
                    current: [{ min: 1, max: 100, label: "1-100 ppm (Test)" }],
                    target: [{ min: 0.1, max: 1000, label: "0.1-1000 ppm (Test Target)" }],
                    gapNotes: "Test analyte for hybrid system verification",
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                }];

                await supabaseStorage.saveUserTargets(testTargets);
                updateStatus('userTargetStatus', `✅ Test target "${analyteName}" added successfully`, 'success');
                log(`Test target added successfully: ${analyteName}`, 'success');
                
                // Clear input
                document.getElementById('testAnalyteName').value = '';
                
            } catch (error) {
                updateStatus('userTargetStatus', `❌ Error adding test target: ${error.message}`, 'error');
                log(`Error adding test target: ${error.message}`, 'error');
            }
        }

        // Clear user data
        async function clearUserData() {
            if (!currentUser) {
                updateStatus('userTargetStatus', '❌ Must be authenticated first', 'error');
                return;
            }

            if (!confirm('Clear all your user data? This will preserve shared capabilities.')) {
                return;
            }

            log('🗑️ Clearing user data...');
            try {
                await supabaseStorage.clearUserData();
                updateStatus('userTargetStatus', '✅ User data cleared successfully', 'success');
                log('User data cleared successfully (shared capabilities preserved)', 'success');
            } catch (error) {
                updateStatus('userTargetStatus', `❌ Error clearing user data: ${error.message}`, 'error');
                log(`Error clearing user data: ${error.message}`, 'error');
            }
        }

        // Test main app integration
        async function testIntegration() {
            log('🚀 Testing main app integration...');
            
            // Test if main app files are accessible
            try {
                const response = await fetch('./index.html');
                if (response.ok) {
                    updateStatus('integrationStatus', '✅ Main app accessible and ready for testing!', 'success');
                    log('Main app integration test successful', 'success');
                    
                    // Provide link to main app
                    const element = document.getElementById('integrationStatus');
                    element.innerHTML = `✅ Main app accessible and ready for testing!<br>
                        <a href="./index.html" target="_blank" style="color: #2563eb; text-decoration: underline; font-weight: bold;">🚀 Open Main Application</a>`;
                } else {
                    throw new Error('Main app not accessible');
                }
            } catch (error) {
                updateStatus('integrationStatus', `❌ Main app integration test failed: ${error.message}`, 'error');
                log(`Main app integration test failed: ${error.message}`, 'error');
            }
        }

        // Event listeners
        document.getElementById('loginBtn').addEventListener('click', () => {
            window.location.href = './login.html';
        });

        document.getElementById('signOutBtn').addEventListener('click', async () => {
            try {
                await authManager.signOut();
                location.reload();
            } catch (error) {
                log(`Sign out error: ${error.message}`, 'error');
            }
        });

        document.getElementById('testDbBtn').addEventListener('click', testDbConnection);
        document.getElementById('testSharedDataBtn').addEventListener('click', testSharedData);
        document.getElementById('testHybridDataBtn').addEventListener('click', testHybridDataLoading);
        document.getElementById('addTestTargetBtn').addEventListener('click', addTestTarget);
        document.getElementById('clearUserDataBtn').addEventListener('click', clearUserData);
        document.getElementById('testIntegrationBtn').addEventListener('click', testIntegration);
        
        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            document.getElementById('testLogs').textContent = '';
        });

        // Auto-run authentication test on load
        window.addEventListener('load', () => {
            log('🧪 Hybrid system test tool loaded');
            log('📋 Test sequence: Auth → Database → Shared Data → Hybrid Loading → Integration');
            testAuth();
        });
    </script>
</body>
</html>
