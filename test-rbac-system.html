<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RBAC System Test Suite</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .test-pass { color: #059669; background-color: #d1fae5; }
        .test-fail { color: #dc2626; background-color: #fee2e2; }
        .test-warn { color: #d97706; background-color: #fef3c7; }
        .test-info { color: #2563eb; background-color: #dbeafe; }
        .test-log { padding: 8px; border-radius: 4px; margin: 4px 0; font-family: monospace; font-size: 12px; }
        .test-section { border: 2px solid #e5e7eb; border-radius: 8px; margin: 16px 0; }
        .test-section.running { border-color: #3b82f6; }
        .test-section.passed { border-color: #10b981; }
        .test-section.failed { border-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">🛡️ RBAC System Test Suite</h1>
        <p class="text-gray-600 mb-6">Comprehensive testing for Role-Based Access Control implementation</p>

        <!-- Test Controls -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="flex flex-wrap gap-2 mb-4">
                <button id="runAllTests" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    🚀 Run All Tests
                </button>
                <button id="clearResults" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    🗑️ Clear Results
                </button>
                <button id="exportResults" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    📄 Export Results
                </button>
            </div>
            <div class="text-sm text-gray-600">
                <div id="testProgress" class="mb-2">Ready to run tests...</div>
                <div id="testSummary" class="font-medium"></div>
            </div>
        </div>

        <!-- Test Results Container -->
        <div id="testResults" class="space-y-4">
            <!-- Test sections will be dynamically added here -->
        </div>

        <!-- Quick Links -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold mb-2">Test Environment Links:</h3>
            <div class="flex flex-wrap gap-4 text-sm">
                <a href="./direct-login-simple.html" target="_blank" class="text-blue-600 hover:underline">Login Page</a>
                <a href="./guest-mode-simple.html" target="_blank" class="text-blue-600 hover:underline">Main App</a>
                <a href="./admin-dashboard.html" target="_blank" class="text-blue-600 hover:underline">Admin Dashboard</a>
                <a href="./test-data-persistence-fix.html" target="_blank" class="text-blue-600 hover:underline">Data Persistence Test</a>
            </div>
        </div>
    </div>

    <script type="module">
        // Import modules
        import { authManager } from './auth.js';
        import { roleManager } from './role-manager.js';

        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Test state
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        // Utility functions
        function addLog(sectionId, message, type = 'info') {
            const section = document.getElementById(sectionId);
            const logContainer = section.querySelector('.test-logs');
            
            const div = document.createElement('div');
            div.className = `test-log test-${type}`;
            div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(div);
            
            // Auto-scroll to bottom
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function createTestSection(id, title, description) {
            const container = document.getElementById('testResults');
            const section = document.createElement('div');
            section.id = id;
            section.className = 'test-section p-4';
            
            section.innerHTML = `
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold">${title}</h3>
                    <div class="test-status px-3 py-1 rounded text-sm font-medium bg-gray-200">Pending</div>
                </div>
                <p class="text-gray-600 mb-3">${description}</p>
                <div class="test-logs max-h-60 overflow-y-auto bg-gray-50 p-2 rounded border"></div>
            `;
            
            container.appendChild(section);
            return section;
        }

        function updateTestStatus(sectionId, status, message = '') {
            const section = document.getElementById(sectionId);
            const statusElement = section.querySelector('.test-status');
            
            section.className = `test-section p-4 ${status}`;
            
            switch (status) {
                case 'running':
                    statusElement.textContent = 'Running...';
                    statusElement.className = 'test-status px-3 py-1 rounded text-sm font-medium bg-blue-200 text-blue-800';
                    break;
                case 'passed':
                    statusElement.textContent = 'Passed ✅';
                    statusElement.className = 'test-status px-3 py-1 rounded text-sm font-medium bg-green-200 text-green-800';
                    testResults.passed++;
                    break;
                case 'failed':
                    statusElement.textContent = 'Failed ❌';
                    statusElement.className = 'test-status px-3 py-1 rounded text-sm font-medium bg-red-200 text-red-800';
                    testResults.failed++;
                    break;
            }
            
            if (message) {
                addLog(sectionId, message, status === 'passed' ? 'pass' : 'fail');
            }
        }

        // Test Suites
        async function testDatabaseSchema() {
            const sectionId = 'dbSchemaTest';
            createTestSection(sectionId, '🗄️ Database Schema Tests', 'Verify database tables, columns, and constraints');
            updateTestStatus(sectionId, 'running');

            try {
                addLog(sectionId, 'Testing user_profiles table structure...', 'info');
                
                // Test user_profiles table
                const { data: profiles, error: profileError } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .limit(1);

                if (profileError) {
                    addLog(sectionId, `user_profiles table error: ${profileError.message}`, 'fail');
                    updateTestStatus(sectionId, 'failed', 'user_profiles table not accessible');
                    return false;
                }

                addLog(sectionId, '✅ user_profiles table accessible', 'pass');

                // Test admin_audit_log table
                addLog(sectionId, 'Testing admin_audit_log table structure...', 'info');
                const { data: auditLog, error: auditError } = await supabase
                    .from('admin_audit_log')
                    .select('*')
                    .limit(1);

                if (auditError) {
                    addLog(sectionId, `admin_audit_log table error: ${auditError.message}`, 'fail');
                    updateTestStatus(sectionId, 'failed', 'admin_audit_log table not accessible');
                    return false;
                }

                addLog(sectionId, '✅ admin_audit_log table accessible', 'pass');

                // Test database functions
                addLog(sectionId, 'Testing database functions...', 'info');
                const { data: roleData, error: roleError } = await supabase
                    .rpc('get_user_role');

                if (roleError && !roleError.message.includes('permission denied')) {
                    addLog(sectionId, `get_user_role function error: ${roleError.message}`, 'warn');
                } else {
                    addLog(sectionId, '✅ get_user_role function exists', 'pass');
                }

                updateTestStatus(sectionId, 'passed', 'Database schema tests completed successfully');
                return true;

            } catch (error) {
                addLog(sectionId, `Schema test failed: ${error.message}`, 'fail');
                updateTestStatus(sectionId, 'failed', 'Database schema test failed');
                return false;
            }
        }

        async function testAuthentication() {
            const sectionId = 'authTest';
            createTestSection(sectionId, '🔐 Authentication Tests', 'Test authentication manager functionality');
            updateTestStatus(sectionId, 'running');

            try {
                addLog(sectionId, 'Testing AuthManager initialization...', 'info');
                
                // Test auth manager initialization
                if (!authManager) {
                    addLog(sectionId, 'AuthManager not available', 'fail');
                    updateTestStatus(sectionId, 'failed', 'AuthManager not initialized');
                    return false;
                }

                addLog(sectionId, '✅ AuthManager available', 'pass');

                // Test authentication status
                addLog(sectionId, 'Testing current authentication status...', 'info');
                const isAuth = authManager.isAuthenticated();
                addLog(sectionId, `Authentication status: ${isAuth}`, isAuth ? 'pass' : 'warn');

                if (isAuth) {
                    // Test user profile retrieval
                    addLog(sectionId, 'Testing user profile retrieval...', 'info');
                    const profile = await authManager.getUserProfile();
                    
                    if (profile) {
                        addLog(sectionId, `✅ User profile retrieved: ${profile.role} (${profile.status})`, 'pass');
                        
                        // Test role-specific methods
                        const isAdmin = await authManager.isAdmin();
                        const isApproved = await authManager.isApproved();
                        const userRole = await authManager.getUserRole();
                        
                        addLog(sectionId, `Role checks - Admin: ${isAdmin}, Approved: ${isApproved}, Role: ${userRole}`, 'info');
                    } else {
                        addLog(sectionId, '⚠️ User profile not found', 'warn');
                    }
                } else {
                    addLog(sectionId, '⚠️ User not authenticated - some tests skipped', 'warn');
                }

                updateTestStatus(sectionId, 'passed', 'Authentication tests completed');
                return true;

            } catch (error) {
                addLog(sectionId, `Authentication test failed: ${error.message}`, 'fail');
                updateTestStatus(sectionId, 'failed', 'Authentication test failed');
                return false;
            }
        }

        async function testRoleManager() {
            const sectionId = 'roleTest';
            createTestSection(sectionId, '👑 Role Manager Tests', 'Test role-based access control functionality');
            updateTestStatus(sectionId, 'running');

            try {
                addLog(sectionId, 'Testing RoleManager initialization...', 'info');
                
                if (!roleManager) {
                    addLog(sectionId, 'RoleManager not available', 'fail');
                    updateTestStatus(sectionId, 'failed', 'RoleManager not initialized');
                    return false;
                }

                addLog(sectionId, '✅ RoleManager available', 'pass');

                // Initialize role manager
                addLog(sectionId, 'Initializing role manager...', 'info');
                await roleManager.init();
                addLog(sectionId, '✅ RoleManager initialized', 'pass');

                // Test role and status retrieval
                const currentRole = roleManager.getRole();
                const currentStatus = roleManager.getStatus();
                addLog(sectionId, `Current role: ${currentRole}, Status: ${currentStatus}`, 'info');

                // Test access controls
                addLog(sectionId, 'Testing access controls...', 'info');
                const features = ['admin_dashboard', 'data_persistence', 'user_features', 'guest_features'];
                
                for (const feature of features) {
                    const hasAccess = roleManager.hasAccess(feature);
                    addLog(sectionId, `${feature}: ${hasAccess ? '✅ Access granted' : '❌ Access denied'}`, hasAccess ? 'pass' : 'info');
                }

                // Test UI restrictions
                addLog(sectionId, 'Testing UI restrictions...', 'info');
                const restrictions = roleManager.getUIRestrictions();
                addLog(sectionId, `UI restrictions: ${JSON.stringify(restrictions)}`, 'info');

                // Test role messages
                addLog(sectionId, 'Testing role messages...', 'info');
                const messages = roleManager.getRoleMessages();
                addLog(sectionId, `Role message: ${messages.title} - ${messages.message}`, 'info');

                updateTestStatus(sectionId, 'passed', 'Role manager tests completed successfully');
                return true;

            } catch (error) {
                addLog(sectionId, `Role manager test failed: ${error.message}`, 'fail');
                updateTestStatus(sectionId, 'failed', 'Role manager test failed');
                return false;
            }
        }

        async function testDataPersistence() {
            const sectionId = 'dataTest';
            createTestSection(sectionId, '💾 Data Persistence Tests', 'Test role-based data persistence and RLS policies');
            updateTestStatus(sectionId, 'running');

            try {
                addLog(sectionId, 'Testing data persistence with role-based access...', 'info');

                // Test if user is authenticated
                if (!authManager.isAuthenticated()) {
                    addLog(sectionId, '⚠️ User not authenticated - testing guest mode only', 'warn');
                    updateTestStatus(sectionId, 'passed', 'Data persistence test completed (guest mode)');
                    return true;
                }

                // Test analyte creation with proper hybrid properties
                addLog(sectionId, 'Testing analyte creation with hybrid properties...', 'info');
                const testAnalyte = {
                    name: `RBAC_Test_${Date.now()}`,
                    current: [{ min: 10, max: 100, label: "Test range" }],
                    target: [{ min: 1, max: 1000, label: "Test target" }],
                    gapNotes: "RBAC system test analyte",
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                addLog(sectionId, '✅ Test analyte created with proper properties', 'pass');

                // Test filtering logic
                const userTargets = [testAnalyte];
                const filteredTargets = userTargets.filter(analyte => !analyte.is_shared && analyte.isCustom);
                
                if (filteredTargets.length === 1) {
                    addLog(sectionId, '✅ Filtering logic works correctly', 'pass');
                } else {
                    addLog(sectionId, '❌ Filtering logic failed', 'fail');
                    updateTestStatus(sectionId, 'failed', 'Data filtering failed');
                    return false;
                }

                // Test database insertion (if user has permissions)
                try {
                    const currentUser = authManager.getCurrentUser();
                    if (currentUser) {
                        const dataToInsert = {
                            user_id: currentUser.id,
                            name: testAnalyte.name,
                            current_ranges: testAnalyte.current,
                            target_ranges: testAnalyte.target,
                            gap_notes: testAnalyte.gapNotes,
                            is_custom: testAnalyte.isCustom,
                            is_shared: false,
                            data_type: 'user_target'
                        };

                        const { data, error } = await supabase
                            .from('gas_analytes')
                            .insert(dataToInsert)
                            .select();

                        if (error) {
                            addLog(sectionId, `Database insertion error: ${error.message}`, 'fail');
                        } else {
                            addLog(sectionId, '✅ Data successfully inserted to database', 'pass');
                            
                            // Clean up test data
                            await supabase
                                .from('gas_analytes')
                                .delete()
                                .eq('id', data[0].id);
                            
                            addLog(sectionId, '✅ Test data cleaned up', 'pass');
                        }
                    }
                } catch (dbError) {
                    addLog(sectionId, `Database test error: ${dbError.message}`, 'warn');
                }

                updateTestStatus(sectionId, 'passed', 'Data persistence tests completed');
                return true;

            } catch (error) {
                addLog(sectionId, `Data persistence test failed: ${error.message}`, 'fail');
                updateTestStatus(sectionId, 'failed', 'Data persistence test failed');
                return false;
            }
        }

        async function testAdminFeatures() {
            const sectionId = 'adminTest';
            createTestSection(sectionId, '🛡️ Admin Features Tests', 'Test admin-specific functionality and access controls');
            updateTestStatus(sectionId, 'running');

            try {
                addLog(sectionId, 'Testing admin feature access...', 'info');

                // Check if current user is admin
                const isAdmin = await authManager.isAdmin();
                addLog(sectionId, `Admin status: ${isAdmin}`, isAdmin ? 'pass' : 'info');

                if (!isAdmin) {
                    addLog(sectionId, '⚠️ User is not admin - testing access restrictions', 'warn');
                    
                    // Test that non-admin users can't access admin features
                    const hasAdminAccess = roleManager.hasAccess('admin_dashboard');
                    if (!hasAdminAccess) {
                        addLog(sectionId, '✅ Admin access correctly restricted for non-admin users', 'pass');
                    } else {
                        addLog(sectionId, '❌ Admin access not properly restricted', 'fail');
                        updateTestStatus(sectionId, 'failed', 'Admin access control failed');
                        return false;
                    }
                } else {
                    addLog(sectionId, '✅ Admin user detected - testing admin features', 'pass');
                    
                    // Test admin dashboard access
                    const hasAdminAccess = roleManager.hasAccess('admin_dashboard');
                    if (hasAdminAccess) {
                        addLog(sectionId, '✅ Admin dashboard access granted', 'pass');
                    } else {
                        addLog(sectionId, '❌ Admin dashboard access denied for admin user', 'fail');
                    }

                    // Test admin audit log access
                    try {
                        const { data, error } = await supabase
                            .from('admin_audit_log')
                            .select('*')
                            .limit(5);

                        if (error) {
                            addLog(sectionId, `Admin audit log access error: ${error.message}`, 'warn');
                        } else {
                            addLog(sectionId, `✅ Admin audit log accessible (${data.length} records)`, 'pass');
                        }
                    } catch (auditError) {
                        addLog(sectionId, `Audit log test error: ${auditError.message}`, 'warn');
                    }
                }

                updateTestStatus(sectionId, 'passed', 'Admin features tests completed');
                return true;

            } catch (error) {
                addLog(sectionId, `Admin features test failed: ${error.message}`, 'fail');
                updateTestStatus(sectionId, 'failed', 'Admin features test failed');
                return false;
            }
        }

        async function testUIIntegration() {
            const sectionId = 'uiTest';
            createTestSection(sectionId, '🖥️ UI Integration Tests', 'Test role-based UI modifications and restrictions');
            updateTestStatus(sectionId, 'running');

            try {
                addLog(sectionId, 'Testing UI integration...', 'info');

                // Test role manager UI setup
                addLog(sectionId, 'Testing role manager UI setup...', 'info');
                const uiSetup = roleManager.setupUI();
                
                if (uiSetup && uiSetup.restrictions) {
                    addLog(sectionId, '✅ UI restrictions applied', 'pass');
                    addLog(sectionId, `Restrictions: ${JSON.stringify(uiSetup.restrictions)}`, 'info');
                } else {
                    addLog(sectionId, '❌ UI setup failed', 'fail');
                    updateTestStatus(sectionId, 'failed', 'UI setup failed');
                    return false;
                }

                // Test notification system
                addLog(sectionId, 'Testing notification system...', 'info');
                const messages = roleManager.getRoleMessages();
                
                // Temporarily show notification for testing
                roleManager.showRoleNotification(messages);
                
                // Check if notification was created
                const notification = document.getElementById('roleNotification');
                if (notification) {
                    addLog(sectionId, '✅ Role notification system working', 'pass');
                    // Remove test notification after a moment
                    setTimeout(() => notification.remove(), 2000);
                } else {
                    addLog(sectionId, '⚠️ Role notification not displayed (may be intentional for guest users)', 'warn');
                }

                // Test menu modifications
                addLog(sectionId, 'Testing menu modifications...', 'info');
                const userMenu = document.getElementById('userMenu') || document.getElementById('guestMenu');
                if (userMenu) {
                    addLog(sectionId, '✅ User menu found', 'pass');
                } else {
                    addLog(sectionId, '⚠️ User menu not found in test environment', 'warn');
                }

                updateTestStatus(sectionId, 'passed', 'UI integration tests completed');
                return true;

            } catch (error) {
                addLog(sectionId, `UI integration test failed: ${error.message}`, 'fail');
                updateTestStatus(sectionId, 'failed', 'UI integration test failed');
                return false;
            }
        }

        // Test runner
        async function runAllTests() {
            console.log('🚀 Starting RBAC System Test Suite...');
            
            // Reset test results
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            
            // Clear previous results
            document.getElementById('testResults').innerHTML = '';
            
            // Update progress
            document.getElementById('testProgress').textContent = 'Running tests...';
            
            const tests = [
                { name: 'Database Schema', fn: testDatabaseSchema },
                { name: 'Authentication', fn: testAuthentication },
                { name: 'Role Manager', fn: testRoleManager },
                { name: 'Data Persistence', fn: testDataPersistence },
                { name: 'Admin Features', fn: testAdminFeatures },
                { name: 'UI Integration', fn: testUIIntegration }
            ];

            testResults.total = tests.length;
            
            for (const test of tests) {
                try {
                    console.log(`Running test: ${test.name}`);
                    await test.fn();
                    await new Promise(resolve => setTimeout(resolve, 500)); // Brief pause between tests
                } catch (error) {
                    console.error(`Test ${test.name} failed:`, error);
                    testResults.failed++;
                }
            }

            // Update final summary
            const summary = `Tests completed: ${testResults.passed} passed, ${testResults.failed} failed, ${testResults.warnings} warnings`;
            document.getElementById('testSummary').textContent = summary;
            document.getElementById('testProgress').textContent = 'All tests completed';
            
            console.log('✅ RBAC System Test Suite completed');
            console.log(summary);
        }

        // Event listeners
        document.getElementById('runAllTests').addEventListener('click', runAllTests);
        
        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('testProgress').textContent = 'Results cleared';
            document.getElementById('testSummary').textContent = '';
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
        });

        document.getElementById('exportResults').addEventListener('click', () => {
            const results = {
                timestamp: new Date().toISOString(),
                summary: testResults,
                details: Array.from(document.querySelectorAll('.test-section')).map(section => ({
                    title: section.querySelector('h3').textContent,
                    status: section.querySelector('.test-status').textContent,
                    logs: Array.from(section.querySelectorAll('.test-log')).map(log => log.textContent)
                }))
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rbac-test-results-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);
        });

        // Auto-run tests on page load
        setTimeout(() => {
            console.log('Auto-starting RBAC tests...');
            runAllTests();
        }, 1000);
    </script>
</body>
</html>
