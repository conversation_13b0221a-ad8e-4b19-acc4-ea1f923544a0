<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remove Button Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .test-btn { background-color: #007bff; color: white; }
        .remove-btn { background-color: #dc3545; color: white; }
        .add-btn { background-color: #28a745; color: white; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .chart-container { border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
        .gas-row { display: flex; align-items: center; padding: 5px; border-bottom: 1px solid #eee; }
        .gas-name { width: 200px; font-weight: bold; position: relative; }
        .delete-analyte-btn { 
            margin-left: 10px; 
            background: #dc3545; 
            color: white; 
            border: none; 
            padding: 2px 6px; 
            border-radius: 3px; 
            cursor: pointer; 
            font-size: 12px;
        }
        .bar-area { flex: 1; height: 20px; background: #f0f0f0; position: relative; }
        .gap-notes-section { width: 200px; padding-left: 10px; font-size: 12px; color: #666; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🗑️ Remove Button Functionality Test</h1>
    
    <div class="test-section">
        <h2>Current Gas Data Status</h2>
        <div id="gasDataStatus" class="result info">
            Total analytes: <span id="totalAnalytes">Loading...</span><br>
            Original analytes: <span id="originalAnalytes">14</span><br>
            Custom analytes: <span id="customAnalytes">Loading...</span>
        </div>
        <button class="test-btn" onclick="refreshStatus()">Refresh Status</button>
    </div>

    <div class="test-section">
        <h2>Add Test Analytes</h2>
        <p>Add some test analytes to test the remove functionality:</p>
        <input type="text" id="newAnalyteName" placeholder="Analyte Name" value="Test Analyte">
        <button class="add-btn" onclick="addTestAnalyte()">Add Test Analyte</button>
        <div id="addResult" class="result info">Ready to add test analytes...</div>
    </div>

    <div class="test-section">
        <h2>Chart with Remove Buttons</h2>
        <div class="chart-container">
            <div id="chart"></div>
        </div>
        <button class="test-btn" onclick="renderChart()">Refresh Chart</button>
    </div>

    <div class="test-section">
        <h2>Remove Button Tests</h2>
        <button class="test-btn" onclick="testRemoveButtons()">Test Remove Button Presence</button>
        <button class="remove-btn" onclick="removeAllCustom()">Remove All Custom Analytes</button>
        <div id="removeTestResult" class="result info">Click "Test Remove Button Presence" to check functionality...</div>
    </div>

    <div class="test-section">
        <h2>Current Data Dump</h2>
        <pre id="dataDump"></pre>
        <button class="test-btn" onclick="refreshDataDump()">Refresh Data</button>
    </div>

    <!-- Include necessary scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script src="chart.js"></script>

    <script>
        // Mock authentication and storage functions
        window.authManager = {
            isAuthenticated: () => false,
            ensureReady: async () => true
        };
        
        window.saveDataToLocalStorage = async function() {
            console.log('Mock save to localStorage');
            localStorage.setItem('alphaGasSolution_gasData', JSON.stringify(gasData));
        };

        // Mock supabaseStorage for testing
        window.supabaseStorage = {
            deleteAnalyte: async (name) => {
                console.log(`Mock delete from Supabase: ${name}`);
            }
        };

        function refreshStatus() {
            const totalElement = document.getElementById('totalAnalytes');
            const customElement = document.getElementById('customAnalytes');
            
            const originalCount = getOriginalAnalytesCount();
            const totalCount = gasData.length;
            const customCount = totalCount - originalCount;
            
            totalElement.textContent = totalCount;
            customElement.textContent = Math.max(0, customCount);
        }

        function addTestAnalyte() {
            const nameInput = document.getElementById('newAnalyteName');
            const name = nameInput.value.trim();
            const result = document.getElementById('addResult');
            
            if (!name) {
                result.className = 'result error';
                result.innerHTML = '❌ Please enter an analyte name';
                return;
            }

            // Check for duplicates
            if (gasData.some(g => g.name === name)) {
                result.className = 'result error';
                result.innerHTML = `❌ Analyte "${name}" already exists`;
                return;
            }

            // Add the analyte
            gasData.push({
                name: name,
                current: [{ min: 1, max: 10, label: 'Current Range' }],
                target: [{ min: 0.1, max: 100, label: 'Target Range' }],
                gapNotes: `Test notes for ${name}`,
                isCustom: true,
                is_shared: false,
                data_type: 'user_target'
            });

            result.className = 'result success';
            result.innerHTML = `✅ Added "${name}" successfully`;
            
            // Auto-increment name for next addition
            const match = name.match(/(\d+)$/);
            if (match) {
                const num = parseInt(match[1]) + 1;
                nameInput.value = name.replace(/\d+$/, num);
            } else {
                nameInput.value = name + ' 2';
            }

            refreshStatus();
            renderChart();
            refreshDataDump();
        }

        function testRemoveButtons() {
            const result = document.getElementById('removeTestResult');
            const originalCount = getOriginalAnalytesCount();
            
            // Check if remove buttons are present for custom analytes only
            const gasRows = document.querySelectorAll('.gas-row');
            let buttonsFound = 0;
            let buttonsExpected = 0;
            let incorrectButtons = 0;
            
            gasRows.forEach((row, index) => {
                const deleteBtn = row.querySelector('.delete-analyte-btn');
                
                if (index >= originalCount) {
                    // Should have a delete button
                    buttonsExpected++;
                    if (deleteBtn) {
                        buttonsFound++;
                    }
                } else {
                    // Should NOT have a delete button
                    if (deleteBtn) {
                        incorrectButtons++;
                    }
                }
            });

            const resultHtml = `
                <strong>Remove Button Test Results:</strong><br>
                Total gas rows: ${gasRows.length}<br>
                Original analytes (no buttons expected): ${originalCount}<br>
                Custom analytes (buttons expected): ${buttonsExpected}<br>
                Remove buttons found: ${buttonsFound}<br>
                Incorrect buttons on original analytes: ${incorrectButtons}
            `;

            if (buttonsFound === buttonsExpected && incorrectButtons === 0) {
                result.className = 'result success';
                result.innerHTML = '✅ ' + resultHtml + '<br><strong>All remove buttons are correctly placed!</strong>';
            } else {
                result.className = 'result error';
                result.innerHTML = '❌ ' + resultHtml + '<br><strong>Remove button placement issues detected!</strong>';
            }
        }

        function removeAllCustom() {
            const originalCount = getOriginalAnalytesCount();
            const customCount = gasData.length - originalCount;
            
            if (customCount === 0) {
                const result = document.getElementById('removeTestResult');
                result.className = 'result info';
                result.innerHTML = 'ℹ️ No custom analytes to remove';
                return;
            }

            if (confirm(`Remove all ${customCount} custom analytes?`)) {
                gasData.splice(originalCount);
                refreshStatus();
                renderChart();
                refreshDataDump();
                
                const result = document.getElementById('removeTestResult');
                result.className = 'result success';
                result.innerHTML = `✅ Removed ${customCount} custom analytes`;
            }
        }

        function refreshDataDump() {
            const dump = document.getElementById('dataDump');
            const originalCount = getOriginalAnalytesCount();
            
            const displayData = gasData.map((gas, index) => ({
                index: index,
                name: gas.name,
                type: index >= originalCount ? 'CUSTOM' : 'ORIGINAL',
                hasRemoveButton: index >= originalCount,
                currentRanges: gas.current ? gas.current.length : 0,
                targetRanges: gas.target ? gas.target.length : 0,
                isCustom: gas.isCustom || false
            }));
            
            dump.textContent = JSON.stringify(displayData, null, 2);
        }

        // Test specific delete functionality
        async function testDeleteFunction() {
            const originalCount = getOriginalAnalytesCount();
            if (gasData.length <= originalCount) {
                alert('Add some test analytes first to test delete functionality');
                return;
            }

            const indexToDelete = gasData.length - 1; // Delete the last one
            const nameToDelete = gasData[indexToDelete].name;
            
            console.log(`Testing delete of index ${indexToDelete}: "${nameToDelete}"`);
            
            try {
                await deleteAnalyte(indexToDelete);
                console.log('Delete function completed successfully');
                refreshStatus();
                renderChart();
                refreshDataDump();
            } catch (error) {
                console.error('Delete function failed:', error);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Remove button test page loaded');
            refreshStatus();
            renderChart();
            refreshDataDump();
        });

        // Add test delete button
        document.body.innerHTML += `
            <div class="test-section">
                <h2>Direct Delete Function Test</h2>
                <button class="remove-btn" onclick="testDeleteFunction()">Test Delete Function</button>
                <p><small>This will delete the last custom analyte using the deleteAnalyte() function directly.</small></p>
            </div>
        `;
    </script>
</body>
</html>
