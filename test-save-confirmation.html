<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Save Confirmation Test - Alpha Gas Solution</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .log-info { color: #374151; background-color: #f3f4f6; padding: 8px; border-radius: 4px; margin: 4px 0; }
        .log-success { color: #059669; background-color: #d1fae5; padding: 8px; border-radius: 4px; margin: 4px 0; }
        .log-error { color: #dc2626; background-color: #fee2e2; padding: 8px; border-radius: 4px; margin: 4px 0; }
        .log-warning { color: #d97706; background-color: #fef3c7; padding: 8px; border-radius: 4px; margin: 4px 0; }
    </style>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">🔧 Save Confirmation Feature Test</h1>
        <p class="text-gray-600 mb-6">Testing the new save confirmation button and authentication status indicators</p>

        <!-- Test Controls -->
        <div class="space-y-4">
            <div class="border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold mb-3">Test Form with Save Confirmation</h2>
                
                <!-- Simplified form for testing -->
                <form id="addAnalyteForm" class="space-y-4">
                    <div>
                        <label for="analyteName" class="form-label">Test Analyte Name:</label>
                        <input type="text" id="analyteName" class="saas-input" value="Test Save Feature" required>
                    </div>

                    <div id="currentRangesContainer" class="dynamic-ranges-container">
                        <!-- Current range inputs will be added here -->
                    </div>
                    
                    <div id="targetRangesContainer" class="dynamic-ranges-container">
                        <!-- Target range inputs will be added here -->
                    </div>

                    <div>
                        <label for="gapNotes" class="form-label">Gap Notes:</label>
                        <textarea id="gapNotes" class="saas-textarea">Testing save confirmation feature</textarea>
                    </div>

                    <!-- Save Status and Controls -->
                    <div class="form-section" id="saveControlsSection">
                        <div class="save-status-container" id="saveStatusContainer">
                            <div class="auth-status" id="authStatus">
                                <span class="status-indicator" id="authIndicator">⚡</span>
                                <span id="authStatusText">Checking authentication...</span>
                            </div>
                            <div class="save-status" id="saveStatus" style="display: none;">
                                <span class="status-indicator" id="saveIndicator">💾</span>
                                <span id="saveStatusText">Ready to save</span>
                            </div>
                        </div>
                        
                        <div class="form-buttons-row">
                            <button type="submit" class="submit-btn" id="addAnalyteBtn">📊 Add to Chart (Local)</button>
                            <button type="button" class="save-btn" id="confirmSaveBtn" title="Manually confirm data persistence to database">
                                <span id="saveButtonText">💾 Confirm Save to Database</span>
                            </button>
                        </div>
                        
                        <div class="save-info" id="saveInfo">
                            <p class="text-sm text-gray-600">
                                <strong>Guest Mode:</strong> Data saved locally only (lost on refresh)<br>
                                <strong>Authenticated:</strong> Data persisted to database + local cache
                            </p>
                        </div>
                    </div>
                </form>
            </div>

            <div class="border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold mb-3">Test Results</h2>
                <div id="testResults" class="space-y-2">
                    <div class="log-info">Ready to test save confirmation feature</div>
                </div>
                <button id="testBtn" class="bg-blue-500 text-white px-4 py-2 rounded mt-3">Run Feature Test</button>
            </div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="config.js"></script>
    <script src="data.js"></script>
    <script type="module" src="supabase-client.js"></script>
    <script type="module" src="auth.js"></script>
    <script type="module" src="storage.js"></script>
    <script src="form-handler.js"></script>

    <script type="module">
        import { authManager } from './auth.js';

        function addLog(container, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log-${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            document.getElementById(container).appendChild(div);
        }

        // Mock gasData for testing
        if (typeof gasData === 'undefined') {
            window.gasData = [];
        }

        // Mock saveDataToLocalStorage for testing
        if (typeof saveDataToLocalStorage === 'undefined') {
            window.saveDataToLocalStorage = async function() {
                console.log('Mock save operation');
                return new Promise((resolve) => {
                    setTimeout(() => {
                        console.log('Mock save completed');
                        resolve();
                    }, 1000);
                });
            };
        }

        // Mock addRangeInput for testing
        if (typeof addRangeInput === 'undefined') {
            window.addRangeInput = function(type) {
                console.log(`Mock addRangeInput called with type: ${type}`);
            };
        }

        // Initialize the form handler
        document.addEventListener('DOMContentLoaded', () => {
            addLog('testResults', 'Initializing form handler...', 'info');
            
            try {
                if (typeof initializeFormHandler === 'function') {
                    initializeFormHandler();
                    addLog('testResults', '✅ Form handler initialized successfully', 'success');
                } else {
                    addLog('testResults', '❌ initializeFormHandler function not found', 'error');
                }
            } catch (error) {
                addLog('testResults', `❌ Error initializing form handler: ${error.message}`, 'error');
            }

            // Test button
            document.getElementById('testBtn').addEventListener('click', () => {
                addLog('testResults', '🧪 Testing save confirmation feature...', 'info');
                
                // Test authentication status update
                if (typeof updateAuthenticationStatus === 'function') {
                    updateAuthenticationStatus();
                    addLog('testResults', '✅ Authentication status updated', 'success');
                } else {
                    addLog('testResults', '❌ updateAuthenticationStatus function not found', 'error');
                }

                // Test manual save function
                if (typeof confirmManualSave === 'function') {
                    addLog('testResults', '✅ Manual save function is available', 'success');
                } else {
                    addLog('testResults', '❌ confirmManualSave function not found', 'error');
                }

                addLog('testResults', '📝 Test completed. Try using the form buttons above.', 'info');
            });
        });
    </script>
</body>
</html>
