<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase Chart Integration - Alpha Gas Solution</title>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Google+Sans+Display:wght@400;500;600&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="google-material-ui.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="md-container md-py-xl">
        <div class="md-card">
            <div class="md-card-header">
                <h1 class="md-title-large">🧪 Supabase Chart Integration Test</h1>
                <p class="md-text-secondary">Testing complete data flow: Add → Save → Load → Display</p>
            </div>
            
            <div class="md-card-content">
                <!-- Test Steps -->
                <div class="md-grid md-grid-cols-1 md:md-grid-cols-2 md-gap-lg">
                    <!-- Authentication Test -->
                    <div class="md-card md-card-compact">
                        <h3 class="md-title-medium md-mb-md">🔐 Step 1: Authentication</h3>
                        <div id="authStatus" class="md-mb-md">
                            <div class="md-loading-spinner"></div>
                            <span class="md-ml-sm">Checking authentication...</span>
                        </div>
                        <button id="testAuthBtn" class="md-btn md-btn-outlined md-w-full">Test Authentication</button>
                    </div>
                    
                    <!-- Add Test Data -->
                    <div class="md-card md-card-compact">
                        <h3 class="md-title-medium md-mb-md">➕ Step 2: Add Test Data</h3>
                        <div id="addDataStatus" class="md-mb-md">Ready to add test data</div>
                        <button id="addTestDataBtn" class="md-btn md-btn-filled md-w-full">Add Test Analyte</button>
                    </div>
                    
                    <!-- Load Data Test -->
                    <div class="md-card md-card-compact">
                        <h3 class="md-title-medium md-mb-md">📥 Step 3: Load Data</h3>
                        <div id="loadDataStatus" class="md-mb-md">Ready to load data</div>
                        <button id="loadDataBtn" class="md-btn md-btn-outlined md-w-full">Load from Supabase</button>
                    </div>
                    
                    <!-- Chart Display Test -->
                    <div class="md-card md-card-compact">
                        <h3 class="md-title-medium md-mb-md">📊 Step 4: Chart Display</h3>
                        <div id="chartStatus" class="md-mb-md">Ready to render chart</div>
                        <button id="renderChartBtn" class="md-btn md-btn-filled md-w-full">Render Chart</button>
                    </div>
                </div>
                
                <!-- Data Display -->
                <div class="md-card md-mt-lg">
                    <h3 class="md-title-medium md-mb-md">📋 Loaded Data</h3>
                    <pre id="dataDisplay" class="md-p-md" style="background: #f5f5f5; border-radius: 4px; font-size: 12px; max-height: 200px; overflow-y: auto;">No data loaded yet</pre>
                </div>
                
                <!-- Chart Test Display -->
                <div class="md-card md-mt-lg">
                    <h3 class="md-title-medium md-mb-md">📊 Chart Test</h3>
                    <div id="chartTest" class="md-p-md" style="background: #f9f9f9; border-radius: 4px; min-height: 100px;">
                        Chart will be rendered here
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="md-flex md-gap-md md-mt-lg">
                    <button id="runFullTestBtn" class="md-btn md-btn-filled">🚀 Run Complete Test</button>
                    <button id="clearDataBtn" class="md-btn md-btn-outlined">🗑️ Clear Test Data</button>
                    <a href="app-simple.html" class="md-btn md-btn-ghost">📱 Go to Main App</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://xllrffehgebiaisgapdk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsbHJmZmVoZ2ViaWFpc2dhcGRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwODM4MDMsImV4cCI6MjA2NDY1OTgwM30.vsRRz9VDCUSuIybS-a4eyYKDqsDbvsJ-UfgIYu7CRYE';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        let currentUser = null;
        let testData = [];

        // SupabaseStorage implementation (same as in app-simple.html)
        class SupabaseStorage {
            async getUserId() {
                const { data: { user } } = await supabase.auth.getUser();
                return user?.id || null;
            }

            async loadHybridData() {
                try {
                    const userId = await this.getUserId();
                    if (!userId) {
                        throw new Error('User not authenticated');
                    }

                    console.log('Loading hybrid data from Supabase for user:', userId);
                    
                    const { data, error } = await supabase
                        .from('gas_analytes')
                        .select('*')
                        .order('created_at');

                    if (error) {
                        console.error('Error loading hybrid data from Supabase:', error);
                        throw error;
                    }

                    if (!data || data.length === 0) {
                        console.log('No hybrid data found in Supabase');
                        return [];
                    }

                    // Transform Supabase data back to app format
                    const transformedData = data.map(item => ({
                        name: item.name,
                        current: item.current_ranges || [],
                        target: item.target_ranges || [],
                        notes: item.gap_notes || '',
                        timestamp: item.created_at,
                        isCustom: item.is_custom,
                        is_shared: item.is_shared || false,
                        data_type: item.data_type || 'user_target'
                    }));

                    console.log('Hybrid data loaded successfully from Supabase:', transformedData);
                    
                    return transformedData;
                } catch (error) {
                    console.error('Failed to load hybrid data from Supabase:', error);
                    throw error;
                }
            }

            async saveUserTargets(userTargets) {
                try {
                    const userId = await this.getUserId();
                    if (!userId) {
                        throw new Error('User not authenticated');
                    }

                    console.log('Saving user targets to Supabase...', {
                        userId: userId,
                        targetCount: userTargets.length,
                        targets: userTargets.map(t => t.name)
                    });
                    
                    // First, clear existing user targets (not shared data)
                    const { error: deleteError } = await supabase
                        .from('gas_analytes')
                        .delete()
                        .eq('user_id', userId)
                        .eq('is_shared', false);

                    if (deleteError) {
                        console.error('Error clearing existing user targets:', deleteError);
                    } else {
                        console.log('Existing user targets cleared successfully');
                    }

                    // Insert user targets only
                    if (userTargets.length > 0) {
                        const dataToInsert = userTargets.map(analyte => ({
                            user_id: userId,
                            name: analyte.name,
                            current_ranges: analyte.current || [],
                            target_ranges: analyte.target || [],
                            gap_notes: analyte.notes || '',
                            is_custom: analyte.isCustom !== false,
                            is_shared: false,
                            data_type: 'user_target'
                        }));

                        console.log('User targets to insert:', dataToInsert);

                        const { data, error } = await supabase
                            .from('gas_analytes')
                            .insert(dataToInsert);

                        if (error) {
                            console.error('Error saving user targets to Supabase:', error);
                            throw error;
                        }

                        console.log('User targets saved successfully to Supabase');
                        return data;
                    } else {
                        console.log('No user targets to save');
                        return [];
                    }
                } catch (error) {
                    console.error('Failed to save user targets to Supabase:', error);
                    throw error;
                }
            }
        }

        const supabaseStorage = new SupabaseStorage();

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            element.innerHTML = `${icon} ${message}`;
            element.className = `md-status-${type}`;
        }

        // Test authentication
        async function testAuthentication() {
            try {
                updateStatus('authStatus', 'Checking authentication...', 'info');
                
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    throw new Error(`Session error: ${error.message}`);
                }
                
                if (session && session.user) {
                    currentUser = session.user;
                    updateStatus('authStatus', `Authenticated as: ${currentUser.email}`, 'success');
                    return true;
                } else {
                    updateStatus('authStatus', 'Not authenticated. Please log in first.', 'warning');
                    return false;
                }
            } catch (error) {
                updateStatus('authStatus', `Authentication failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Add test data
        async function addTestData() {
            try {
                if (!currentUser) {
                    updateStatus('addDataStatus', 'Please authenticate first', 'warning');
                    return;
                }

                updateStatus('addDataStatus', 'Adding test analyte...', 'info');

                const testAnalyte = {
                    name: `Test Analyte ${Date.now()}`,
                    current: [
                        { min: 10, max: 100, unit: 'ppm' },
                        { min: 200, max: 500, unit: 'ppm' }
                    ],
                    target: [
                        { min: 1, max: 1000, unit: 'ppm' }
                    ],
                    notes: 'Test analyte created by integration test',
                    isCustom: true,
                    is_shared: false,
                    data_type: 'user_target'
                };

                await supabaseStorage.saveUserTargets([testAnalyte]);
                updateStatus('addDataStatus', 'Test analyte added successfully!', 'success');
                
            } catch (error) {
                updateStatus('addDataStatus', `Failed to add test data: ${error.message}`, 'error');
            }
        }

        // Load data from Supabase
        async function loadData() {
            try {
                if (!currentUser) {
                    updateStatus('loadDataStatus', 'Please authenticate first', 'warning');
                    return;
                }

                updateStatus('loadDataStatus', 'Loading data from Supabase...', 'info');

                testData = await supabaseStorage.loadHybridData();
                
                updateStatus('loadDataStatus', `Loaded ${testData.length} analytes`, 'success');
                
                // Display the loaded data
                document.getElementById('dataDisplay').textContent = JSON.stringify(testData, null, 2);
                
            } catch (error) {
                updateStatus('loadDataStatus', `Failed to load data: ${error.message}`, 'error');
                document.getElementById('dataDisplay').textContent = `Error: ${error.message}`;
            }
        }

        // Render chart with loaded data
        function renderChart() {
            try {
                updateStatus('chartStatus', 'Rendering chart...', 'info');
                
                const chartContainer = document.getElementById('chartTest');
                
                if (testData.length === 0) {
                    chartContainer.innerHTML = '<div class="md-text-center md-text-secondary md-p-lg">No data to display</div>';
                    updateStatus('chartStatus', 'No data to render', 'warning');
                    return;
                }

                let chartHTML = '<div class="md-mb-md"><h4>Chart Preview:</h4></div>';
                
                testData.forEach((analyte, index) => {
                    chartHTML += `
                        <div class="md-p-sm md-mb-sm" style="border: 1px solid #ddd; border-radius: 4px;">
                            <div class="md-flex md-items-center md-justify-between">
                                <strong>${analyte.name}</strong>
                                <span class="md-text-xs md-text-secondary">${analyte.data_type}</span>
                            </div>
                            <div class="md-text-sm md-mt-xs">
                                Current ranges: ${analyte.current?.length || 0} | 
                                Target ranges: ${analyte.target?.length || 0}
                            </div>
                            ${analyte.notes ? `<div class="md-text-xs md-text-secondary md-mt-xs">${analyte.notes}</div>` : ''}
                        </div>
                    `;
                });
                
                chartContainer.innerHTML = chartHTML;
                updateStatus('chartStatus', `Chart rendered with ${testData.length} analytes`, 'success');
                
            } catch (error) {
                updateStatus('chartStatus', `Chart rendering failed: ${error.message}`, 'error');
            }
        }

        // Run complete test flow
        async function runCompleteTest() {
            console.log('🚀 Starting complete integration test...');
            
            // Step 1: Test authentication
            const authSuccess = await testAuthentication();
            if (!authSuccess) return;
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 2: Add test data
            await addTestData();
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 3: Load data
            await loadData();
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 4: Render chart
            renderChart();
            
            console.log('✅ Complete integration test finished');
        }

        // Clear test data
        async function clearTestData() {
            try {
                if (!currentUser) {
                    alert('Please authenticate first');
                    return;
                }

                if (confirm('Clear all test data from database?')) {
                    const { error } = await supabase
                        .from('gas_analytes')
                        .delete()
                        .eq('user_id', currentUser.id)
                        .like('name', 'Test Analyte%');

                    if (error) throw error;
                    
                    alert('Test data cleared successfully');
                    testData = [];
                    document.getElementById('dataDisplay').textContent = 'Test data cleared';
                    document.getElementById('chartTest').innerHTML = 'Chart cleared';
                }
            } catch (error) {
                alert(`Failed to clear test data: ${error.message}`);
            }
        }

        // Event listeners
        document.getElementById('testAuthBtn').addEventListener('click', testAuthentication);
        document.getElementById('addTestDataBtn').addEventListener('click', addTestData);
        document.getElementById('loadDataBtn').addEventListener('click', loadData);
        document.getElementById('renderChartBtn').addEventListener('click', renderChart);
        document.getElementById('runFullTestBtn').addEventListener('click', runCompleteTest);
        document.getElementById('clearDataBtn').addEventListener('click', clearTestData);

        // Auto-run authentication test on load
        window.addEventListener('load', testAuthentication);
    </script>
</body>
</html>
