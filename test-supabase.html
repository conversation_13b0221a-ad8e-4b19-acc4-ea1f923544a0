<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Supabase Connection Test</h1>
    <div id="status"></div>
    
    <script type="module">
        import { supabase, supabaseStorage } from './supabase-client.js';
        
        const statusDiv = document.getElementById('status');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            statusDiv.appendChild(div);
        }
        
        async function testSupabase() {
            try {
                addStatus('Testing Supabase connection...', 'info');
                
                // Test basic connection
                const { data, error } = await supabase
                    .from('gas_analytes')
                    .select('count(*)')
                    .limit(1);
                
                if (error) {
                    addStatus(`Connection error: ${error.message}`, 'error');
                    return;
                }
                
                addStatus('✅ Supabase connection successful!', 'success');
                
                // Test storage class
                const connectionTest = await supabaseStorage.testConnection();
                if (connectionTest) {
                    addStatus('✅ Storage class working correctly!', 'success');
                } else {
                    addStatus('❌ Storage class connection failed', 'error');
                }
                
                // Test loading data
                addStatus('Testing data loading...', 'info');
                const loadedData = await supabaseStorage.loadData();
                addStatus(`✅ Data loaded: ${loadedData.length} records`, 'success');
                
            } catch (error) {
                addStatus(`Test failed: ${error.message}`, 'error');
                console.error('Supabase test error:', error);
            }
        }
        
        // Run test when page loads
        testSupabase();
    </script>
</body>
</html>
