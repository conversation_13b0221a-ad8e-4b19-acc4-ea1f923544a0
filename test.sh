#!/usr/bin/env zsh

# Test script for Alpha Gas Solution
echo "🧪 Testing Alpha Gas Solution Application"
echo "========================================"

# Check if server is running
echo "\n1. Checking if server is running..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Server is running on http://localhost:3000"
else
    echo "❌ Server is not running. Please start with 'npm start'"
    exit 1
fi

# Test API endpoint
echo "\n2. Testing API endpoint..."
API_RESPONSE=$(curl -s -X POST http://localhost:3000/api/analyze-calibration \
    -H "Content-Type: application/json" \
    -d '{"prompt":"Simple test"}')

if [[ $API_RESPONSE == *"candidates"* ]]; then
    echo "✅ API endpoint is working correctly"
else
    echo "❌ API endpoint failed"
    echo "Response: $API_RESPONSE"
    exit 1
fi

# Check environment variables
echo "\n3. Checking environment configuration..."
if [[ -f .env ]]; then
    echo "✅ .env file exists"
    if grep -q "GEMINI_API_KEY=" .env; then
        echo "✅ API key is configured"
    else
        echo "❌ API key not found in .env file"
    fi
else
    echo "❌ .env file missing"
fi

# Check security files
echo "\n4. Checking security configuration..."
if [[ -f .gitignore ]]; then
    if grep -q ".env" .gitignore; then
        echo "✅ .env file is properly ignored by git"
    else
        echo "⚠️  .env file is not in .gitignore"
    fi
else
    echo "❌ .gitignore file missing"
fi

echo "\n🎉 Alpha Gas Solution is ready for production!"
echo "📱 Open http://localhost:3000 in your browser to use the application"
echo "🔒 Your API key is secure and not exposed to clients"
echo "\n📖 Next steps:"
echo "   - Test the calibration analysis feature in your browser"
echo "   - Deploy to your preferred hosting platform"
echo "   - Set GEMINI_API_KEY environment variable on your host"
